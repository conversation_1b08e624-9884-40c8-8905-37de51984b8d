Stack trace:
Frame         Function      Args
0007FFFF8B70  00021006116E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFF7A70) msys-2.0.dll+0x2116E
0007FFFF8B70  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFF8B70  0002100469F2 (00021028DF99, 0007FFFF8A28, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFF8B70  00021006A3FE (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A3FE
0007FFFF8B70  00021006A525 (0007FFFF8B80, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A525
0001004F94B7  00021006B985 (0007FFFF8B80, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B985
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFBD27C0000 ntdll.dll
7FFBD1A20000 KERNEL32.DLL
7FFBCF8E0000 KERNELBASE.dll
7FFBD1D60000 USER32.dll
7FFBD0190000 win32u.dll
7FFBD1CB0000 GDI32.dll
7FFBD0280000 gdi32full.dll
7FFBD0050000 msvcp_win.dll
7FFBCFCE0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFBD1B60000 advapi32.dll
7FFBD1F30000 msvcrt.dll
7FFBD0F60000 sechost.dll
7FFBD23F0000 RPCRT4.dll
7FFBCE770000 CRYPTBASE.DLL
7FFBCFFB0000 bcryptPrimitives.dll
7FFBD0920000 IMM32.DLL
