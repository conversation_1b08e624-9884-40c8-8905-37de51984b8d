@font-face {
  font-family: 'fontello';
  src: url('./fontello.ttf');
  src: url('./fontello.ttf') format('embedded-opentype'),
       url('./fontello.woff2') format('woff2'),
       url('./fontello.woff') format('woff'),
       url('./fontello.ttf') format('truetype'),
       url('./fontello.svg') format('svg');
  font-weight: normal;
  font-style: normal;
}
/* Chrome hack: SVG is rendered more smooth in Windozze. 100% magic, uncomment if you need it. */
/* Note, that will break hinting! In other OS-es font will be not as sharp as it could be */
/*
@media screen and (-webkit-min-device-pixel-ratio:0) {
  @font-face {
    font-family: 'fontello';
    src: url('../font/fontello.svg?57534690#fontello') format('svg');
  }
}
*/
[class^="pw-icon-"]:before, [class*=" pw-icon-"]:before {
  font-family: "fontello";
  font-style: normal;
  font-weight: normal;
  speak: never;

  display: inline-block;
  text-decoration: inherit;
  width: 1em;
  margin-right: .2em;
  text-align: center;
  /* opacity: .8; */

  /* For safety - reset parent styles, that can break glyph codes*/
  font-variant: normal;
  text-transform: none;

  /* fix buttons height, for twitter bootstrap */
  line-height: 1em;

  /* Animation center compensation - margins should be symmetric */
  /* remove if not needed */
  margin-left: .2em;

  /* you can be more comfortable with increased icons size */
  /* font-size: 120%; */

  /* Font smoothing. That was taken from TWBS */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  /* Uncomment for 3D effect */
  /* text-shadow: 1px 1px 1px rgba(127, 127, 127, 0.3); */
}

.pw-icon-menu:before { content: '\e800'; } /* '' */
.pw-icon-share:before { content: '\e801'; } /* '' */
.pw-icon-search:before { content: '\e802'; } /* '' */
.pw-icon-calendar:before { content: '\e803'; } /* '' */
.pw-icon-clock:before { content: '\e804'; } /* '' */
.pw-icon-medium:before { content: '\e805'; } /* '' */
.pw-icon-comment:before { content: '\e806'; } /* '' */
.pw-icon-user:before { content: '\e807'; } /* '' */
.pw-icon-pocket:before { content: '\e808'; } /* '' */
.pw-icon-doc-text:before { content: '\e809'; } /* '' */
.pw-icon-down-outline:before { content: '\e80a'; } /* '' */
.pw-icon-down-open-big:before { content: '\e80b'; } /* '' */
.pw-icon-angle-down:before { content: '\e80c'; } /* '' */
.pw-icon-circle-empty:before { content: '\e80d'; } /* '' */
.pw-icon-dot-circled:before { content: '\e80e'; } /* '' */
.pw-icon-check:before { content: '\e80f'; } /* '' */
.pw-icon-check-empty:before { content: '\e810'; } /* '' */
.pw-icon-export:before { content: '\e811'; } /* '' */
.pw-icon-pencil-alt:before { content: '\e812'; } /* '' */
.pw-icon-chat-1:before { content: '\e813'; } /* '' */
.pw-icon-mic:before { content: '\e814'; } /* '' */
.pw-icon-video-alt:before { content: '\e815'; } /* '' */
.pw-icon-videocam-outline:before { content: '\e816'; } /* '' */
.pw-icon-pinterest:before { content: '\e817'; } /* '' */
.pw-icon-gmail:before { content: '\e818'; } /* '' */
.pw-icon-quote-left:before { content: '\e819'; } /* '' */
.pw-icon-quote-right:before { content: '\e81a'; } /* '' */
.pw-icon-link:before { content: '\e81b'; } /* '' */
.pw-icon-location:before { content: '\e81c'; } /* '' */
.pw-icon-pin:before { content: '\e81d'; } /* '' */
.pw-icon-attach:before { content: '\e81e'; } /* '' */
.pw-icon-play-1:before { content: '\e81f'; } /* '' */
.pw-icon-eye:before { content: '\e820'; } /* '' */
.pw-icon-heart:before { content: '\e821'; } /* '' */
.pw-icon-heart-filled:before { content: '\e822'; } /* '' */
.pw-icon-plus:before { content: '\e823'; } /* '' */
.pw-icon-download-cloud:before { content: '\e824'; } /* '' */
.pw-icon-facebook:before { content: '\e825'; } /* '' */
.pw-icon-twitter:before { content: '\e826'; } /* '' */
.pw-icon-linkedin-squared:before { content: '\e827'; } /* '' */
.pw-icon-gplus:before { content: '\e828'; } /* '' */
.pw-icon-pinterest-circled:before { content: '\e829'; } /* '' */
.pw-icon-github-circled:before { content: '\e82a'; } /* '' */
.pw-icon-behance:before { content: '\e82b'; } /* '' */
.pw-icon-dribbble:before { content: '\e82c'; } /* '' */
.pw-icon-instagram:before { content: '\e82d'; } /* '' */
.pw-icon-lastfm:before { content: '\e82e'; } /* '' */
.pw-icon-rss:before { content: '\e82f'; } /* '' */
.pw-icon-vimeo-squared:before { content: '\e830'; } /* '' */
.pw-icon-forrst:before { content: '\e831'; } /* '' */
.pw-icon-skype:before { content: '\e832'; } /* '' */
.pw-icon-picasa:before { content: '\e833'; } /* '' */
.pw-icon-youtube-play:before { content: '\e834'; } /* '' */
.pw-icon-flickr:before { content: '\e835'; } /* '' */
.pw-icon-tumblr:before { content: '\e836'; } /* '' */
.pw-icon-blogger:before { content: '\e837'; } /* '' */
.pw-icon-delicious:before { content: '\e838'; } /* '' */
.pw-icon-digg:before { content: '\e839'; } /* '' */
.pw-icon-friendfeed:before { content: '\e83a'; } /* '' */
.pw-icon-wordpress:before { content: '\e83b'; } /* '' */
.pw-icon-stackoverflow:before { content: '\e83c'; } /* '' */
.pw-icon-foursquare:before { content: '\e83d'; } /* '' */
.pw-icon-xing:before { content: '\e83e'; } /* '' */
.pw-icon-sina-weibo:before { content: '\e83f'; } /* '' */
.pw-icon-soundcloud:before { content: '\e840'; } /* '' */
.pw-icon-fivehundredpx:before { content: '\e841'; } /* '' */
.pw-icon-slideshare:before { content: '\e842'; } /* '' */
.pw-icon-android:before { content: '\e843'; } /* '' */
.pw-icon-apple:before { content: '\e844'; } /* '' */
.pw-icon-windows:before { content: '\e845'; } /* '' */
.pw-icon-vkontakte:before { content: '\e846'; } /* '' */
.pw-icon-myspace:before { content: '\e847'; } /* '' */
.pw-icon-meetup:before { content: '\e848'; } /* '' */
.pw-icon-cart:before { content: '\e849'; } /* '' */
.pw-icon-reddit:before { content: '\e84a'; } /* '' */
.pw-icon-stumbleupon-circled:before { content: '\e84b'; } /* '' */
.pw-icon-path:before { content: '\e84c'; } /* '' */
.pw-icon-dropbox:before { content: '\e84d'; } /* '' */
.pw-icon-paper-plane:before { content: '\e84e'; } /* '' */
.pw-icon-paper-plane-1:before { content: '\e84f'; } /* '' */
.pw-icon-goodreads:before { content: '\e850'; } /* '' */
.pw-icon-grooveshark:before { content: '\e851'; } /* '' */
.pw-icon-comment-1:before { content: '\e852'; } /* '' */
.pw-icon-steam:before { content: '\e853'; } /* '' */
.pw-icon-angellist:before { content: '\e855'; } /* '' */
.pw-icon-icq:before { content: '\e856'; } /* '' */
.pw-icon-edit:before { content: '\e857'; } /* '' */
.pw-icon-home-1:before { content: '\e858'; } /* '' */
.pw-icon-doc-alt:before { content: '\e859'; } /* '' */
.pw-icon-attach-1:before { content: '\e85a'; } /* '' */
.pw-icon-thumbs-up:before { content: '\e85b'; } /* '' */
.pw-icon-info-circled-alt:before { content: '\e85c'; } /* '' */
.pw-icon-warning-empty:before { content: '\e85d'; } /* '' */
.pw-icon-plus-squared-alt:before { content: '\e85e'; } /* '' */
.pw-icon-minus-squared-alt:before { content: '\e85f'; } /* '' */
.pw-icon-left-small:before { content: '\e860'; } /* '' */
.pw-icon-bookmark-empty:before { content: '\e861'; } /* '' */
.pw-icon-bookmark-empty-1:before { content: '\e862'; } /* '' */
.pw-icon-vine:before { content: '\e863'; } /* '' */
.pw-icon-reply-outline:before { content: '\e864'; } /* '' */
.pw-icon-network:before { content: '\e865'; } /* '' */
.pw-icon-tag-empty:before { content: '\e866'; } /* '' */
.pw-icon-tag:before { content: '\e867'; } /* '' */
.pw-icon-mail:before { content: '\e868'; } /* '' */
.pw-icon-mic-outline:before { content: '\e869'; } /* '' */
.pw-icon-camera-outline:before { content: '\e86a'; } /* '' */
.pw-icon-tiktok:before { content: '\e86b'; } /* '' */
.pw-icon-feather:before { content: '\e86c'; } /* '' */
.pw-icon-left-open-big:before { content: '\e86d'; } /* '' */
.pw-icon-right-open-big:before { content: '\e86e'; } /* '' */
.pw-icon-up-open-big:before { content: '\e86f'; } /* '' */
.pw-icon-picture-1:before { content: '\e870'; } /* '' */
.pw-icon-videocam:before { content: '\e871'; } /* '' */
.pw-icon-link-ext:before { content: '\e872'; } /* '' */
.pw-icon-level-up:before { content: '\e873'; } /* '' */
.pw-icon-back-in-time:before { content: '\e874'; } /* '' */
.pw-icon-stopwatch:before { content: '\e875'; } /* '' */
.pw-icon-star-empty:before { content: '\e876'; } /* '' */
.pw-icon-star:before { content: '\e877'; } /* '' */
.pw-icon-down-circled:before { content: '\e878'; } /* '' */
.pw-icon-file-pdf:before { content: '\e879'; } /* '' */
.pw-icon-cw:before { content: '\e87a'; } /* '' */
.pw-icon-ccw:before { content: '\e87b'; } /* '' */
.pw-icon-briefcase:before { content: '\e87c'; } /* '' */
.pw-icon-graduation-cap:before { content: '\e87d'; } /* '' */
.pw-icon-activity:before { content: '\e87e'; } /* '' */
.pw-icon-heart-empty:before { content: '\e87f'; } /* '' */
.pw-icon-heart-1:before { content: '\e880'; } /* '' */
.pw-icon-keyboard:before { content: '\e881'; } /* '' */
.pw-icon-phone:before { content: '\e882'; } /* '' */
.pw-icon-lightbulb-1:before { content: '\e883'; } /* '' */
.pw-icon-vcard-1:before { content: '\e884'; } /* '' */
.pw-icon-music-outline:before { content: '\e885'; } /* '' */
.pw-icon-cancel-circled-outline:before { content: '\e886'; } /* '' */
.pw-icon-info-outline:before { content: '\e887'; } /* '' */
.pw-icon-location-outline:before { content: '\e888'; } /* '' */
.pw-icon-globe-alt-outline:before { content: '\e889'; } /* '' */
.pw-icon-brush:before { content: '\e88a'; } /* '' */
.pw-icon-coffee:before { content: '\e88b'; } /* '' */
.pw-icon-book-open:before { content: '\e88c'; } /* '' */
.pw-icon-video-1:before { content: '\e88d'; } /* '' */
.pw-icon-key:before { content: '\e88e'; } /* '' */
.pw-icon-graduation-cap-1:before { content: '\e88f'; } /* '' */
.pw-icon-camera:before { content: '\e890'; } /* '' */
.pw-icon-unsplash:before { content: '\e891'; } /* '' */
.pw-icon-beaker:before { content: '\e892'; } /* '' */
.pw-icon-beaker-1:before { content: '\e893'; } /* '' */
.pw-icon-smile:before { content: '\e894'; } /* '' */
.pw-icon-frown:before { content: '\e895'; } /* '' */
.pw-icon-music:before { content: '\e896'; } /* '' */
.pw-icon-photo:before { content: '\e897'; } /* '' */
.pw-icon-cancel:before { content: '\e898'; } /* '' */
.pw-icon-cancel-outline:before { content: '\e899'; } /* '' */
.pw-icon-camera-2:before { content: '\e89a'; } /* '' */
.pw-icon-picture-2:before { content: '\e89b'; } /* '' */
.pw-icon-file-image:before { content: '\e89c'; } /* '' */
.pw-icon-music-1:before { content: '\e89d'; } /* '' */
.pw-icon-minus:before { content: '\e89e'; } /* '' */
.pw-icon-plus-1:before { content: '\e89f'; } /* '' */
.pw-icon-minus-1:before { content: '\e8a0'; } /* '' */
.pw-icon-download-alt:before { content: '\e8a1'; } /* '' */
.pw-icon-camera-alt:before { content: '\e8a4'; } /* '' */
.pw-icon-moon:before { content: '\e8a6'; } /* '' */
.pw-icon-home-outline:before { content: '\e8a7'; } /* '' */
.pw-icon-phone-outline:before { content: '\e8a8'; } /* '' */
.pw-icon-sun:before { content: '\e8a9'; } /* '' */
.pw-icon-plane-outline:before { content: '\e8aa'; } /* '' */
.pw-icon-tree:before { content: '\e8ab'; } /* '' */
.pw-icon-location-inv:before { content: '\e8ac'; } /* '' */
.pw-icon-smiley:before { content: '\e8ad'; } /* '' */
.pw-icon-person:before { content: '\e8ae'; } /* '' */
.pw-icon-adult:before { content: '\e8af'; } /* '' */
.pw-icon-child:before { content: '\e8b0'; } /* '' */
.pw-icon-location-1:before { content: '\e8b1'; } /* '' */
.pw-icon-heart-2:before { content: '\e8b2'; } /* '' */
.pw-icon-cup:before { content: '\e8b4'; } /* '' */
.pw-icon-doc:before { content: '\e8b5'; } /* '' */
.pw-icon-cd:before { content: '\e8b6'; } /* '' */
.pw-icon-food:before { content: '\e8b7'; } /* '' */
.pw-icon-paypal:before { content: '\e8b9'; } /* '' */
.pw-icon-spotify-1:before { content: '\e8ba'; } /* '' */
.pw-icon-left-outline:before { content: '\e8bb'; } /* '' */
.pw-icon-left:before { content: '\e8bc'; } /* '' */
.pw-icon-right:before { content: '\e8bd'; } /* '' */
.pw-icon-right-small:before { content: '\e8be'; } /* '' */
.pw-icon-right-outline:before { content: '\e8bf'; } /* '' */
.pw-icon-shopping-bag:before { content: '\e8c0'; } /* '' */
.pw-icon-cancel-line:before { content: '\e8c1'; } /* '' */
.pw-icon-calendar-line:before { content: '\e8c2'; } /* '' */
.pw-icon-check-line:before { content: '\e8c3'; } /* '' */
.pw-icon-lock:before { content: '\e8c5'; } /* '' */
.pw-icon-lock-1:before { content: '\e8c6'; } /* '' */
.pw-icon-bloglovin-alt:before { content: '\e8c7'; } /* '' */
.pw-icon-bloglovin:before { content: '\e8c8'; } /* '' */
.pw-icon-whatsapp:before { content: '\e8c9'; } /* '' */
.pw-icon-image-alt:before { content: '\e8ca'; } /* '' */
.pw-icon-deviantart:before { content: '\f1bd'; } /* '' */
.pw-icon-twitch:before { content: '\f1e8'; } /* '' */
.pw-icon-yelp:before { content: '\f1e9'; } /* '' */
.pw-icon-snapchat:before { content: '\f2ac'; } /* '' */
.pw-icon-quora:before { content: '\f2c4'; } /* '' */
