!function(){"use strict";var e=function(e,n){var o=e.find(".bdt-prime-slider").find(".bdt-scroll-down"),r=o.data("selector"),t=o.data("settings");o.length&&n(o).on("click",(function(e){e.preventDefault(),bdtUIkit.scroll(o,t).scrollTo(n(r))}))},n=function(e,n){var o=e.data("id"),r=e.find("[data-reveal-enable]").data("reveal-enable");if(void 0===r||"yes"!==r)return;const t=n(".reveal-active-"+o).find('[data-reveal="reveal-active"]');n(t).css({opacity:"1"});const d=e.find("[data-reveal-settings]").data("reveal-settings");let l=0;n(t).each((function(e,n){l+=80;const o=new RevealFx(n,{revealSettings:{bgColors:[d.bgColors],direction:String(d.direction),duration:Number(d.duration+l),easing:String(d.easing),onHalfway:function(e,n){e.style.opacity=1}}});!function(e,n){var o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};o.rootMargin=o.rootMargin||"10% 0px 0px 0px",new IntersectionObserver((function(e,r){e.forEach((function(e){e.isIntersecting&&(n(e),o.loop||r.unobserve(e.target))}))}),o).observe(e)}(n,(function(){o.reveal()}),{root:null,rootMargin:"0px",threshold:.8})})),setTimeout((()=>{const e=n(".reveal-active-"+o);var r=n(e).find(".reveal-muted");n(r).each((function(e,o){n(o).addClass("reveal-loaded"),n(o).removeClass("reveal-muted")}))}),1.3*(d.duration+l))};jQuery(window).on("elementor/frontend/init",(function(){elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-general.default",n),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-general.slide",n),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-general.crelly",n),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-general.meteor",n),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-blog.default",n),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-blog.coral",n),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-blog.folio",n),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-blog.zinest",n),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-isolate.default",n),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-isolate.locate",n),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-isolate.slice",n),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-dragon.default",n),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-flogia.default",n),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-mount.default",n),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-elysium.default",n),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-fiestar.default",n),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-sequester.default",n),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-mercury.default",n),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-pacific.default",n),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-paranoia.default",n),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-rubix.default",n),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-storker.default",n),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-tango.default",n),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-vertex.default",n),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-woocommerce.default",n),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-woolamp.default",n),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-astoria.default",n),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-avatar.default",n),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-flexure.default",n),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-fluent.default",n),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-fortune.default",n),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-knily.default",n),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-monster.default",n),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-general.default",e),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-general.meteor",e),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-blog.default",e),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-blog.coral",e),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-isolate.default",e),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-isolate.locate",e),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-woocommerce.default",e),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-fluent.default",e),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-astoria.default",e)}))}(jQuery,window.elementorFrontend);