/* --------------------------------------------
	
	main.css : this is the main style file of the theme.
	
	Author 		: pixelwars 
	Url			: pixelwars.org
	
	---
	
	CONTEXT:
	
	1. BASE (@base)
		1.1 ELEMENTS 
		1.2 FONTS
		1.3 TYPOGRAPHY
		1.4 BUTTONS
	2. FORMS (@forms)
	3. COMMON (@common)
	4. LAYOUT (@layout)
	5. MODULES (pages) (@modules)
	6. BLOG (@blog)
	7. SHOP (@shop)
	8. PORTFOLIO (@portfolio)
	
 -------------------------------------------- */
   


/*  --------------------------------------------

	1. BASE (@base) - base html elements
	
    -------------------------------------------- */

/* ----- 1.1 ELEMENTS ----- */   
html {
    font-size: 13px; line-height: 1.9; box-sizing: border-box; overflow-x: hidden; -webkit-text-size-adjust: 100%; -webkit-tap-highlight-color: rgba(0,0,0,0);-webkit-tap-highlight-color: transparent;
    }
*,
*:before, 
*:after {
    box-sizing: inherit; -webkit-font-smoothing: antialiased;
    }
::selection {
 	text-shadow: none; color: #000; background: #FFF9CD;
	}
::-moz-selection {
 	text-shadow: none; color: #000; background: #FFF9CD;
	}
body {
	color: #444; background: #fff; overflow-x: hidden;
	}
a {
	color: #D2AB74; text-decoration: none; outline: none;
	}
a:hover,
a:focus {
	color: #C9B69B;	text-decoration: none; outline: none;
	}
/* HEADINGS TEXT COLOR */
h1,
h2,
h3,
h4,
h5,
h6,
blockquote,
.tab-titles {
	color: #222;
	}
figure {
	margin: 16px 0 32px 0;
	}
iframe {
 	border: none; width: 100%; max-width: 100%;
	}
img {
 	display: block; max-width: 100%; height: auto;
	}
audio,
canvas,
iframe,
img,
svg,
video {
    vertical-align: middle;
	}
audio {
	width: 100%;
	}

/* ----- 1.2 FONTS ----- */ 
/* BODY */
body {
 	font-family: -apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif;
	}
/* SUB HEADINGS */
h2,
h3,
h4,
h5,
h6,
blockquote,
.tab-titles {
	font-family: -apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif;
	}
/* HEADINGS */
h1,
.entry-title,
.footer-subscribe h3,
.widget_categories ul li,
.widget_recent_entries ul li a,
.widget_pages ul li,
.widget_nav_menu ul li,
.widget_archive ul li,
.widget_most_recommended_posts ul li a,
.widget_calendar table caption,
.tptn_title,
.nav-single a,
.widget_recent_comments ul li,
.widget_product_categories ul li,
.widget_meta ul li,
.widget_rss ul a.rsswidget {
	font-family: -apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif;
	}
/* MENU */
.nav-menu,
.entry-meta,
.owl-nav,
label,
.page-links,
.navigation,
.entry-title i,
.site-info,
.filters {
	font-family: -apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif;
	}
/* SITE TITLE */
.site-title {
	font-family: -apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif;
	}
/* SITE DESCRIPTION */
.site-description {
	font-family: -apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif;
	}
/* WIDGET TITLE TITLE */
.widget-title {
	font-family: -apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif;
	}
/* TOP BAR */
.top-bar {
	font-family: -apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif;
	}
/* FONTELLO ICONS */
.hentry .entry-header:before,
.submenu-toggle:before,
.shopping-cart:before,
.toggle h4:before,
.selector:before,
.checker span:before,
.radio span:before,
.uploader span.action:before,
.is-meta-with-icons .entry-meta > span:before,
.entry-share:before,
.entry-share a:before,
.button:before,
.button:after,
.woocommerce-thankyou-order-received:before,
.portfolio-grid .featured-image > a:first-of-type:after,
.post-password-form:before,
.cart_totals:before,
.edit-link:before,
.entry-reading-time:before,
.entry-like a:before {
    font-family:fontello; font-style:normal; font-weight:400; speak:none; display:inline-block; text-decoration:inherit; text-align:center; font-variant:normal; text-transform:none;
    }

/* ----- 1.3 TYPOGRAPHY ----- */
/* headings and paragraphs  */
h1,
h2,
h3,
h4,
h5,
h6 { 
	line-height: 1.4; font-weight: 700; margin: 1.2em 0 0.5em 0;
    }
h2,
h3,
h4,
h5,
h6 { 
	font-weight: 700; 
    }
h1:first-child,
h2:first-child,
h3:first-child,
h4:first-child,
h5:first-child,
h6:first-child { 
    margin-top: 0; 
    }
h1 {
 	font-size: 2em; margin: 0.8em 0 0.8em 0; line-height: 1.2;
	}
h2 {
 	font-size: 1.5em; line-height: 1.4;
	}
h3 {
 	font-size: 1.2em;
	}
h4 {
 	font-size: 1.0em;
	}
h5 {
 	font-size: 1.0em;
	}
h6 {
 	font-size: 1.0em;
	}
p {
 	margin: 0 0 1.1em 0;
	}
.elementor-text-editor p:last-child {
    margin-bottom: 0;
	}
small small {
    font-size: 95%;
	}
/* blockquote  */
blockquote {
 	position: relative; margin: 2.4em auto; padding: .4em 0; font-size: 1.6em; font-weight: bold; line-height: 1.4;
	}
	blockquote p {
		display: inline;
		}
	blockquote cite {
		display: block; font-size: 11px; opacity: .6; padding-right: 1em; margin-top: 1.2em; font-style: normal; font-weight: 400; text-transform: uppercase; letter-spacing: .04em;
		}
		blockquote:before {
			content: ''; position: absolute; top: -20px; left: 0; width: 13rem; border-bottom: 1px solid; opacity: 0.12; 
		  	}
		blockquote:after {
			content: ''; position: absolute; bottom: -16px; left: 0; width: 13rem; border-bottom: 1px solid; opacity: 0.12;
		  	}
		.wp-block-pullquote:not(.is-style-solid-color) blockquote:before,
		.wp-block-pullquote:not(.is-style-solid-color) blockquote:after {
			left: 50%; transform: translate(-50%, -50%);
		  	}
		blockquote.has-text-align-right:before,
		blockquote.has-text-align-right:after {
			left: auto; right: 0;
		  	}
		.wp-block-quote.is-large, .wp-block-quote.is-style-large {
			padding: .4em 0; margin: 2.4em auto;
			}
		.wp-block-pullquote.is-style-solid-color blockquote cite {
			text-transform: uppercase;
			}
hr {
 	padding: 0; margin: 2em 0; outline: 0; border-width: 0 0 1px 0; border-style: solid; border-color: rgba(0, 0, 0, 0.07);
	}
/* tables  */
table,
th,
td,
.wp-block-calendar tbody td, .wp-block-calendar th {
	border: 1px solid;
	}
table {
	margin: 1.4em 0 3em 0; border-collapse: separate; border-spacing: 0; border-width: 1px 0 0 1px; width: 100%;
	}
th,
td {
	font-weight: 400; text-align: left;
	}
caption{
	font-weight: 700; text-align: left;
	}
th {
	border-width: 0 1px 1px 0; font-weight: 700; text-transform: uppercase;
	}
td {
	border-width: 0 1px 1px 0;
	}
th, td {
	padding: 0.5em 1em;
	}
thead th {
	padding: .8em 1em; font-size: 1rem;
	}
/* code and pre  */
code,
pre {
 	margin: 2em 0 4em; max-width: 100%; font-family: Monaco, Menlo, Consolas, "Courier New", monospace; color: inherit; white-space: pre; overflow: auto; padding: 0px 6px; font-size: .875rem; border: 1px solid #0000002e;
	}
code {
 	display: inline-block; margin: 0 4px -6px;
	}
pre {
	padding: 12px;
	}
pre code {
    background: transparent; box-shadow: none; border: 0;
	}
/* lists  */
ol,
ul {
 	margin: .2em 0 1em 0; padding: 0 0 0 1.6em;
	}
ol ol, ul ul {
    margin: 8px 0;
	}
ul {
 	list-style: disc;
	}
dt {
 	margin-bottom: .8em; display: inline-block; font-weight: bold; letter-spacing: 1px; text-transform: uppercase;
	}
dd {
	padding-bottom: 1.5em; margin-left: 2em;
	}
address {
	margin-bottom: 1.2em; 
	}
	

/* CONTENT ALIGNMENT STYLE */
.aligncenter {
    display: block;
	}
.aligncenter, 
.alignnone, 
.alignright, 
.alignleft, 
.alignwide {
    margin: 1.6em auto; clear: both;
	}
.aligncenter:first-child, 
.alignnone:first-child {
	margin-top: 0;
	}
.aligncenter:last-child, 
.alignnone:last-child {
	margin-bottom: 0;
	}
.wp-caption {
	margin-bottom: 1.6em; max-width: 100%;
	}
.wp-caption img[class*="wp-image-"] {
	display: block; margin: 0;
	}
.wp-caption-text {
	font-size: .9rem; opacity: .7; padding: 0.8em 0 0;
	}
.aligncenter .wp-caption-text {
	text-align: center;
	}
/* GUTENBERG STYLES */
.wp-block-embed:first-child {
	margin-top: 0;
	}
.wp-block-image figcaption {
	font-size: 90%; margin-bottom: 0;
	}
/* Buttons */
.wp-block-button {
    margin-top: 1.4em; margin-bottom: 1.4em;
	}
.wp-block-button__link:hover {
	background: #131517;
	}
.wp-block-button__link {
	transition: .3s all;
	}
.wp-block-button.alignleft,
.wp-block-button.alignright {
    margin-top: 0; margin-bottom: 1.4em;
	}
.wp-block-button.is-style-squared a {
    border-radius: 0;
	}
/* Covers */
.wp-block-cover:not([class*=background-color]) .wp-block-cover__inner-container, .wp-block-cover:not([class*=background-color]) .wp-block-cover-image-text, .wp-block-cover:not([class*=background-color]) .wp-block-cover-text, .wp-block-cover-image:not([class*=background-color]) .wp-block-cover__inner-container, .wp-block-cover-image:not([class*=background-color]) .wp-block-cover-image-text, .wp-block-cover-image:not([class*=background-color]) .wp-block-cover-text {
    color: #fff;
	}
/* Blockquote */
.wp-block-quote.is-large p, .wp-block-quote.is-style-large p {
    font-style: normal; font-size: 1.24em; line-height: 1.3;
    }
.wp-block-pullquote p,
.wp-block-pullquote.is-style-solid-color blockquote p {
    font-size: 1.4em; line-height: 1.2;
    }
.wp-block-quote.is-large cite, .wp-block-quote.is-large footer, .wp-block-quote.is-style-large cite, .wp-block-quote.is-style-large footer {
    font-size: .8rem;
	}
/* Blockquote in a column */
.wp-block-column + .wp-block-column .wp-block-quote {
    font-size: 20px;
	}
/* Tables */
.wp-block-calendar {
    margin: 2em 0;
    }
.wp-block-calendar table th {
    text-align: center; font-weight: 700; font-size: 1rem; background: transparent;
    }
.wp-block-calendar table {
    font-size: 1rem;
    }
.wp-calendar-nav {
	text-align: left; margin-top: 8px; font-size: 14px; text-transform: uppercase; font-weight: 700;
    }
.wp-calendar-nav a {
	color: inherit; font-style: normal;
    }
.wp-calendar-nav .wp-calendar-nav-next {
	float: right;
    }
.wp-block-calendar {
    margin: 2em 0;
	}
/* categories dropdown */
.wp-block-categories-dropdown.wp-block-categories {
	margin: 1.4em 0;
	}
/* comments */
.wp-block-latest-comments {
	padding-left: 0;
    }
.wp-block-latest-comments .wp-block-latest-comments__comment {
	margin: 20px 0; padding: 1.8rem; border: 1px solid #0000001a;
	}
.wp-block-latest-comments .wp-block-latest-comments__comment:first-child {
	margin-top: 0;
    }
.wp-block-latest-comments .wp-block-latest-comments__comment:last-child {
	margin-bottom: 0;
    }
.wp-block-latest-comments .wp-block-latest-comments__comment-date {
	text-transform: uppercase; font-size: 11px; letter-spacing: 1px; 
    }
.wp-block-latest-comments .wp-block-latest-comments__comment-excerpt p {
	font-size: 14px; margin: 10px 0 0 0;
    }
a.wp-block-latest-comments__comment-author {
    color: inherit; font-weight: bold; font-size: 14px;
    }
a.wp-block-latest-comments__comment-link {
    color: inherit; text-transform: uppercase; font-size: 13px; text-decoration: underline;
	}
/* latest posts */
ul.wp-block-latest-posts__list.has-dates.wp-block-latest-posts {
    padding-left: 0;
    }
.wp-block-latest-posts:not(.is-grid) > li {
    margin-bottom: 12px;
    }
.wp-block-latest-posts > li a {
    font-size: 1.5rem; line-height: 1.1;
	}
/* tag cloud */
.wp-block-tag-cloud a {
    color: inherit;	
	}
/* layout elements */
.wp-block-group.has-background {
    padding: 2rem; margin-bottom: 2rem;
	}
.has-large-font-size {
    font-size: 2rem; line-height: 1.4;
	}
/* preformated */
pre.wp-block-preformatted,
pre.wp-block-verse {
    background: transparent; border: 0; padding: 0; box-shadow: none;
	}
pre.wp-block-verse {
	font-family: inherit; font-size: 1.0rem;
	}
/* striped tables */
.wp-block-table table {
    margin: 1em 0;
    }
figure.wp-block-table.is-style-stripes {
    border-bottom: 1px solid #0000001a;
    }
.wp-block-table.is-style-stripes table {
    border: 0;
    }
.wp-block-table.is-style-stripes tbody tr:nth-child(odd) {
    background: #00000012;
    }
	
/* TABLETS */
@media screen and (min-width: 768px) {
	.alignnone {
		margin: 2em 0; 
		}
	.aligncenter {
		margin: 2em auto; 
		}
	.alignright,
	.wp-block-image .alignright {
		float:right; margin: 8px 0 32px 38px;
		}
	.alignleft,
	.wp-block-image .alignleft {
		float: left; margin: 8px 38px 32px 0;
		}
	.alignright,
	.alignleft {
		max-width: 50%;
		}
	.alignright .wp-caption-text,
	.alignleft .wp-caption-text {
		max-width: 230px;
		}
}
/* DESKTOPS MEDIUM */
@media screen and (min-width: 1400px) { 
	.is-post-media-overflow .layout-fixed .aligncenter:not(.full):not(.no-strech),
	.is-post-media-overflow .layout-fixed .media-wrap,
	.is-post-media-overflow .layout-fixed .owl-carousel,
	.is-post-media-overflow .layout-fixed .gallery,
	body:not(.woocommerce-page) .content-area:not(.with-sidebar) .alignwide {
		max-width: 166%; width: 166% !important; margin-left: -33%;
		}
	
	/* fix for small images are being streched when post media overflow is activated */
	.is-post-media-overflow .layout-fixed .wp-caption.aligncenter:not(.full):not(.no-strech) img {
		width: 100%;
		}
	.is-post-media-overflow .layout-fixed .wp-caption.aligncenter:not(.full) img.no-strech {
		width: auto; max-width: 100%; margin-left: auto; margin-right: auto;
		}
	
	.is-post-media-overflow .layout-fixed .alignright,
	.is-post-media-overflow .layout-fixed .alignleft {
		max-width: 66%;
		}
	.is-post-media-overflow .layout-fixed .alignright {
		margin-right: -33%;
		}
	.is-post-media-overflow .layout-fixed .alignleft {
		margin-left: -33%;
		}	
}


/* ----- 1.4 BUTTONS ----- */
/* ------------------------------------------ */
/* fix for style jump with js added classes */
.site-content form input[type=submit],
.site-content form input[type=button], 
.site-content form button {
	visibility: hidden;
	}
html.is-ready .site-content form input[type=submit],
html.is-ready .site-content form input[type=button], 
html.is-ready .site-content form button {
	visibility: visible;
	}

/* BUTTONS */
.button {
	 display: inline-block; position: relative; padding: 14px 26px; margin-bottom: .25em; outline: 0; border: 2px solid currentColor; color: inherit; text-shadow: none; background: none; border-radius: 0; cursor: pointer;
	}
.button, 
button, 
html .elementor-button, 
html .ekit-wid-con .elementskit-btn, 
html .ekit-wid-con .ekit_creative_button, 
.more-link {
	font-size: 12px; font-weight: 500; letter-spacing: 1px; text-transform: uppercase; border-radius: 0;
	}
html .ekit-wid-con .ekit_creative_button {
	padding: 20px 40px;
	}
.button:hover {
	color: inherit;
	}
.button:active {
	top:2px; left:2px;
	}
/* Large Buttons */
.button.big {
 	font-size: 13px; padding: 24px 36px;
	}
/* Huge Buttons */
.button.huge {
	font-size: 13px; padding: 32px 46px;
	}
/* Small Buttons */
.button.small {
 	padding: 7px 14px; font-size: 11px;
	}
/* Small Buttons */
.button.mini {
		padding: 5px 10px; font-size: 10px;
	}

/* Buttons with icons */
.button i {
	display: inline; font-size: 140%; margin-right: .4em; margin-left: -0.2em; vertical-align: middle;
   }
.button + .button {
   margin-left: 1.4em;
   }

/* Buttons Lowercase */
.is-buttons-lowercase .button,
.is-buttons-lowercase .elementor-button,
.is-buttons-lowercase .elementskit-btn,
.is-buttons-lowercase .ekit-wid-con .ekit_creative_button,
.is-buttons-lowercase .more-link {
	text-transform: none;
	}

/* BUTTON STYLE : SOLID */
.button.is-solid {
	background: #111; color: #fff !important; border-color: transparent;
	}

/* BUTTON STYLE : SOLID LIGHT */
.button.is-solid-light {
	background: #eadcdc; color: inherit !important; border-color: transparent;
	}

/* BUTTON STYLE : UNDERLINE */
.button.is-underline {
	background: none !important; border: 0; padding: 1px 0; border-bottom: 2px solid currentColor; border-radius: 0 !important;
	}

/* BUTTON STYLE : NAKED */
.button.is-naked {
	background: none !important; border: 0; padding: 3px 0; border-radius: 0 !important;
	}
.button.is-naked:after {
    content: ''; position: absolute; width: 100%; height: 2px; bottom: 0; left: 0; background-color: currentColor; transform: scaleX(0); transform-origin: bottom right; transition: transform 0.3s;
	}
.button.is-naked:hover:after {
	transform-origin: bottom left; transform: scaleX(1);
	}

/* BUTTON STYLE : 3D */	
.button.is-3d {
	box-shadow:1px 1px 0, 2px 2px 0, 3px 3px 0, 4px 4px 0,5px 5px 0; position: relative; transition: all 0s !important;
	}
.button.is-3d:hover:active {
	top:5px; left:5px; box-shadow:0 0 0 0 !important; transform: none !important;
	}	

/* BUTTON STYLE : SHADOW */
.button.is-shadow {
	color: #fff !important; background: #111; box-shadow: 0px 18px 23px -6px #111; border-color: transparent;
	}
.button.is-shadow:hover {
	transform: translate( 0px, 2px); box-shadow: 0px 2px 10px -5px #111; 
	}
.button.is-shadow:active  {
	transition-duration: .0s; box-shadow: 0px -1px 10px -5px #111; 
	}

/* BUTTON STYLE : SHADOW LIGHT */
.button.is-shadow-light {
	color: inherit !important; background: #fff; box-shadow: 0px 4px 24px -14px; border-color: transparent;
	}
.button.is-shadow-light:hover {
	transform: translate( 0px, 2px); box-shadow: 0px 2px 10px -5px #222; 
	}
.button.is-shadow-light:active  {
	transition-duration: 0s; box-shadow: 0px -1px 10px -5px #222; 
	}

/* BUTTON STYLE : PAPER */	
.button.is-paper {
	position: relative; background: #fff; 
	}
.button.is-paper:after {
	content: ''; position: absolute; top: 3px; left: 3px; width: calc(100% + 4px); height: calc(100% + 4px); background: #dccfcf; border: 2px solid; z-index: -1;
	}
.button.is-paper:active {
	top:2px; left:2px; transform: none !important;
	}	
.button.is-paper:active:after {
	top: 1px; left: 1px; 
	}

/* BUTTON STYLE : SHIFT */	
.button.is-shift {
	position: relative; color: inherit !important;
	}
.button.is-shift:after {
	content: ''; position: absolute; top: 4px; left: 4px; width: calc(100% + 4px); height: calc(100% + 4px); background: #dccfcf; z-index: -1; transition: all .2s;
	}
.button.is-shift:hover:after {
	top: 0px; left: 0px; 
	}

/* BUTTON STYLE : CIRCLE */	
.button.is-circle {
	position: relative; color: inherit !important; border: 0; border-radius: 0 !important;
	}
.button.is-circle:before {
	content: ''; position: absolute; left: 0; top: 48%; width: 50px; height: 50px; border-radius: 50px; transform: translateY(-50%); background: #dccfcf; z-index: -1; transition: all .2s;
	}
.button.small.is-circle:before {
	width: 32px; height: 32px;
	}
.button.big.is-circle i {
	margin-left: -8px;
	}
.button.big.is-circle:before {
	width: 64px; height: 64px;
	}
.button.small.is-circle i {
	margin-left: -6px;
	}
.button.is-circle:hover:before {
	width: 100%;
	}
	
	
/* PRIMARY BUTTON COLOR */
.button.is-primary {
	color: #222;;
	}		
.button.is-primary.is-shadow,
.button.is-primary.is-solid,
.button.is-primary.is-solid-light {
	background-color: #222;;
	}	
.button.is-primary.is-shadow {
	box-shadow: 0px 18px 23px -6px #222;
	} 

/* PRIMARY BUTTON HOVER COLOR */
.button.is-primary:hover {
	color: #89a2c5 ;;
	}		
.button.is-primary.is-shadow:hover,
.button.is-primary.is-solid:hover,
.button.is-primary.is-solid-light:hover,
.button.is-primary.is-shift:after,
.button.is-primary.is-circle:before {
	background-color: #89a2c5 ;;
	}	
.button.is-primary.is-shadow:hover {
	box-shadow: 0px 2px 10px -5px #89a2c5 ; 
	} 

/* SECONDARY BUTTON COLOR */
.button.is-secondary {
	color: #F05365;;
	}		
.button.is-secondary.is-shadow,
.button.is-secondary.is-solid,
.button.is-secondary.is-solid-light {
	background-color: #F05365;;
	}	
.button.is-secondary.is-shadow {
	box-shadow: 0px 18px 23px -6px #F05365;
	} 

/* SECONDARY BUTTON HOVER COLOR */
.button.is-secondary:hover {
	color: #8a797b;;
	}		
.button.is-secondary.is-shadow:hover,
.button.is-secondary.is-solid:hover,
.button.is-secondary.is-solid-light:hover,
.button.is-secondary.is-shift:after,
.button.is-secondary.is-circle:before {
	background-color: #8a797b;;
	}	
.button.is-secondary.is-shadow:hover {
	box-shadow: 0px 2px 10px -5px #8a797b; 
	} 

/* END: BUTTONS */
/* ------------------------------------------ */


/*  --------------------------------------------

	2. FORMS (@forms) - form elements
	
    -------------------------------------------- */
fieldset {
    border: 0; margin: 0; padding: 0;
	}
form p {
 	position: relative; margin: 1.6em 0;
	}
form p:first-child {
 	margin-top: 0;
	}
form p:last-child {
 	position: relative; margin-bottom: 0;
	}
label {
 	display: block; font-size: 11px; letter-spacing: 1px; font-weight: 400; margin-bottom: 0.6em; text-transform: uppercase;
	}
::-webkit-input-placeholder { 
	color:rgba(0, 0, 0, .4);
	}
::-moz-placeholder { 
	color:rgba(0, 0, 0, .4);
	} 
:-ms-input-placeholder { 
	color:rgba(0, 0, 0, .4);
	}
input[type="search"]::-webkit-search-decoration,
input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-results-button,
input[type="search"]::-webkit-search-results-decoration { 
	display: none; 
	}
label input { 
    font-weight: 400;
    }
.input-text,
textarea,
select {
 	padding: 1em; width: 100%; font-size: 13px; color: inherit; border: 2px solid rgba(0, 0, 0, 0.1); border-radius: 0; background-color: transparent; -webkit-appearance: none; -webkit-tap-highlight-color: transparent; box-sizing: border-box; box-shadow: none;
	}
input[type=search] {
	box-sizing: border-box;	
	}
.input-text:hover,
textarea:hover,
input:hover,
select:hover {
 	outline: 0;
	}
.input-text:focus,
textarea:focus,
input:focus,
select:focus {
 	border-color: currentColor; outline: 0;
	}
textarea {
 	min-height: 9em; line-height: 1.6; resize: vertical;
	}
select {
	padding: .8em 1em; padding-right: 36px; -webkit-appearance: none; -moz-appearance: none; -ms-appearance: none; appearance: none; background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAALEwAACxMBAJqcGAAAAZJJREFUaIHt1r9LVWEYB/DPVTEpgqRBaEhocmiyoc2lrZbG+gts8z8I5xykRfwxROBiEBENSdAmQZMKTldwUHAJAltEBG2453Beg7zXe99zXZ4PHC7nx/s833O5931fQgghhBBCCDUbxCqe9qHXc7zDQM6iizjHMaZyFv7HE5wUvRY6GdDpW+4XnyP4gskrR2vvMT5juDg/yFm8gTmtb+YcvzCRsf5D/E7qvyl6ZtXAStLkAOMZ6j7AYVJ3WQ3hS4P4kDTbxVgP9e5hL6m3VvSo1TDWk6bbGO2izl3sJHW+qn7/tbuFjaT5j+Jap27jZzJ+AzczZ2zrDraSEN9wo4NxI/iejNssal2LMTSTMB8xdMnzQ/iUPN/U238oi3GtGakM9b9VdADvXZzF7vcpY1sTWmtDGW7examwgbfJ/dzrSBaP8EcV8nVybza5fqSelTyLKa39Uhl2pjjK87r3Ulk8w6kqdHmc6s9uNouXOFOFP8OLa03UhVeqF5iuq8llc3avFlUL1FKNfUIIIYQQQgjd+QvyJ2oyQcCnYgAAAABJRU5ErkJggg==); background-position: 97% center; background-position: right 10px center; background-repeat: no-repeat; background-size: 20px 20px;
	}
select::-ms-expand {
    display: none;
	}
input[type=file]{
	width: 100%; outline: 0; cursor: pointer; font-size: 13px;
	}
input[type=file]::-webkit-file-upload-button {
	display: inline-block; padding: .6em 1em; outline: 0; color: inherit; background-color: transparent; border: 2px solid rgba(0, 0, 0, 0.07); cursor: pointer;
	}
input[type=checkbox] {
    -webkit-appearance: none; height: 1em; position: relative; background-color: transparent; border: 2px solid currentColor; width: 1em; border-radius: 3px; outline: none;  font-size: 15px; vertical-align: text-bottom; margin-right: .6em;
	}
input[type=checkbox]:active {
    box-shadow: 0 0 0.8em rgba(0, 0, 0, 0.2) inset;
	}
input[type=checkbox]:before,
input[type=checkbox]:after {
    content: ''; background-color: inherit; position: absolute; transition: 0.1s background-color;
	}
input[type=checkbox]:after {
    width: 0.14em; height: 0.60em; transform: rotate(36deg) translate(0.36em, -0.17em); 
	}
input[type=checkbox]:before {
    width: 0.14em; height: 0.35em; transform: rotate(-58deg) translate(-0.22em, 0.30em);
	}
input[type=checkbox]:checked:after,
input[type=checkbox]:checked:before {
    background-color: currentColor;
	}
input[type=radio] {
    -webkit-appearance: none; height: 1em; position: relative; background-color: #fff; border: 2px solid currentColor; width: 1em; border-radius: 50%; outline: none; font-size: 15px; margin-right: .6em; vertical-align: text-bottom;
	}
input[type=radio]:checked:before {
    content: ''; background-color: currentColor; position: absolute; border-radius: 50%; top: 3px; left: 3px; bottom: 3px; right: 3px;
	}
.inline-label {
 	display: inline-block; margin-right: 2.2em;
	}
/* validation  */
label.error {
    position: absolute; bottom: -16px; left: 10px; margin: 0; min-width: 150px; font-size: 10px; padding: .2em .8em; z-index: 1; color: #222; background-color: #fbe7ba; 
    }



/*  --------------------------------------------

	3. COMMON (@common) - common and helper classes
	
    -------------------------------------------- */
/* clearfix */
.cf:before,
.cf:after,
.site-main:before,
.site-main:after,
.site-content:before,
.site-content:after,
.entry-content:before,
.entry-content:after,
.hentry:before,
.hentry:after,
.featured-area:before ,
.featured-area:after,
article:before,
article:after {
 	content: " "; display: table;
	}
.cf:after,
.site-main:after,
.site-content:after,
.entry-content:after,
.hentry:after,
.featured-area:after,
article:after  {
 	clear: both;
	}
.screen-reader-text,
.is-tagline-hidden .site-description,
.is-header-small .site-description {
	clip: rect(1px, 1px, 1px, 1px); width: 1px; height: 1px; overflow: hidden; position: absolute !important;
	}
.center {
 	text-align: center;
	}
.hidden {
	display: none;
	}
.visually-hidden { 
  	position: absolute; overflow: hidden; clip: rect(0 0 0 0); height: 1px; width: 1px; margin: -1px; padding: 0; border: 0; 
	}
.easing,
.nav-image-link img {
 	-webkit-transition: all 0.3s ease-out; transition: all 0.3s ease-out;
	}
.easing-short,
.social-link,
.shopping-cart,
.toggle-link,
.post-thumbnail:before,
.more-link,
.entry-meta a,
input,
button,
a.button,
.entry-content > p:not(.more) > a,
.page-links ul li a,
.gallery-item a:before,
.navigation a,
.widget a,
.post-wrap,
.post-wrap:before,
.share-links a,
.featured-image a:before,
.blog-circles .featured-image,
.tagcloud a,
.blog-simple .featured-image,
.entry-share-wrap,
.wc-tabs a,
.filters li a,
.portfolio-grid .featured-image > a:first-of-type:after,
.entry-title,
.entry-title a {
    -webkit-transition: all 0.15s ease-out; transition: all 0.15s ease-out;
	}
/* fluidbox */
.fluidbox--ready { 
	cursor: -webkit-zoom-in; cursor: -moz-zoom-in; cursor: zoom-in; 
	} 
.fluidbox--opened .fluidbox__wrap,
.fluidbox:not(.fluidbox--closed) .fluidbox__ghost { 
	cursor: -webkit-zoom-out; cursor: -moz-zoom-out; cursor: zoom-out; 
	} 
.fluidbox__ghost {
    -webkit-transition: opacity 0s 0s,-webkit-transform .4s 0s;
    transition: opacity 0s 0s,-webkit-transform .4s 0s;
    transition: opacity 0s 0s,transform .4s 0s;
    transition: opacity 0s 0s,transform .4s 0s,-webkit-transform .4s 0s;
}
.fluidbox--closed .fluidbox__ghost {
    -webkit-transition: opacity 0s .4s,-webkit-transform .4s 0s;
    transition: opacity 0s .4s,-webkit-transform .4s 0s;
    transition: opacity 0s .4s,transform .4s 0s;
    transition: opacity 0s .4s,transform .4s 0s,-webkit-transform .4s 0s;
}


/*  --------------------------------------------

	4. LAYOUT (@layout) : layout styles
	
    -------------------------------------------- */

.site-main {
	padding: 30px 0; position: relative;
	}
.site-main .layout-medium:after {
  	content: ""; display: table; clear: both;
	}
	
/* BOXED LAYOUTS */
.is-body-boxed .site,
.is-middle-boxed .site-main,
.is-content-boxed .content-area {
	background: #fff; position: relative;
	}
/* MIDDLE BOXED */
.is-middle-boxed .site-main {
	padding-top: 4.8%; padding-bottom: 4.8%; margin-bottom: 20px;
	}
.is-middle-boxed .top-content + .site-main {
	margin-top: 20px;
	}
/* MIDDLE BOXED & FOOTER BOXED */
.is-middle-boxed.is-footer-boxed .site-main {
	margin-bottom: 0;
	}

/* CONTENT BOXED */
.is-content-boxed .content-area {
	padding-top: 4.8%; padding-bottom: 4.8%; margin-bottom: 50px;
	}
/* CONTENT WIDTHS */
.layout-full,
.layout-fixed,
.layout-medium,
.header-wrap-inner,
.is-header-fixed-width .header-wrap,
.is-header-float-box:not(.is-header-float-box-menu) .site-header:not(.clone) .header-wrap,
.is-header-full-with-margins .header-wrap,
.is-menu-bar.is-menu-fixed-bg .menu-wrap,
.is-header-full-with-margins.is-menu-bar .site-navigation.clone,
.is-body-boxed .site,
.is-body-boxed .header-wrap,
.is-body-boxed.is-menu-sticky .clone,
.is-middle-boxed .site-main,
.is-footer-boxed .site-footer,
.is-content-boxed .site-content,
.top-bar .top-bar-wrap,
.is-top-bar-fixed .top-bar,
.is-top-bar-fixed-bg .top-bar,
.is-menu-bar .site-branding-wrap,
.is-header-fixed-width.is-menu-sticky .clone,
html .tutor-container,
html .lp-content-area,
html .learn-press-breadcrumb {
	width: 90%; margin-left: auto; margin-right: auto; clear: both;
	}
	
.layout-medium,
.is-header-row .header-wrap-inner,
.is-header-small .header-wrap-inner,
.is-menu-bar.is-menu-fixed-bg .menu-wrap,
.is-header-fixed-width .header-wrap,
.is-header-fixed-width.is-menu-bar .site-navigation,
.is-header-float-box:not(.is-header-float-box-menu) .site-header:not(.clone) .header-wrap,
.is-header-float-box.is-menu-bar .site-header:not(.clone) .site-navigation:not(.clone),
.is-body-boxed .site,
.is-body-boxed .header-wrap,
.is-body-boxed.is-menu-bar .site-navigation,
.is-body-boxed:not(.is-menu-bar) .site-header,
.is-middle-boxed .site-main,
.intro-content,
.is-footer-boxed .site-footer,
.is-content-boxed .site-main .layout-fixed,
.top-bar .top-bar-wrap,
.is-top-bar-fixed .top-bar,
.is-top-bar-fixed-bg .top-bar,
.is-menu-bottom.is-menu-bottom-overflow .site-header:not(.clone) .site-navigation:not(.clone) .menu-wrap,
.site-branding-wrap,
.is-header-border-fixed .header-wrap:after,
.is-header-border-fixed .menu-wrap:after,
html .tutor-container,
html .lp-content-area,
html .learn-press-breadcrumb {
	max-width: 1060px; margin-left: auto; margin-right: auto;
	}
	
/* BOXED/FIXED LAYOUT FIXES */
.is-menu-bar .header-wrap-inner,
.is-header-fixed-width.is-menu-full:not(.is-menu-bar) .header-wrap-inner,
.is-header-fixed-width.is-menu-fixed-bg .menu-wrap,
.is-header-fixed-width.is-menu-bar.is-menu-full .menu-wrap .layout-medium,
.is-body-boxed .header-wrap,
.is-body-boxed .site-footer,
.is-top-bar-fixed .top-bar .top-bar-wrap { 
	width: 100%;
	}
.is-menu-bar.is-menu-full .menu-wrap .layout-medium,
.is-top-bar-full .top-bar .top-bar-wrap {
	max-width: none;
	}
	
.layout-fixed,
.blog-list,
.blog-regular,
.is-content-boxed .single .site-content,
.is-content-boxed .page .site-content {
	max-width: 740px; margin-left: auto; margin-right: auto;
	}
.content-area.with-sidebar .blog-list,
.content-area.with-sidebar .blog-regular {
	max-width: 100%;
	}
.layout-full {
	 max-width: 1900px; overflow: hidden;
	}
.sidebar {
	margin-top: 2em; padding-bottom: 40px; line-height: 1.7; will-change: min-height;
	}
.sidebar-wrap {
    transform: translate(0, 0); transform: translate3d(0, 0, 0); will-change: position, transform;
	}
.hentry {
	margin-bottom: 1em;
	}
.entry-header {
	text-align: center; margin-bottom: 1.4em;
	}


/* BLOG TEXT ALIGN LEFT */
.is-blog-text-align-left .blog-stream .entry-header,
.is-blog-text-align-left .blog-grid .hentry-middle,
.is-blog-text-align-left .blog-list .hentry-middle,
.is-blog-text-align-left .more,
.is-blog-text-align-left .format-link .entry-content,
.is-blog-text-align-left .format-chat .entry-content,
.is-blog-text-align-left .format-status .entry-content,
.is-blog-text-align-left .format-quote blockquote,
.is-blog-text-align-left .nav-links,
.is-blog-text-align-left .page-links  {
	text-align: left;
	}
.is-blog-text-align-left .format-aside .entry-content,
.is-blog-text-align-left .format-quote blockquote,
.is-blog-text-align-left .blog-stream .featured-image img {
	margin-left: 0; 
	}
	
/* BLOG TEXT ALIGN RIGHT */
.is-blog-text-align-right .blog-stream .entry-header,
.is-blog-text-align-right .blog-stream .entry-content,
.is-blog-text-align-right .blog-grid .hentry-middle,
.is-blog-text-align-right .blog-list .hentry-middle,
.is-blog-text-align-right .more,
.is-blog-text-align-right .format-link .entry-content,
.is-blog-text-align-right .format-chat .entry-content,
.is-blog-text-align-right .format-status .entry-content,
.is-blog-text-align-right .format-quote blockquote,
.is-blog-text-align-right .nav-links,
.is-blog-text-align-right .page-links {
	text-align: right;
	}
.is-blog-text-align-right .format-aside .entry-content,
.is-blog-text-align-right .format-quote blockquote {
	margin-right: 0; 
	}
	
/* POST/PAGE TITLE ALIGN LEFT */
.is-post-title-align-left .post-header .entry-header {
	text-align: left;
	}
.is-post-title-align-left .category-description {
	margin-left: 0;
	}
	
/* POST/PAGE TITLE ALIGN RIGHT */
.is-post-title-align-right .post-header .entry-header {
	text-align: right;
	}
.is-post-title-align-right .category-description {
	margin-right: 0;
	}
	

/* SINGLE POST/PAGE STYLES */
.top-content-single {
	position: relative; z-index: 500;
	}

/* TOP CONTENT SINGLE CLASSIC POST */
.top-content-single .post-header-classic .featured-image {
	margin-bottom: 0;
	}
.top-content-single .post-header-classic .entry-header {
	padding: 30px 0; margin: 0 auto; max-width: 88%;
	}
/* post style : title-full */
html:not(.is-middle-boxed) .top-content-single .post-header-classic .entry-header:last-child {
	padding-bottom: 0;
	}

/* SINGLE POST TEXT WITH MARGINS */
.is-single-post-title-with-margins .post-header-classic .entry-header,
html.is-single-post-title-with-margins .top-content-single .post-header-classic .entry-header:last-child { /* post style : title-full */
	padding: 16% 0; margin: 0 auto; max-width: 600px;
	}

/* SINGLE POST OVERLAY FULL SCREEN */
.is-top-content-single-full-screen.top-content-single .post-wrap {
	padding-top: 100vh; padding-top: calc(100vh + 1px);
	}
/* scroll down indicator */
.scrolldown {
	width: 1px; height: 60px; position: absolute; bottom: 30px; left: 0; right: 0; margin: 0 auto; overflow: hidden; z-index: 10; background: linear-gradient(to bottom, white 50%, rgba(255, 255, 255, 0) 50%); background-position: 0 -60px; background-size: 100% 200%; animation: scrolldown 2.2s cubic-bezier(0.76, 0, 0.3, 1) forwards infinite;
	}
@keyframes scrolldown {
  0% { background-position: 0 -60px; }
  75% { background-position: 0 0; }
  100% { background-position: 0 60px; }
}
		
.hentry .entry-content > iframe,
.hentry .media-wrap {
	margin: .2em 0 2em 0;
	}
.post-header .media-wrap,
.media-wrap:first-child {
	margin-top: 0;
	}
iframe.instagram-media {
	max-width: 100% !important; margin-bottom: 2em !important;
	}
.fluid-audio.fluid-width-video-wrapper {
	padding-top: 56.25%;
	}
	.entry-title {
		margin: 0; word-break: break-word;
		}
        .entry-title i {
            display: block; font-size: 11px; font-style: normal; margin-bottom: .1em; letter-spacing: 2px; text-transform: uppercase; font-weight: 700; opacity: .5;
			}
		.archive-header .entry-title {
			margin-bottom: 1.4em !important;
			}
.entry-content {
	margin-bottom: 16px;
	}
.entry-content > p:not(.more) > a:not(.social-link):not(.button) {
    text-decoration: underline;
	}
.entry-content > p:not(.more) > a:not(.social-link):not(.button):hover {
    border-bottom-color: #171717;
	}

/* CATEGORY DESCRIPTION */
.category-description { 
	font-size: 13px; max-width: 360px; margin: 6px auto 60px auto;
	}


/* ----- 4.1 HEADER ----- */
.site-header {
	position: relative; z-index: 1000; font-size: 14px;
	}
/* header text color */
.site-header {
	color: #222;
	}
.header-wrap {
	position: relative;  background-size: cover; background-position: center; background-color: #fff;
	}
.is-header-dark .header-wrap {
	background-color: #090909;
	}
/* header bg mask */
.header-wrap:before {
	content: ""; position: absolute; top: 0; left: 0; right: 0; bottom: 0; opacity: 0; z-index: 20; background: linear-gradient(130deg, #2f00a2 30%, #cc8b47 100%);
	}
/* end: header bg mask */
.header-wrap-inner {
	position: relative; display: flex; flex-direction: row; flex-wrap: wrap; 
	}
.site-navigation {
	flex: 1 100%;
	}

/* Logo Container Width */
.is-logo-container-full .site-branding-wrap {
	width: 100%;
	}
.site-branding {
	position: relative; z-index: 500; text-align: center; width: 100%;
	}
.site-branding-wrap {
	padding: 14px 0; position: relative;
	}
/* HEADER SMALL */
html.is-header-small .site-branding-wrap {
	padding: 0;
	}
/* MENU BAR */
.is-menu-bar .site-branding {
	flex: 1 100%; width: 100%;
	}
.is-menu-bar .site-branding-center {
    display: flex; flex-direction: column; justify-content: center;
	}
/* MENU TOP */
.is-menu-top .site-branding {
	order: 2;
	}
/* fix for: header style : border bottom; site-branding border bottom not visible */
.is-menu-bottom .site-branding {
	z-index: 800;
	}
/* fix for: header small should never get logo top/bottom padding */
.is-header-small .site-header .site-title a {
	padding-top: 0 !important; padding-bottom: 0 !important;
	}
.site-title {
	margin: 0; color: inherit; font-size: 22px; font-weight: 400; line-height: 1; text-transform: none; 
	}
.is-site-title-uppercase .site-title {
	text-transform: uppercase;
	}
.site-description {
	margin: 6px 0 0; opacity: 0.65; font-size: 12px;
	}
/* Tagline Uppercase */
.is-tagline-uppercase .site-description {
	text-transform: uppercase;
	}
.site-description:empty {
	display: none;
	}
.site-header .site-title a {
	display: inline-block; color: inherit; vertical-align: middle;
	}
.is-header-small .site-header .site-title a {
	display: block; 
	}
.is-header-small .site-title img {
	margin-top: -1%;
	}
.site-title img {
	max-height: 26px; transition: all 0.4s; display: inline-block; 
	image-rendering: crisp-edges; image-rendering: -webkit-optimize-contrast; /* fix for resized logo not looking crisp on non-retina screens */
	}
/* fix for logo text/image centering */
.is-header-row .site-title img,
.is-menu-bar .site-title img {
	vertical-align: top;
	}
.site-title .site-title-text {
	vertical-align: text-bottom; line-height: .92; transition: font-size 0.5s;
	}
.is-header-small .site-title .site-title-text {
	vertical-align: middle; 
	}
/* end: fix for logo text/image centering */
.toggle-link {
	display: block; width: 52px; line-height: 100%; height: 100%; text-align: center; cursor: pointer; overflow: hidden; color: inherit; opacity: .8; -webkit-user-select: none; user-select: none;
	}
.toggle-link:hover {
	opacity: 1; color: inherit;
	}



/* --------------------------------- */
/* HEADER BG SHAPE */
.header-bg-shape {
	position: absolute; top: 100%; left: 0; width: 100%; height: 20px; color: #fff; filter: drop-shadow(4px 6px 3px rgba(0, 0, 0, 0.12));
	}
.header-bg-shape path {
	fill: currentColor;
	}
.is-header-dark:not(.is-menu-bottom) .header-bg-shape,
.is-menu-dark.is-menu-bottom .header-bg-shape {
	color: #090909;
	}
/* hide on header transparent and transparent light */
.is-header-transparent .site-header:not(.clone) .header-bg-shape,
.is-header-transparent-light .site-header:not(.clone) .header-bg-shape {
	display: none;
	}
/* --------------------------------- */


/* --------------------------------- */
/* LOGO BEFORE & AFTER WIDGET AREAS */
.site-branding-left,
.site-branding-right {
	justify-content: center; padding: 16px 0;
	}
/* default: shown only on desktops */
.site-branding-left,
.site-branding-right,
.is-header-row .site-branding .site-branding-left,
.is-header-row .site-branding .site-branding-right,
.is-header-small .site-branding .site-branding-left,
.is-header-small .site-branding .site-branding-right {
	display: none;
	}
/* Show on any device */
.is-site-branding-left-show .site-branding-left,
.is-site-branding-right-show .site-branding-right {
	display: flex; 
	}
/* END: LOGO BEFORE & AFTER WIDGET AREAS */	
/* --------------------------------- */

	
/* --------------------------------- */
/* HEADER SEARCH */
.search-toggle {
    display: none; position: absolute; top: 0; right: -12px; width: 42px;
	}
.search-toggle:before {
	 font-family: fontello; font-size: 20px; content: "\e802";
	}
/* Header Search Form */
.search-container {
	display: none; padding: 16px 0; transition: all 0.3s ease-in-out; 
	}
.search-box {
	width: 100%;
    }
.search-box label { 
    margin: 0; text-align: center; color: inherit;
    }
.search-box label span { 
	font-size: 12px; opacity: .5;
	}
.search-box #search-field {
    display: block; padding: 1em 1.2em; text-align: center; background-color: transparent; border: 0; font-size: 1.6em; font-weight: 400; width: 100%; max-width: 100%; box-sizing: inherit;
    }
.site-header ::-webkit-input-placeholder { color: inherit; }
.site-header ::-moz-placeholder { color: inherit; }
.site-header ::-ms-placeholder { color: inherit; }
.search-container .search-submit { 
    display: none !important; 
	}
/* END: HEADER SEARCH */
/* --------------------------------- */


/* --------------------------------- */
/* Header Social Icons */
.site-navigation .social-container {
	position: absolute; top: 12px; right: 0;
	}
.shopping-cart~.social-container {
	right: 50px; left: auto;
	}
.site-header .social-link {
	margin: 0; font-size: 16px; width: 24px; line-height: 30px; color: inherit; opacity: .8; border: 0; background: none;
    }
.site-header .social-link:hover { 
    color: inherit; opacity: 1; background: none;
	}
.site-header .social-container .button { 
	line-height: 1.4; 
	}
.site-header .social-container * + .button,
.site-header .social-container .button + * { 
	margin-left: 12px; 
	}
/* --------------------------------- */

	
/* --------------------------------- */
/* MENU TOGGLE */
.menu-toggle {
	cursor: pointer; color: inherit; display: block; width: 52px; height: 50px; margin-left:-16px; position: relative; user-select: none;
	}
.menu-toggle:hover {
	color: inherit;
	}
.menu-toggle .lines,
.menu-toggle .lines:before, 
.menu-toggle .lines:after {
	display: inline-block; width: 16px; height: 2px; background: currentColor; transition: background 0.6s, transform 0.6s; position: absolute; top: 47%; left: 16px;
	}
.menu-toggle .lines:before, 
.menu-toggle .lines:after {
	left: 0; content: ''; transform-origin: 0.28571rem center;
	}
.menu-toggle .lines:before {
	top: 6px;
	}
.menu-toggle .lines:after {
	top: -6px;
	}
.menu-toggle.close {
	transform: scale3d(0.8, 0.8, 0.8);
	}
.is-menu-toggled-on .menu-toggle .lines {
	background: transparent !important;
	}
.is-menu-toggled-on .menu-toggle .lines:before, 
.is-menu-toggled-on .menu-toggle .lines:after {
	transform-origin: 50% 50%; top: 0;
	}
.is-menu-toggled-on .menu-toggle .lines:before {
	transform: rotate3d(0, 0, 1, 45deg);
	}
.is-menu-toggled-on .menu-toggle .lines:after {
	transform: rotate3d(0, 0, 1, -45deg);
	}
/* --------------------------------- */



/* --------------------------------- */	
/* STICKY MENU & HEADER */
.is-menu-sticky .clone {
	position: fixed; width: 100%; z-index: 1100; top: 0; left: auto; display: none; -webkit-backface-visibility: hidden; backface-visibility: hidden; transition: transform .3s, height .3s, background .4s, opacity .3s;
	}
.is-menu-sticky .clone.is-visible {
	display: block;
	}

/* SMART STICKY MENU & HEADER */
/* hidden when it goes up */
.is-menu-sticky.menu-invisible .clone:not(.is-active) {
	/* IE fallback */ transform: translate(0, -120%); 
	transform: translate(0, calc(-100% - 40px)); opacity: 0; 
	}
/* when shown & detached position is fixed */
.is-menu-sticky.menu-detached .clone {
	display: block;
	}

	
/* admin bar and sticky header */
@media screen and (min-width: 601px) {
	.is-menu-sticky .admin-bar .clone.is-visible,
	.is-menu-sticky.menu-detached .admin-bar .clone /* smart sticky */ {
		top: 46px; 
		}
	}
@media screen and (min-width: 783px) {
	.is-menu-sticky .admin-bar .clone.is-visible,
	.is-menu-sticky.menu-detached .admin-bar .clone /* smart sticky */ {
		top: 32px; 
		}
	}
		
/* END: STICKY MENU & HEADER */
/* --------------------------------- */	


/* Fix for : menu border top leaves top marging empty space on sticky menu */
.is-menu-bar .site-navigation.clone .menu-wrap {
	margin-top: 0;
	}


/* --------------------------------- */
/* Shopping Cart */
.shopping-cart {
	display: flex; align-items: center; justify-content: center; width: 52px; line-height: 100%; height: 52px; font-size: 20px; position: absolute; top: 0; right: -7px; text-align: center; color: inherit; opacity: .8;
	}
.shopping-cart:hover {
	color: inherit; opacity: 1;
	}
.shopping-cart:before {
	content: '\e8c0';
	}
.shopping-cart > span {
	position: relative; margin: -18px 0 0 -8px; width: 16px; line-height: 16px; font-size: 10px; text-align: center; border-radius: 50%; font-family: Helvetica, Arial, sans-serif;
	}
/* --------------------------------- */


/* --------------------------------- */
/* NAVIGATION */
.site-navigation {
	position: relative; z-index: 750;
	}
.site-header .menu-wrap { 
	position: relative; background-color: #fff;
    }
.is-header-row .menu-wrap,
.is-header-small .menu-wrap {
	background: none !important;
	}
.is-menu-dark .menu-wrap { 
    background: #090909;
    }
.site-navigation .layout-medium { 
	position: relative;
    }
.nav-menu { 
    display: block; text-align: center; font-size: 18px;
    }
.nav-menu ul { 
    display: block; list-style:none; padding:0; margin:0; font-size: 1em; line-height: 1.5; letter-spacing: 1px;
    }
.nav-menu ul li { 
	position:relative; display:block; padding: 0; margin:0; cursor: default; 
	}
html:not(.is-header-small) .nav-menu > ul > li {
	transition: line-height 0.5s;
	}
.nav-menu ul li a { 
	display: inline-block; padding: 8px 12px; color: inherit;
	}
.nav-menu .link-text { 
	transition: color 0.4s, background-color 0.4s, opacity 0.4s; 
	}
/* MENU UPPERCASE */
.is-menu-uppercase .nav-menu {
	text-transform: uppercase;
	}
/* --------------------------------- */



/* --------------------------------- */
/* SUB MENUS */	
.nav-menu ul ul {
	font-size: 80%;
	}

/* Submenu Toggles */
.submenu-toggle {
	display: inline-block; width: 1em; height: 1em; line-height: 1em; margin-left: .34em; text-align: center; color: inherit; cursor: pointer; 
	}
.submenu-toggle:before {
	content: '\e80b'; font-size: 1em;
	}
.submenu-toggle:hover {
	border-color: rgba(0, 0, 0, 1)
	}
.nav-menu .active > a > .submenu-toggle {
	border-color: rgba(0, 0, 0, 1)
	}
.is-header-dark:not(.is-menu-bar) .nav-menu .active > a > .submenu-toggle,
.is-menu-dark.is-menu-bar .nav-menu .active > a > .submenu-toggle {
	border-color: rgba(255, 255, 255, 1)
	}
.nav-menu .active > a > .submenu-toggle:before {
	content: '\e86f'; 
	}
/* END: SUB MENUS */	
/* --------------------------------- */



/* --------------------------------- */
/* MOBILE ONLY STYLES */
@media screen and (max-width: 991px) {
	
	/* NAVIGATION */
	.site-header {
		max-height: 50vh; transition: max-height 0.3s;
		}
	/* prevent html body scroll only when sticky menu is toggled on */
	html.is-menu-toggled-on.menu-detached,
	html.is-menu-toggled-on.is-sticky-menu-visible {
		overflow: hidden;
		}
	/* fix: don't show sticky menu if original menu is toggled on */
	.site-navigation.is-active + .clone {
		display: none;
		}
	.is-menu-toggled-on .clone {
		height: 100vh; max-height: 100vh; overflow: auto; overflow-x: hidden;
		}
	.is-menu-toggled-on .menu-wrap {
		padding-bottom: 60px;
		}
	
	.nav-menu { 
		display: none; padding: 2em 0; text-align: left;
		}
	.nav-menu ul li:not(:last-child) {
		border-bottom: 1px solid rgba(0, 0, 0, 0.08);
		}
	.is-menu-dark .nav-menu ul li:not(:last-child),
	.is-header-dark:not(.is-menu-bar) .nav-menu ul li:not(:last-child) {
		border-bottom: 1px solid rgba(255, 255, 255, 0.14);
		}		
	.nav-menu ul ul {
		margin-bottom: 6px; padding: 10px 0 10px 16px; margin-bottom: 0; display: none;
		}
	.nav-menu ul li a {
		padding: 18px 0; display: block;
		}
	.nav-menu > ul > li > a {
		padding: 18px 0; 
		}
	.nav-menu li.active > ul {
		display: block;
		}
	.nav-menu li.active > a,
	.nav-menu ul ul li:last-child a {
		border: 0;
		}
	/* Submenu Toggles */
	.nav-menu .submenu-toggle {
		float: right; width: 40px; height: 40px; line-height: 32px; border: 1px solid rgba(0, 0, 0, 0.08); margin-top: -6px; border-radius: 50%; position: relative; z-index: 1; transition: all .8s;
		}
	.nav-menu .link-text .submenu-toggle {
		display: none;
		}
	.nav-menu .submenu-toggle:before {
		font-size: 12px; width: 9px; line-height: 3.46;
		}
	.nav-menu li li .submenu-toggle {
		width: 34px; height: 34px; line-height: 30px; margin-right: 4px;
		}
	.nav-menu li li .submenu-toggle:before {
		font-size: 10px;
		}
	
	/* HEADER SMALL LOGO CENTER */
    .is-header-small.is-header-logo-center .site-navigation .menu-logo {
		display: none;
		}
	
	/* Header Small : show social icons as static and below search bar */
	.is-header-small .site-navigation .social-container { 
		display: none; position: static; padding: 20px 0 32px 0; text-align: center;
		}	
		
	/* mobile menu toggled on */
	.is-menu-toggled-on .site-header.is-active .nav-menu,
	.is-menu-toggled-on .site-header.is-active .search-container,
	.is-menu-toggled-on .site-header.is-active .site-navigation .social-container { 
		display: block;
		}
	
	/* dark menu : make submenu links text light on mobile too */
	/* submenus can be selected light background (on large screens) even the header is dark background */
	.is-menu-dark .nav-menu ul li a:not(:hover),
	.is-header-dark:not(.is-menu-bar) .nav-menu ul li a:not(:hover) {
		color: rgba(255,255,255, 0.9);
		}
		
	/* HEADER VERTICAL: hide widgets in the header */
	.is-header-vertical .site-header .social-container .widget {
		display: none;
		}
}
/* END: MOBILE ONLY STYLES */
/* --------------------------------- */



/* --------------------------------- */
/* HEADER ROW */
.is-header-row.is-menu-full .header-wrap-inner,
.is-header-small.is-menu-full .header-wrap-inner {
	max-width: 100%;
	}
@media screen and (max-width: 991px) {	
	.is-header-row .header-wrap-inner {
		width: 100%;
		}
}
/* --------------------------------- */


/* --------------------------------- */
/* HEADER SMALL */
.is-header-small .site-title {
	line-height: 50px;
	}
.is-header-small .site-branding {
	position: absolute; z-index: 900; top: 0; left: 32px; width: auto; max-width: 316px; padding: 0; text-align: left; display: flex; align-items: flex-start;
	}
.is-header-small .site-navigation .layout-medium {
	position: static; width: 100%; max-width: 100%;
	}
/* --------------------------------- */



/* --------------------------------- */
/* MAKE sticky HEADER/MENU width FULL even the header/menu layout is fixed width */
.is-header-sticky-always-full .site-header.clone .header-wrap,
.is-header-sticky-always-full.is-menu-bar .site-header.clone .menu-wrap,
.is-header-sticky-always-full .site-header.clone,
.is-header-sticky-always-full.is-menu-bar .site-navigation.clone .menu-wrap,
.is-header-sticky-always-full.is-menu-bar .site-header.clone .site-navigation,
.is-header-sticky-always-full.is-menu-bar .site-navigation.clone,
.is-header-sticky-always-full.is-body-boxed .site-header.clone {
	width: 100%; max-width: 100%; left: 0;
	}
/* --------------------------------- */


/* --------------------------------- */
/* LOGO HOVER EFFECTS */
/* Shine */
.is-logo-hover-shine .site-title a {
	position: relative; overflow: hidden;
	}
.is-logo-hover-shine .site-title a::before {
	position: absolute; top: 0; left: -100%; z-index: 2; display: block; content: ''; width: 50%; height: 100%; background: linear-gradient(to right, rgba(255,255,255,0) 0%, rgba(255,255,255,.3) 100%); transform: skewX(-25deg);
	}
.is-logo-hover-shine .site-title a:hover::before {
	animation: shine 1.0s;
	}
@keyframes shine {
	100% {
		left: 125%;
	}
}
/* Zoom */
.is-logo-hover-zoom .site-title a {
	transition: transform .3s;
	}
.is-logo-hover-zoom .site-title a:hover {
	transform: scale(1.06);
	}
/* Zoom Rotate */	
.is-logo-hover-zoom-rotate .site-title a {
	transition: transform .3s; 
	}
.is-logo-hover-zoom-rotate .site-title a:hover {
	transform: rotate(-6deg) scale(1.1);
	}
/* Drop Shadow */	
.is-logo-hover-drop-shadow .site-title a {
	display: inline-flex; vertical-align: middle; transform: perspective(1px) translateZ(0); box-shadow: 0 0 1px rgba(0, 0, 0, 0); position: relative; transition-duration: 0.3s; transition-property: transform;
	}
.is-logo-hover-drop-shadow .site-title a:before {
    pointer-events: none; position: absolute; z-index: -1; content: ''; top: 100%; left: 5%; height: 10px; width: 90%; opacity: 0; background: -webkit-radial-gradient(center, ellipse, rgba(0, 0, 0, 0.35) 0%, rgba(0, 0, 0, 0) 80%); background: radial-gradient(ellipse at center, rgba(0, 0, 0, 0.35) 0%, rgba(0, 0, 0, 0) 80%); transition-duration: 0.3s; transition-property: transform, opacity;
	}
.is-logo-hover-drop-shadow .site-title a:hover {
    transform: translateY(-5px);
	}
.is-logo-hover-drop-shadow .site-title a:hover:before {
    opacity: 1; transform: translateY(5px);
	}
/* Skew */
.is-logo-hover-skew .site-title a {
	display: inline-flex; vertical-align: middle; transform: perspective(1px) translateZ(0); box-shadow: 0 0 1px rgba(0, 0, 0, 0); transition-duration: 0.3s;transition-property: transform; transform-origin: 0 100%;
	}
.is-logo-hover-skew .site-title a:hover {
    transform: skew(-10deg);
	}
/* Wobble */
.is-logo-hover-wobble .site-title a {
	display: inline-flex; vertical-align: middle; transform: perspective(1px) translateZ(0); box-shadow: 0 0 1px rgba(0, 0, 0, 0); transform-origin: 100% 0;
	}
.is-logo-hover-wobble .site-title a:hover {
	animation-name: wobble; animation-duration: 1s; animation-timing-function: ease-in-out; animation-iteration-count: 1;
	}
@keyframes wobble {
	16.65% { transform: skew(-12deg); }
	33.3% { transform: skew(10deg); }
	49.95% { transform: skew(-6deg); }
	66.6% { transform: skew(4deg); }
	83.25% { transform: skew(-2deg); }
	100% { transform: skew(0); }
  }
/* END: LOGO HOVER EFFECTS */
/* --------------------------------- */



/* --------------------------------- */
/* MENU BOTTOM OVERFLOW */
.is-menu-bottom.is-menu-bottom-overflow .site-header:not(.clone) .site-navigation:not(.clone) {
	margin-bottom: -48px;
	}
.is-menu-bottom.is-menu-bottom-overflow .site-header:not(.clone) .site-navigation:not(.clone) .menu-wrap {
	width: 90%;
	}
/* END: MENU BOTTOM OVERFLOW */
/* --------------------------------- */


/* --------------------------------- */
/* HEADER FLOAT */
/* header small top fix */
.is-header-float.is-header-small:not(.is-menu-toggled-on) .top-content,
.is-header-float.is-header-small:not(.is-menu-toggled-on) .site-header + .site-main,
.is-header-float.is-header-small:not(.is-menu-toggled-on) .site-header ~ .elementor,
.is-header-float.is-header-small:not(.is-menu-toggled-on) .top-content-single  {
	margin-top: -68px; padding-top: 0 !important;
	}
.is-header-float-margin.is-header-small:not(.is-menu-toggled-on) .top-content,
.is-header-float-margin.is-header-small:not(.is-menu-toggled-on) .site-header + .site-main,
.is-header-float-margin.is-header-small:not(.is-menu-toggled-on) .site-header ~ .elementor,
.is-header-float-margin.is-header-small:not(.is-menu-toggled-on) .top-content-single  {
	margin-top: -128px;
	}
/* header menu bar top fix */
.is-header-float.is-menu-bar:not(.is-menu-toggled-on) .top-content,
.is-header-float.is-menu-bar:not(.is-menu-toggled-on) .site-header + .site-main,
.is-header-float.is-menu-bar:not(.is-menu-toggled-on) .site-header ~ .elementor,
.is-header-float.is-menu-bar:not(.is-menu-toggled-on) .top-content-single  {
	margin-top: -249px; padding-top: 0 !important;
	}
/* header row top fix */
.is-header-float.is-header-row:not(.is-menu-toggled-on) .top-content,
.is-header-float.is-header-row:not(.is-menu-toggled-on) .site-header + .site-main,
.is-header-float.is-header-row:not(.is-menu-toggled-on) .site-header ~ .elementor,
.is-header-float.is-header-row:not(.is-menu-toggled-on) .top-content-single  {
	margin-top: -152px; padding-top: 0 !important;
	}
/* no margin when mobile menu is active */
.is-header-float.is-menu-toggled-on .top-content,
.is-header-float.is-menu-toggled-on .site-header + .site-main,
.is-header-float.is-menu-toggled-on .site-header ~ .elementor,
.is-header-float.is-menu-toggled-on .top-content-single  {
	margin-top: 0 !important;
	}

/* fix for: IE - Edge margin top negatives not working right */
.is-MS.is-header-float .top-content,
.is-MS.is-header-float .site-header + .site-main,
.is-MS.is-header-float .site-header ~ .elementor,
.is-MS.is-header-float .top-content .top-content-single {
	display: inline-block; width: 100%;
	}
	
/* featured area top space fix */
.is-header-float:not(.is-menu-toggled-on) .top-content .featured-area,
.is-menu-bottom-overflow:not(.is-menu-toggled-on) .top-content .featured-area {
	margin-top: 0 !important;
	}
.is-header-float .top-content .block:first-child,
.is-menu-bottom-overflow .top-content .block:first-child {
	padding-top: 0;
	}
	
/* HEADER FLOAT MARGIN */
.is-header-float.is-header-float-margin .site-header:not(.clone) {
	margin-top: 50px;
	}
/* --------------------------------- */	




/* --------------------------------- */	
/* HEADER FLOAT BOX MENU: make menu fixed width on mobile too */
@media screen and (max-width: 1200px) {
	.is-header-float-box-menu.is-menu-bar .site-header:not(.clone) .site-navigation:not(.clone) { /* also applies to header float box menu */
		max-width: 90%;
		}
}
/* --------------------------------- */	




/* --------------------------------- */
/* HEADER TRANSPARENT = no-bg */
.is-header-transparent:not(.is-menu-toggled-on) .site-header:not(.clone) .header-wrap,
.is-header-transparent.is-menu-bar:not(.is-menu-toggled-on) .site-header:not(.clone) .site-navigation:not(.clone) .menu-wrap,
.is-header-transparent-light:not(.is-menu-toggled-on) .site-header:not(.clone) .header-wrap,
.is-header-transparent-light.is-menu-bar:not(.is-menu-toggled-on) .site-header:not(.clone) .site-navigation:not(.clone) .menu-wrap,
.is-header-float-box-menu.is-menu-bar:not(.is-menu-toggled-on) .site-header:not(.clone) .header-wrap {
	transition: none; background: none;
	}
/* --------------------------------- */



/* --------------------------------- */
/* HEADER HALF TRANSPARENT - dependency : .is-header-transparent */
/* Note: gets backgound colors from HEADER MASK STYLE option */
.is-header-half-transparent:not(.is-menu-toggled-on) .site-header:not(.clone) .header-wrap:before {
	opacity: 0.6; 
	}		
/* --------------------------------- */



/* --------------------------------- */
/* NEGATIVE IMAGE LOGO SWITCH when original and clone header text is in contrast  */
.site-branding-center .logo-negative,
/* Contrast Status 01 : header-transparent(light-text) and header is not dark(dark-text) */
.is-header-transparent:not(.is-header-dark):not(.is-menu-toggled-on) .site-header:not(.clone) .site-branding-center img:not(.logo-negative), 

/* Contrast Status 02 : header-transparent-light(dark-text) and header is dark(light-text) */
.is-header-transparent-light.is-header-dark:not(.is-menu-toggled-on) .site-header:not(.clone) .site-branding-center img:not(.logo-negative),

/* Contrast Status 03 : header-float-box-menu(light-text) and header is not dark(dark-text) */
.is-header-float-box-menu.is-menu-bar:not(.is-header-dark):not(.is-menu-toggled-on) .site-header:not(.clone) .site-branding-center img:not(.logo-negative) { 
	display: none; 
	}
	
	
/* Contrast Status 01 : header-transparent(light-text) and header is not dark(dark-text) */
.is-header-transparent:not(.is-header-dark):not(.is-menu-toggled-on) .site-header:not(.clone) .site-branding-center .logo-negative,

/* Contrast Status 02 : header-transparent-light(dark-text) and header is dark(light-text) */
.is-header-transparent-light.is-header-dark:not(.is-menu-toggled-on) .site-header:not(.clone) .site-branding-center .logo-negative,

/* Contrast Status 03 : header-float-box-menu(light-text) and header is not dark(dark-text) */
.is-header-float-box-menu.is-menu-bar:not(.is-header-dark):not(.is-menu-toggled-on) .site-header:not(.clone) .site-branding-center .logo-negative {
	display: inline-block;
	}

/* END NEGATIVE LOGO IMAGE */
/* --------------------------------- */



/* --------------------------------- */
/* WHITE TEXT for HEADER & MENU */

/* MENU DARK AND BAR : make menu text white - is-menu-dark: is only applicable to menu-bar layout since on other header layouts(row and small) menu text color inherit from is-header-dark class */  
.is-menu-dark.is-menu-bar .site-navigation,

/* HEADER DARK AND SMALL OR ROW : make all header text white */
.is-header-dark:not(.is-menu-bar):not(.is-header-transparent-light) .site-header,
.is-header-dark.is-header-transparent-light:not(.is-menu-bar) .site-header.clone,

/* HEADER DARK AND MENU BAR : make only logo text white */
.is-header-dark.is-menu-bar .site-header .site-branding, 

/* HEADER ROW and TRANSPARENT: white text for original(non-sticky) header except when mobile menu toggled */
.is-header-transparent.is-header-row:not(.is-menu-toggled-on) .site-header:not(.clone),

/* HEADER SMALL and TRANSPARENT: white text for original(non-sticky) header except mobile menu toggled */
.is-header-transparent.is-header-small:not(.is-menu-toggled-on) .site-header:not(.clone),

/* HEADER MENU BAR and TRANSPARENT : make all header text white - made compatible with is-logo-stick-with-menu */
.is-header-transparent.is-menu-bar:not(.is-menu-toggled-on) .site-header:not(.clone) .site-branding,
.is-header-transparent.is-menu-bar:not(.is-menu-toggled-on) .site-header:not(.clone) .site-navigation:not(.clone),

/* HEADER MENU BAR and FLOAT BOX MENU : make only logo text white */
.is-header-float-box-menu.is-menu-bar:not(.is-menu-toggled-on) .site-header:not(.clone) .site-branding {
	color: #fff;
	}

/* shopping cart number when text is white */
.is-menu-dark .shopping-cart > span,
.is-header-dark:not(.is-menu-bar) .shopping-cart > span,
.is-header-transparent.is-header-small:not(.is-menu-toggled-on) .site-header:not(.clone) .shopping-cart > span,
.is-header-transparent.is-menu-bar:not(.is-menu-toggled-on) .site-header:not(.clone) .site-navigation:not(.clone) .shopping-cart > span,
.is-header-transparent.is-header-row:not(.is-menu-toggled-on) .site-header:not(.clone) .shopping-cart > span {
	background: rgba(255, 255, 255, 0.4);
	}

/* Sub Menu Toogle Icon  when text is white */
.is-menu-dark .submenu-toggle,
.is-header-dark:not(.is-menu-bar) .submenu-toggle,
.is-header-transparent.is-header-small:not(.is-menu-toggled-on) .site-header:not(.clone) .submenu-toggle,
.is-header-transparent.is-menu-bar:not(.is-menu-toggled-on) .site-navigation:not(.clone) .submenu-toggle,
.is-header-transparent.is-header-row:not(.is-menu-toggled-on) .site-header:not(.clone) .submenu-toggle {
	border-color: rgba(255, 255, 255, 0.2);
	}
/* END: WHITE TEXT for HEADER & MENU */
/* --------------------------------- */





/* --------------------------------- */
/* HEADER TRANSPARENT LIGHT = dark text */
/* non-sticky site-header should inherit text color  */
.is-header-transparent-light.is-menu-bar:not(.is-menu-toggled-on) .site-header:not(.clone) .site-branding,
.is-header-transparent-light.is-menu-bar:not(.is-menu-toggled-on) .site-header:not(.clone) .site-navigation:not(.clone)  {
	color: inherit;
	}
/* shopping cart number */
.shopping-cart > span,
.is-header-transparent-light.is-header-small:not(.is-menu-toggled-on) .site-header:not(.clone) .shopping-cart > span,
.is-header-transparent-light.is-menu-bar:not(.is-menu-toggled-on) .site-header:not(.clone) .site-navigation:not(.clone) .shopping-cart > span,
.is-header-transparent-light.is-header-row:not(.is-menu-toggled-on) .site-header:not(.clone) .shopping-cart > span {
	color: #fff; background: rgba(0, 0, 0, 0.9);
	}
/* END HEADER TRANSPARENT LIGHT = dark text */
/* --------------------------------- */




/* --------------------------------- */
/* HEADER BG BLUR */
/* incompatible with header/menu smart sticky */
.is-header-bg-blur-slightly .site-header:not(.clone) { 
	backdrop-filter: blur(6px);
	} 
.is-header-bg-blur-medium .site-header:not(.clone) {
	backdrop-filter: blur(10px);
	} 
.is-header-bg-blur-more .site-header:not(.clone) {
	backdrop-filter: blur(16px);
	} 
.is-header-bg-blur-intense .site-header:not(.clone) {
	backdrop-filter: blur(26px);
	} 
/* fix: sticky menu is not visible with header bg blur actiaved */
.is-header-bg-blur-slightly.is-header-smaller .site-header,
.is-header-bg-blur-medium .site-header,
.is-header-bg-blur-more .site-header,
.is-header-bg-blur-intense .site-header {
	backdrop-filter: none;
	} 
/* --------------------------------- */



/* --------------------------------- */
/* HEADER/MENU SHADOW and STICKY SHADOW */

/* SHADOW SOFT */
.is-header-shadow-soft:not(.is-menu-bar) .site-header .header-wrap,
.is-header-shadow-soft.is-menu-bar .site-header .site-branding,
.is-menu-shadow-soft.is-menu-bar .site-navigation .menu-wrap,
/* sticky shadows */
.is-header-sticky-shadow-soft:not(.is-menu-bar) .site-header.clone .header-wrap,
.is-header-sticky-shadow-soft.is-menu-bar .site-header.clone .site-branding,
.is-menu-sticky-shadow-soft.is-menu-bar .site-header:not(.original) .site-navigation:not(.original) .menu-wrap {
	box-shadow: 0 6px 15px 0 rgba(17,17,17,0.12); 
	}

/* SHADOW SOFT SHORT */
.is-header-shadow-soft-short:not(.is-menu-bar) .site-header .header-wrap,
.is-header-shadow-soft-short.is-menu-bar .site-header .site-branding,
.is-menu-shadow-soft-short.is-menu-bar .site-navigation .menu-wrap,
/* sticky shadows */
.is-header-sticky-shadow-soft-short:not(.is-menu-bar) .site-header.clone .header-wrap,
.is-header-sticky-shadow-soft-short.is-menu-bar .site-header.clone .site-branding,
.is-menu-sticky-shadow-soft-short.is-menu-bar .site-header:not(.original) .site-navigation:not(.original) .menu-wrap {
	box-shadow: 0 3px 10px 0 rgba(17,17,17,0.12); 
	} 

/* SHADOW SOFT SHORTER */
.is-header-shadow-soft-shorter:not(.is-menu-bar) .site-header .header-wrap,
.is-header-shadow-soft-shorter.is-menu-bar .site-header .site-branding,
.is-menu-shadow-soft-shorter.is-menu-bar .site-navigation .menu-wrap,
/* sticky shadows */
.is-header-sticky-shadow-soft-shorter:not(.is-menu-bar) .site-header.clone .header-wrap,
.is-header-sticky-shadow-soft-shorter.is-menu-bar .site-header.clone .site-branding,
.is-menu-sticky-shadow-soft-shorter.is-menu-bar .site-header:not(.original) .site-navigation:not(.original) .menu-wrap {
	box-shadow: 0 0 8px rgba(0, 0, 0, 0.1);
	} 

/* SHADOW SOFT LONG */
.is-header-shadow-soft-long:not(.is-menu-bar) .site-header .header-wrap,
.is-header-shadow-soft-long.is-menu-bar .site-header .site-branding,
.is-menu-shadow-soft-long.is-menu-bar .site-navigation .menu-wrap,
/* sticky shadows */
.is-header-sticky-shadow-soft-long:not(.is-menu-bar) .site-header.clone .header-wrap,
.is-header-sticky-shadow-soft-long.is-menu-bar .site-header.clone .site-branding,
.is-menu-sticky-shadow-soft-long.is-menu-bar .site-header:not(.original) .site-navigation:not(.original) .menu-wrap {
	box-shadow: 0 10px 40px 0 rgba(0, 0, 0, 0.12);
	} 

/* SHADOW OFFSET */
.is-header-shadow-offset:not(.is-menu-bar) .site-header .header-wrap,
.is-header-shadow-offset.is-menu-bar .site-header .site-branding,
.is-menu-shadow-offset.is-menu-bar .site-navigation .menu-wrap {
	box-shadow: 0 28px 16px -18px rgba(17,17,17,0.62); 
	} 

/* SHADOW SIDES */
.is-header-shadow-sides:not(.is-menu-bar) .site-header .header-wrap,
.is-header-shadow-sides.is-menu-bar .site-header .site-branding,
.is-menu-shadow-sides.is-menu-bar .site-navigation .menu-wrap {
	box-shadow: -10px 0px 9px -7px rgba(0, 0, 0, 0.7), 10px 0px 9px -7px rgba(0, 0, 0, 0.7), 5px 5px 15px 5px rgba(0,0,0,0);
	} 

/* SHADOW INSET */
.is-header-shadow-inset:not(.is-menu-bar) .site-header .header-wrap,
.is-header-shadow-inset.is-menu-bar .site-header .site-branding,
.is-menu-shadow-inset.is-menu-bar .site-navigation .menu-wrap {
	box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.43), inset 0 0 6px 3px rgba(0, 0, 0, 0.27);
	} 

/* SHADOW LAYERS */
.is-header-shadow-layers:not(.is-menu-bar) .site-header .header-wrap,
.is-header-shadow-layers.is-menu-bar .site-header .site-branding,
.is-menu-shadow-layers.is-menu-bar .site-navigation .menu-wrap {
	box-shadow: 6px 6px rgba(256, 256, 256, 0.5),
			12px 12px rgba(256, 256, 256, 0.3),
			18px 18px rgba(256, 256, 256, 0.1);
	} 

/* Disable Shadows When HEADER TRANSPARENT */
.is-header-transparent .site-header:not(.clone) .header-wrap,
.is-header-transparent .site-header:not(.clone) .site-branding,
.is-header-transparent .site-header:not(.clone) .site-navigation:not(.clone) .menu-wrap {
	box-shadow: none !important;
	}

/* END: /* HEADER/MENU SHADOW and STICKY SHADOW */ 
/* --------------------------------- */



/* --------------------------------- */
/* MENU & HEADER BORDERS */
.header-wrap:after,
.is-menu-bar .menu-wrap:after {
	content: ""; position: absolute; top: 0px; left: 0px; right: 0px; bottom: 0px; opacity: 1; z-index: -1; background: currentColor;
	}

/* HEADER FLOAT BOX MENU: Hide Header Border */
.is-header-float-box-menu.is-menu-bar:not(.is-menu-toggled-on) .site-header:not(.clone) .header-wrap:after {
	display: none;
	}

/* HEADER TRANSPARENT: Hide Header/Menu Border */
.is-header-transparent .site-header:not(.clone) .header-wrap:after,
.is-header-transparent-light .site-header:not(.clone) .header-wrap:after,
.is-header-transparent.is-menu-bar .site-header:not(.clone) .site-navigation:not(.clone) .menu-wrap:after,
.is-header-transparent-light.is-menu-bar .site-header:not(.clone) .site-navigation:not(.clone) .menu-wrap:after {
	display: none;
	}
.is-header-transparent .site-header:not(.clone) .header-wrap,
.is-header-transparent.is-menu-bar .site-header:not(.clone) .site-navigation:not(.clone) .menu-wrap,
.is-header-float-box-menu .site-header:not(.clone) .header-wrap {
	margin-top: 0;
	}


/* ------------*/
/* HEADER TRANSPARENT STYLES */
/* HEADER TRANSPARENT STYLE : BORDER BOTTOM */
.is-header-transparent-border-bottom.is-header-transparent:not(.is-menu-bar) .site-header:not(.clone) .header-wrap,
.is-header-transparent-border-bottom.is-header-transparent.is-menu-bar .site-header:not(.clone) .site-navigation:not(.clone) .menu-wrap,
.is-header-transparent-border-bottom.is-header-transparent.is-menu-bar .site-header:not(.clone) .site-branding {
	border-bottom: 1px solid rgba(255, 255, 255, 0.25);
	}
/* transparent light */
.is-header-transparent-border-bottom.is-header-transparent-light:not(.is-menu-bar) .site-header:not(.clone) .header-wrap,
.is-header-transparent-border-bottom.is-header-transparent-light.is-menu-bar .site-header:not(.clone) .site-navigation:not(.clone) .menu-wrap,
.is-header-transparent-border-bottom.is-header-transparent-light.is-menu-bar .site-header:not(.clone) .site-branding {
	border-bottom: 1px solid rgba(0, 0, 0, 0.12);
	}
/* HEADER TRANSPARENT STYLE : BORDER BOTTOM FIXED --- this option also effect general header/menu border: makes it fixed width */
.is-header-border-fixed.is-header-transparent:not(.is-menu-bar) .site-header:not(.clone) .header-wrap-inner,
.is-header-border-fixed.is-header-transparent.is-menu-bar .site-header:not(.clone) .site-navigation:not(.clone) .menu-wrap .layout-medium,
.is-header-border-fixed.is-header-transparent.is-menu-bar .site-header:not(.clone) .site-branding-wrap {
	border-bottom: 1px solid rgba(255, 255, 255, 0.25);
	}
/* transparent light */
.is-header-border-fixed.is-header-transparent-light:not(.is-menu-bar) .site-header:not(.clone) .header-wrap-inner,
.is-header-border-fixed.is-header-transparent-light.is-menu-bar .site-header:not(.clone) .site-navigation:not(.clone) .menu-wrap .layout-medium,
.is-header-border-fixed.is-header-transparent-light.is-menu-bar .site-header:not(.clone) .site-branding-wrap {
	border-bottom: 1px solid rgba(0, 0, 0, 0.12);
	}
/* HEADER TRANSPARENT STYLE : BORDER ALL */
.is-header-transparent-border-all.is-header-transparent .site-header:not(.clone) .header-wrap {
	border: 1px solid rgba(255, 255, 255, 0.25);
	}
.is-header-transparent-border-all.is-header-transparent.is-menu-top .site-header:not(.clone) .site-navigation:not(.clone) .menu-wrap,
.is-header-transparent-border-all.is-header-transparent.is-menu-bottom .site-header:not(.clone) .site-branding {
	border-bottom: 1px solid rgba(255, 255, 255, 0.25);
	}
/* transparent light */
.is-header-transparent-border-all.is-header-transparent-light .site-header:not(.clone) .header-wrap {
	border: 1px solid rgba(0, 0, 0, 0.12);
	}
.is-header-transparent-border-all.is-header-transparent-light.is-menu-top .site-header:not(.clone) .site-navigation:not(.clone) .menu-wrap,
.is-header-transparent-border-all.is-header-transparent-light.is-menu-bottom .site-header:not(.clone) .site-branding {
	border-bottom: 1px solid rgba(0, 0, 0, 0.12);
	}
/* END: HEADER TRANSPARENT STYLES */
/* ------------- */


/* Fix: Remove Top Border Radius on HEADER/MENU when sticky */
.site-header.clone .header-wrap,
.site-header.clone .header-wrap:after,
.site-header.clone .header-wrap:before,
.is-menu-bar .site-navigation.clone .menu-wrap,
.is-menu-bar .site-navigation.clone .menu-wrap:after { border-top-left-radius: 0px; border-top-right-radius: 0px; }

/* END: HEADER STYLES */
/* --------------------------------- */




/* --------------------------------- */
/* TOP BAR */
.top-bar {
	position: relative; z-index: 1010; /* -> should be higher than header which is 1000 */ margin: 0 auto; font-size: 11px; color: #fff; padding: 0; font-weight: 400; line-height: 35px;
	}
.top-bar,
.top-bar select option {
	background: #171717;
	}
.top-bar .top-bar-wrap {
	display: flex; flex-direction: row; flex-wrap: wrap; margin: 0 auto; align-items: center; justify-content: center;
	}
.top-bar-wrap > div {
	display: flex; flex-direction: row; flex-basis: 100%; justify-content: space-between;
	}
.top-bar .top-bar-right {
	flex-direction: row-reverse; border-top: 1px solid rgba(255, 255, 255, 0.15); 
	}
/* items in top bar */
.top-bar a {
	color: inherit;
	}
.top-bar .textwidget a:hover {
	text-decoration: underline;
	}		
.top-bar * {
	margin-top: 0; margin-bottom: 0;
	}	
.top-bar .top-bar-left > * + * {
	margin-left: 20px;
	}	
.top-bar .top-bar-right > * + * {
	margin-right: 16px;
	}	
	
/* menus in top bar */
.top-bar ul {
	margin: 0; padding: 0;
	}
.top-bar ul li {
	display: inline-block; line-height: 1.2;
	}
.top-bar ul li + li {
	margin-left: 6px; padding-left: 8px; border-left: 1px solid rgba(255, 255, 255, 0.2); 
	}
.top-bar ul li a {
	color: inherit; opacity: .7;
	}
.top-bar ul li a:hover {
	opacity: 1;
	}
/* socials in top bar */
.top-bar .social-link {
	margin: 0; font-size: 13px; width: 18px; line-height: 30px; border: 0; background: none; color: inherit; opacity: .7;
    }
.top-bar .social-link + .social-link {
    margin-inline-end: 10px;
	}
.top-bar .social-link:hover { 
    opacity: 1; color: inherit; background: none;
    }

/* language switcher menu in top bar */
.top-bar ul .current-lang a {
	opacity: 1;
	}

/* language switcher dropdpwn in top bar */
.top-bar select {
    max-width: none; padding: 0px 5px; line-height: 1.3; font-size: inherit; font-family: inherit; letter-spacing: inherit; text-transform: inherit; color: inherit; width: auto; padding-right: 18px; background-size: 13px 13px; background-position: right 3px center; border-width: 0px; opacity: .7; border-left: 1px solid rgba(255, 255, 255, 0.2); padding-left: 10px; border-right: 1px solid rgba(255, 255, 255, 0.2);
	}
.top-bar select:focus {
	opacity: 1;
	}
html:not(.is-top-bar-light):not(.is-top-bar-transparent):not(.is-header-transparent-light) .top-bar select,
html.is-top-bar-transparent:not(.is-header-transparent-light) .top-bar select,
html.is-header-transparent-light:not(.is-top-bar-transparent) .top-bar select {
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAABs0lEQVRoge2WwStEURTGDyYmUiMLZUFZWViNhZ2NHRtL/gJ2/gNZs5CNBgspG0qSBSk7KSuU1SgLUzZTis2kKT9d7jSnGaY3z3tvNudXp3ndd993vu81794rhmEYhmEYhmHECtAB7APTgMRcs8Au0B5ZJiDHDyVgMsYAU8CH77UZxFvQlM/+Ny0ipyKSDf86/mRCRE5EpNNPKESmDLQBa1QpAqMRvvkx4FXpr7qeUQaohNhRTQrAcATmR4AXpbvte0XmXzdzH/OhavYIDPzD/CDwpPQOfA+JK4CrTuBcNb0H+kKY7wcelM6Z15a4A7jqAa5U82s/FtR8L3Cjnnda3bXz4gzgKgPcKRMXQFcA82ngUj1367Xq5sYdQPz/P6/MHAGpBvPdvWM1P9/oG0oigPiVqKBMfe+iv8xzY3s1q9hQI+2kAojfE4rK3HplKaS6BG+o+4H2kSQDuBoH3pXJZXVvRY2/AdkgmkkHEH9OKimzS74qNHWWakUAVzNAmXrKzZ5mWxXA1TzwqSK467lmdVoZwNWiCrAQRiMIqehi1pETkYwf3Iqxj2EYhmEYhmEYIRCRLx3RBOxVP4s/AAAAAElFTkSuQmCC);
	}
.top-bar select option {
    color: inherit; letter-spacing: inherit;
	}

/* top bar text uppercase */
.is-top-bar-uppercase .top-bar {
	text-transform: uppercase; letter-spacing: 1px;
	}
/* top bar content full width */
.is-top-bar-content-full .top-bar-wrap {
	max-width: 98%;
	}
/* top bar light */
.is-top-bar-light .top-bar,
.is-header-transparent-light.is-top-bar-transparent .top-bar {
	color: inherit;
	}
.is-top-bar-light .top-bar ul li + li,
.is-header-transparent-light.is-top-bar-transparent .top-bar ul li + li {
	border-color: rgba(0, 0, 0, 0.1);
	}
.is-top-bar-light  .top-bar select,
.is-header-transparent-light.is-top-bar-transparent .top-bar select {
    border-color: rgba(0, 0, 0, 0.1);
	}

/* Mobile Visibility */
@media screen and (max-width: 991px) {
	.is-top-bar-mobile-hidden .top-bar,
	.is-top-bar-mobile-left-visible .top-bar-right,
	.is-top-bar-mobile-right-visible .top-bar-left {
		display: none;
		}	
}


/* TOP BAR STYLES */
/* transparent */
.is-top-bar-transparent .top-bar {
	background: none; border-bottom: 1px solid rgba(255, 255, 255, 0.24);
	}
/* top bar transparent and header transparent light . that means top bar should have dark text */
.is-top-bar-transparent.is-header-transparent-light .top-bar {
	background: none; border-bottom: 1px solid rgba(0, 0, 0, 0.1);
	}
.is-top-bar-transparent.is-header-transparent-light .top-bar select option {
	background: #fff;
	}
.is-top-bar-transparent .top-bar select,
.is-top-bar-transparent .top-bar .social-link,
.is-top-bar-transparent .top-bar ul li a {
	opacity: .9;
	}
/* shadow */
.is-top-bar-shadow .top-bar {
	box-shadow: 0 1px 5px rgba(0, 0, 0, 0.10);
	}
/* shadow */
.is-top-bar-shadow-inset .top-bar {
	box-shadow: 0 -2px 5px inset rgba(0, 0, 0, 0.10);
	}
/* border bottom */
.is-top-bar-border-bottom .top-bar {
	border-bottom: 1px solid rgba(0, 0, 0, 0.1);
	}	
/* border bottom bold */
.is-top-bar-border-bottom-bold .top-bar {
	border-bottom: 3px solid #171717;
	}	
/* END: TOP BAR */
/* --------------------------------- */





/* --------------------------------- */
/* ----- 4.2 FOOTER ----- */
.site-footer {
	position: relative; clear: both; width: 100%; font-size: 13px;
	}

/* --------------------------------- */
/* Footer Border Style */
.site-footer:before {
	content: ""; position: absolute; top: 0; left: 0; right: 0; bottom: 0; z-index: 1; pointer-events: none;
	}
/* border top */
.is-footer-border-top .site-footer:before {
	border-top: 1px solid;
	}
/* border all */
.is-footer-border-all .site-footer:before {
	border: 1px solid;
	}
/* border light */
.is-footer-border-light .site-footer:before {
	opacity: 0.08;
	}
/* border top bold */
.is-footer-border-bold .site-footer:before {
	border-width: 4px;
	}
/* end: footer border style */
/* --------------------------------- */	



/* --------------------------------- */	
/* Copyright Bar */
.site-footer .site-info {
	position: relative;	padding: 18px; text-align: center; font-size: 11px; letter-spacing: 1px;
	}
/* Uppercase */
.is-copyright-uppercase .site-footer .site-info {
	text-transform: uppercase;
	}
.site-info .textwidget {
	font-size: 1em;
	}
.site-info .textwidget p:only-child,
.site-info p:last-child {
	margin: 0;
	}
.site-info i {
	color: rgba(220,214,133,1);
	}
/* menu in copyright */
.site-info ul li {
	display: inline-block; margin: 0 8px 6px 8px;
	}
.site-info ul li a:not(:hover) {
	color: inherit;
	}
/* copyright border style */
/* border top */
.is-copyright-border-top .site-info:before {
	content: ""; position: absolute; top: 0; left: 0; width: 100%; background: currentColor; height: 1px;
	}
/* border top light */
.is-copyright-border-light .site-info:before {
	opacity: 0.08;
	}
/* border top bold */
.is-copyright-border-bold .site-info:before {
	height: 3px;
	}
/* end: copyright border style */
/* --------------------------------- */	


/* --------------------------------- */	
/* Footer Subscribe */
.footer-subscribe { 
    position: relative; overflow: hidden; text-align: center; background: #fafafa;
	}
.footer-subscribe .layout-medium {
	padding: 2.4em 0;
	}
.is-footer-subscribe-dark .footer-subscribe { 
    color: #fff; background: #222;
	}
.footer-subscribe h3 {
	font-size: 2em; font-weight: 700; margin-bottom: .4em;
	}
.is-footer-subscribe-dark .footer-subscribe h3 { 
	color: inherit;
	}
.footer-subscribe h3:after {
	position: absolute; bottom: -132px; right: 50%; margin-right: -130px; font-family: fontello; content: '\e868'; font-weight: 400; vertical-align: text-top; display: inline-block; margin-left: 12px; font-size: 238px; color: rgba(0, 0, 0, 0.04); z-index: 0;
	}
.is-footer-subscribe-dark .footer-subscribe h3:after {
	color: rgba(255, 255, 255, 0.07);
	}
.footer-subscribe form p {
	margin: 10px 0 0;
	}
.footer-subscribe label.error {
    left: 50%; transform: translateX(-50%); top: 24px !important;
	}
.footer-subscribe label:not(.error) {
	font-family: inherit; text-transform: none; letter-spacing: 0; font-size: .9rem; color: rgba(0, 0, 0, 0.7);
	}
.is-footer-subscribe-dark .footer-subscribe label:not(.error) {
	color: rgba(255, 255, 255, 0.7);
	}
.footer-subscribe input[type="email"] {
	text-align: center; margin-bottom: .8em; letter-spacing: .03em; color: inherit !important; padding: 1.2em !important; width: 290px !important; background: rgb(255 255 255 / 63%) !important; border-radius: 16px;
	}
.is-footer-subscribe-dark .footer-subscribe input[type="email"] {
	color: #fff !important; background: rgba(255, 255, 255, 0.12) !important;
	}
.footer-subscribe input[type="email"]:focus {
	 background: rgb(255 255 255 / 100%) !important;
	}	
.is-footer-subscribe-dark .footer-subscribe input[type="email"]:focus {
	 background: rgba(255, 255, 255, 0.06) !important;
	}
.footer-subscribe ::-webkit-input-placeholder { color: rgba(0, 0, 0, 0.8); }
.footer-subscribe ::-moz-placeholder { color: rgba(0, 0, 0, 0.8); }
.footer-subscribe ::-ms-placeholder { color: rgba(0, 0, 0, 0.8); }
	
.is-footer-subscribe-dark .footer-subscribe ::-webkit-input-placeholder { color: rgba(255, 255, 255, 0.4); }
.is-footer-subscribe-dark .footer-subscribe ::-moz-placeholder { color: rgba(255, 255, 255, 0.4); }
.is-footer-subscribe-dark .footer-subscribe ::-ms-placeholder { color: rgba(255, 255, 255, 0.4); }
.is-footer-subscribe-dark .footer-subscribe input[type=submit] {
	color: #fff; border-color: #fff;
	}
/* --------------------------------- */		
	


/* --------------------------------- */	
/* Footer Instagram */
.footer-insta,
.null-instagram-feed,
.instagram-pics-wrap {
	position: relative;
	}
.footer-insta ul,
.featured-area .instagram-pics {
	padding: 0; margin: 0; text-align: center; font-size: 0;
	}
.footer-insta ul li,
.featured-area .instagram-pics li { 
	display: inline-block; width: 25%; padding: 0; font-size: 1em;
	}
/* instagram size : large = 6 column = 3x2 col on mobile */
.footer-insta .instagram-size-large li,
.featured-area .instagram-pics.instagram-size-large li {
	width: 33.33333333333%;
	}
/* instagram size : original = 4 column = 2x2 on mobile*/
.footer-insta .instagram-size-original li,
.featured-area .instagram-pics.instagram-size-original li {
	width: 50%;
	}
.instagram-pics + p {
	position: absolute; top: 50%; left: 50%; margin-top: -20px; margin-left: -60px; text-align: center;
	}
.instagram-pics + p a {
	display: inline-block; padding: 6px 16px; font-size: 10px; text-transform: uppercase; letter-spacing: 1px; color: inherit; background: #fff;     transition: all .3s;
	}
.instagram-pics + p a:hover {
	background: #f7f7f7;
	}
.instagram-pics + p a:before {
	font-family: fontello; content: "\e82d"; font-style: normal; font-size: 14px; font-weight: 400; display: inline-block; margin-right: 12px;
	}

/* NEW INSTAGRAM PLUGIN : Social Feed Gallery https://wordpress.org/plugins/insta-gallery/ */
/* buttons vertical center */
.insta-gallery-feed .insta-gallery-actions {
    position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); z-index: 100;
    }
.insta-gallery-feed .insta-gallery-actions .insta-gallery-button {
    font-size: 11px; text-transform: uppercase; letter-spacing: 1px;
	}
/* Carousel Mode */
html .insta-gallery-feed[data-feed_layout=carousel] .swiper-button-next, .insta-gallery-feed[data-feed_layout=carousel] .swiper-button-prev {
    width: 60px; text-align: center;
    }
html .insta-gallery-feed[data-feed_layout=carousel] .swiper-button-next>i, .insta-gallery-feed[data-feed_layout=carousel] .swiper-button-prev>i {
    width: 100%;
    }
.insta-gallery-feed .swiper-pagination-bullet {
    width: 14px; height: 14px;
    }
/* Fix for new insta plugin popup : Social Feed Gallery */
.qligg-mfp-wrap .mfp-container .mfp-content {
    opacity: 1;
    }
.qligg-mfp-wrap .mfp-container .mfp-content .mfp-figure.bottom .mfp-bottom-bar {
    overflow: hidden;
    }
.qligg-mfp-wrap .mfp-container .mfp-content .mfp-bottom-bar .mfp-title .mfp-icons {
    padding: 0 !important; line-height: 46px;
	}

/* Alternative Instagram Plugin: Animated Live Wall
https://wordpress.org/plugins/animated-live-wall/ */
/* html[class*="-share"] {  *conflicts with is-share-links-boxed class
	cursor: default; margin: 0; padding: 0; line-height: 1.9;
	} */
.free-wall .brick {
	box-shadow: none;
	}
.ri-grid {
	margin: 0;
	}
html .ri-grid ul li a {
	background-size: cover;
	}
/* --------------------------------- */	




/* --------------------------------- */	
/* Footer Widgets */
.footer-widgets {
	padding: 50px 0 10px;
	}
.footer-widgets h1, 
.footer-widgets h2,
.footer-widgets h3, 
.footer-widgets h4, 
.footer-widgets h5, 
.footer-widgets h6, 
.footer-widgets blockquote, 
.footer-widgets .tab-titles {
	color: inherit;
	}
/* icon box widgets in footer widgets area */
.footer-widgets.widget-area .widget_pixelwars_core_widget__icon_box {
	margin-bottom: 6px;
	}
/* --------------------------------- */	



/* --------------------------------- */	
/* Password Protected Pages */
.post-password-form {
	max-width: 24em; margin: 2em auto 0 auto; text-align: center; padding: 2em; border: 4px solid #00000012;
	}
.post-password-form p {
	max-width: 360px; margin-left: auto; margin-right: auto;
	}
.post-password-form:before {
	content: '\e8c5'; display: block; font-size: 25px; width: 60px; line-height: 60px; margin: -64px auto 0 auto; color: #fff; background: #222; border-radius: 50%;
	}
.post-password-form p:first-child {
	padding-top: 2em;
    }
.post-password-form p {
	font-size: 12px; text-transform: uppercase; letter-spacing: 1px; font-weight: bold;
    }
.post-password-form input {
    margin: 2em 0; text-align: center;
    }
.post-password-form label input {
	margin-top: 0; margin-bottom: 0;
    }
.post-password-form p > label {
    display: block; position: relative;
    }
/* --------------------------------- */	



/*  --------------------------------------------

	5. MODULES @modules : reusable styles
	
	-------------------------------------------- */

/* SEARCH NO RESULTS */
body.search-no-results .blog-stream .entry-header,
.hentry.no-posts .entry-header {
    text-align: center;
	}

/* INTRO */
.intro {
	position: relative; padding: 50px 0; margin-top: 4px; margin-bottom: 4px; text-align: center; clear: left; background-repeat: no-repeat; background-size: cover; background-position: 50% 50%; overflow: hidden;
	}
.intro:before {
	content: ""; position: absolute; top: 0; left: 0; right: 0; bottom: 0; z-index: 5; opacity: 0; background: #25262e;
	}
.intro iframe,
.site-header iframe,
.post-thumbnail iframe {
	max-width: none;
	}
.intro-content {
	position: relative; z-index: 20; margin-left: auto; margin-right: auto;
	}
.intro-text {
	width: 80%; margin: 0 auto;
	}
.intro-content img {
	display: inline-block; max-height: 160px; margin-bottom: 2em; border-radius: 50%;
	}
.intro h1 {
	font-size: 4vw; font-weight: 400; line-height: 1.3;
	}
.intro *:last-child {
	margin-bottom: 0;
	}
/* intro light text */
.is-intro-text-light .intro {
	color: #fff;
	}
.is-intro-text-light .intro h1 {
	color: inherit;
	}
.is-intro-text-light .intro .button {
	color: #fff; border-color: #fff;
	}
.is-intro-text-light .intro .button:hover {
	background: #fff; color: #222;
	}
.intro-vid {
	max-height: none; position: absolute; top: 0; left: 0; right: 0; bottom: 0;
	}
.intro-vid video,
.intro-vid .fluid-width-video-wrapper {
	width: 100%; height: auto; margin: auto; visibility: hidden;
	}
.loaded .intro-vid video,
.loaded .intro-vid .fluid-width-video-wrapper {
	visibility: visible;
	}

/* FULL WIDTH IMAGE */
img.full,
figure.full,
.parallax-image,
.alignfull {
  	width: 100vw; max-width: none; position: relative; left: 50%; right: 50%; margin-left: -50vw; margin-right: -50vw; margin-top: 1em; margin-bottom: 1.6em;
	}
.parallax-image {
	height: 100vh; background-size: cover; background-repeat: no-repeat; background-position: center; background-attachment: fixed;
	}
.parallax-image.half,
.wp-block-cover.alignfull {
	height: 50vh;
	}
figure.full img {
  	width: 100%; max-width: 100%; position: static; margin-left: 0; margin-right: 0;
	}
img.parallax {
	display: none;
	}



/* HTTP ALERT */
.http-alert {
 	text-align: center
	}
	.http-alert h1 {
        margin: .12em 0 .3em 0; font-size: 8em; letter-spacing: .03em; line-height: 1; 
		}
	.http-alert .button {
		margin-top: 2em; 
		}

/* GRID PAGE */
.show-grid div {
 	font-size: 11px; font-weight: bold; padding-top: 1.2em; padding-bottom: 1.2em; text-align: center; margin-bottom: 1.4em; border: 1px solid rgba(33, 41, 51, 0.1); border-radius: 0;
	}

/* post-thumbnail */
.post-thumbnail { 
    position: relative; z-index: 0; width: 100%; background-position: 50% 50%; background-size:cover; background-color: #111; background-repeat: no-repeat; 
    }
.post-wrap {
	position: relative; padding-top: 100%;
	}
.post-thumbnail .entry-title a {
	display: inline-block;
	}
.post-thumbnail .entry-title a:hover {
    transform: scale(1.07,1.07);
	}
/* ratio tall */
.ratio-fs .post-wrap {
	min-height: 100vh; min-height: calc(100vh + 1px); padding: 0;
	}
.ratio-tall .post-wrap {
	padding-top: 120% !important;
	}
.post-wrap:before {
	content: ""; position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: #2b2b28; opacity: .42; z-index: 2; backface-visibility: hidden;
	}
.link-box .post-thumbnail:hover .post-wrap:before {
	 opacity: .4;
	}
.post-thumbnail .entry-header {
    position: absolute; top: 10%; left: 10%; width: 60%; max-width: 360px; z-index: 100; padding: 0; margin: 0; text-align: center; opacity: 0; transition: all .2s;
	}
.top-content-single .post-thumbnail .entry-header {
	max-width: 100%;
	}
.post-thumbnail .entry-header.ready {
	opacity: 1; 
	}
.post-thumbnail .post-wrap .entry-header:before {
    color: #fff; font-size: 15px; display: none;
	}
.post-thumbnail .entry-title { 
	line-height: 1.2;
	}
.slider-box .entry-title,
.link-box .entry-title { 
	font-weight: 400; 
	}
.more-link.outside {
	position: absolute; z-index: 100; display: none;
	}

	
/* SLIDER OR LINK-BOX TITLE TEXT TRANSFORM DEFAULT : NONE */
.slider-box .entry-title,
.link-box .entry-title {
	text-transform: none;
	}
	
/* SLIDER OR LINK-BOX TITLE UPPERCASE */
.is-slider-title-uppercase .slider-box .entry-title,
.is-link-box-title-uppercase .link-box .entry-title {
	text-transform: uppercase;
	}
	

/* SLIDER OR LINK-BOX TEXT ALIGN : css-default: center */

/* LEFT */
.is-slider-text-align-left .slider-box .entry-header,
.is-link-box-text-align-left .link-box .entry-header {
	text-align: left;
	}

/* RIGHT */
.is-slider-text-align-right .slider-box .entry-header,
.is-link-box-text-align-right .link-box .entry-header {
	text-align: right;
	}
	
/* END : SLIDER OR LINK-BOX TEXT ALIGN */



/* SLIDER OR LINK-BOX VERTICAL ALIGN : css-default: top */

/* CENTER */
.is-slider-v-align-center .slider-box .entry-header,
.is-link-box-v-align-center .link-box .entry-header,
.related-posts .post-thumbnail .entry-header,
.post-header-overlay .entry-header,
.overlay-post .entry-header  {
  top: 50%;
  -webkit-transform: translate3d(0,0,0) perspective(1px) translateY(-50%);
  -ms-transform: translate3d(0,0,0) perspective(1px) translateY(-50%);
  transform: translate3d(0,0,0) perspective(1px) translateY(-50%);
  /*-webkit-backface-visibility: hidden;
    backface-visibility: hidden;*/
	}
.is-header-transparent.is-header-float.is-menu-bar.is-slider-v-align-center .slider-box .entry-header {
	top: 56%;
	}

/* BOTTOM */
.is-slider-v-align-bottom .slider-box .entry-header,
.is-link-box-v-align-bottom .link-box .entry-header {
	top: auto; bottom: 10%;
	}


/* SLIDER HORIZONTAL ALIGN : css-default: left */

/* CENTER */
.is-slider-h-align-center .slider-box .entry-header,
.related-posts .post-thumbnail .entry-header,
.post-header-overlay .entry-header,
.overlay-post .entry-header  {
	left: 0; right: 0; margin: auto;
	}

/* RIGHT */
.is-slider-h-align-right .slider-box .entry-header {
	left: auto; right: 10%;
	}

/* END : SLIDER OR LINK-BOX HORIZONTAL ALIGN */




/* link-box */
.link-box .block-link { 
	display: block; position: absolute; top: 0; left: 0; right: 0; bottom: 0; text-indent: -999em; overflow: hidden; z-index: 100;
	}
.link-box .post-thumbnail .entry-header { 
	width: 80%; 
	}
.post-thumbnail .entry-title,
.post-thumbnail .entry-title a:hover { 
    color: #fff;
	}
.post-thumbnail .entry-meta { 
    padding: 0 0 4px 0; margin-bottom: .6em; color: #fff;
    }	
.post-thumbnail .cat-links a + a {
	margin-left: 8px;
	}	


/* --------------------------------- */
/* WIDGET: ICON BOX */
.pw-icon-box { 
	position: relative; text-align: left; transition: transform .3s ease-out;
	}
.pw-icon-box.is-icon-top:hover { 
	transform: translateY(-5px); 
	}
.pw-icon-box + .pw-icon-box { 
	margin-left: 20px;
	}
.pw-icon-box > a { 
	position: absolute; top: 0; left: 0; right: 0; bottom: 0; z-index: 10; opacity: 0; text-indent: -1000em; overflow: hidden;
	}
.pw-icon-box h3 { 
	font-size: 1.1em; font-weight: 500; line-height: 1; margin: 0; color: inherit;
	}
.pw-icon-box img { 
	height: 52px; width: auto; transition: transform .3s ease-out;
	}
.pw-icon-box:hover a + img { 
	transform: scale(1.14);
	}
.pw-icon-box p { 
	margin: 0; font-size: 12px; line-height: 1.2;
	}
.pw-icon-box h3 + p { 
	opacity: 0.6; margin-top: 2px;
	}
/* display : inline-block */
.pw-icon-box.is-inline-block { 
	flex: 1; max-width: 210px;
	}
/* title uppercase */
.is-icon-box-title-uppercase .pw-icon-box h3 {
	text-transform: uppercase;
	}
/* icon position: top */
.pw-icon-box.is-icon-top { 
	text-align: center;
	}
.pw-icon-box.is-icon-top img + div { 
	margin-top: 16px;
	}
/* icon position: left */
.pw-icon-box:not(.is-icon-top) { 
	display: flex; align-items: center;
	}
.pw-icon-box:not(.is-icon-top) > div { 
	flex: 1;
	}
.pw-icon-box.is-icon-left img { 
	margin-right: 12px;
	}
.pw-icon-box.is-icon-left.is-size-xxxs img,
.pw-icon-box.is-icon-left.is-size-xxs img { 
	margin-right: 8px;
	}
/* icon position: right */
.pw-icon-box.is-icon-right { 
	text-align: right;
	}
.pw-icon-box.is-icon-right img { 
	margin-left: 12px; order: 2;
	}
.pw-icon-box.is-icon-right.is-size-xxxs img,
.pw-icon-box.is-icon-right.is-size-xxs img { 
	margin-left: 8px;
	}
/* icon sizes */
.pw-icon-box.is-size-xxxs img { 
	height: 16px;
	}
.pw-icon-box.is-size-xxs img { 
	height: 20px;
	}
.pw-icon-box.is-size-xs img { 
	height: 26px;
	}
.pw-icon-box.is-size-sm img { 
	height: 32px;
	}
.pw-icon-box.is-size-md img { 
	height: 42px;
	}
.pw-icon-box.is-size-lg img { 
	height: 52px;
	}
.pw-icon-box.is-size-xl img { 
	height: 62px;
	}
.pw-icon-box.is-size-xxl img { 
	height: 72px;
	}
.pw-icon-box.is-size-xxxl img { 
	height: 96px;
	}
/* end: icon sizes */

/* fix: invert icon colors when header is transparent and not dark */
.is-header-transparent:not(.is-header-dark) .site-header:not(.clone) .pw-icon-box img { 
	filter: invert(1);
	}
/* icon boxes in the Featured Area */
.featured-area .pw-icon-box {
	padding: 20px; display: inline-block;
	}
.featured-area .pw-icon-box + .pw-icon-box { 
	margin-left: 0px;
	}
/* END: WIDGET: ICON BOX */
/* --------------------------------- */




/* --------------------------------- */
/* SLIDER STYLES */

/* LABEL STYLE */
.is-slider-title-label .slider-box .entry-title a,
.is-slider-title-label-no-spaces .slider-box .entry-title a,
.is-slider-title-label-close .slider-box .entry-title a,
.is-link-box-title-label .link-box .entry-title a {
	display: inline; padding: 2px 14px; line-height: 1.6; color: #222; background: #fff; -webkit-box-decoration-break: clone; box-decoration-break: clone; 
	}

/* LABEL ROTATED STYLE */
.is-slider-title-rotated .slider-box .entry-title,
.is-link-box-title-rotated .link-box .entry-title {
	-webkit-transform: rotate(-5deg); -ms-transform: rotate(-5deg); transform: rotate(-5deg); 
	}

/* LABEL CLOSE STYLE */
.is-slider-title-label-close .slider-box .entry-title a {
	padding: 8px 18px; line-height: 1.3; box-shadow: 0 0 20px rgba(0, 0, 0, 0.5)
	}

/* LABEL NO SPACES STYLE */
.is-slider-title-label-no-spaces .slider-box .entry-title a {
	padding: 6px 18px; line-height: 1.4;
	}
	
/* INLINE BORDERS STYLE */
.is-slider-title-inline-borders .slider-box .entry-title a,
.is-link-box-title-inline-borders .link-box .entry-title a {
	display: inline; padding: 5px 14px; line-height: 2.1; border: 4px solid #fff; -webkit-box-decoration-break: clone; box-decoration-break: clone; 
	}
	
/* STAMP STYLE */
.is-slider-title-stamp .slider-box .entry-title a {
	display: inline; padding: 5px 14px; line-height: 2.1; border: 4px solid #fff;
	}

/* BORDER BOTTOM STYLE */
.is-slider-title-border-bottom .slider-box .entry-title a,
.is-link-box-title-border-bottom .link-box .entry-title a {
	display: inline; padding: 4px 0; line-height: 1.8; border-bottom: 5px solid #fff;
	}

/* 3D SHADOW */
.is-slider-title-3d-shadow .slider-box .entry-title {
	text-shadow: 0 1px 0 #ccc, 0 2px 0 #c9c9c9, 0 3px 0 #bbb, 0 4px 0 #b9b9b9, 0 5px 0 #aaa, 0 6px 1px rgba(0,0,0,.1), 0 0 5px rgba(0,0,0,.1), 0 1px 3px rgba(0,0,0,.3), 0 3px 5px rgba(0,0,0,.2), 0 5px 10px rgba(0,0,0,.25), 0 10px 10px rgba(0,0,0,.2), 0 20px 20px rgba(0,0,0,.15);
	}

/* 3D SHADOW HARD */
.is-slider-title-3d-hard-shadow .slider-box .entry-title {
	text-shadow: 0px 1px 0px #999, 0px 2px 0px #888, 0px 3px 0px #777, 0px 4px 0px #666, 0px 5px 0px #555, 0px 6px 0px #444, 0px 7px 0px #333, 0px 8px 7px #001135;
	}

/* DARK SHADOW*/
.is-slider-title-dark-shadow .slider-box .entry-title {
	    text-shadow: rgba(0,0,0,.5) -1px 1px 0, rgba(0,0,0,.4) -2px 2px 0, rgba(0,0,0,.3) -3px 3px 0, rgba(0,0,0,.2) -4px 4px 0, rgba(0,0,0,.2) -5px 5px 0, rgba(0,0,0,.2) -6px 6px 0, rgba(0,0,0,.2) -7px 7px 0, rgba(0,0,0,.1) -8px 8px 0, rgba(0,0,0,.1) -9px 9px 0, rgba(0,0,0,.1) -10px 10px 0, rgba(0,0,0,.1) -11px 11px 0, rgba(0,0,0,.1) -12px 12px 0, rgba(0,0,0,.1) -12px 12px 10px;
	}

/* RETRO SHADOW */
.is-slider-title-retro-shadow .slider-box .entry-title {
	text-shadow: 3px 3px 0px #2c2e38, 5px 5px 0px #5c5f72;
	}

/* COMIC SHADOW */
.is-slider-title-comic-shadow .slider-box .entry-title {
	    text-shadow: 11px 12px 12px rgba(0,0,0,0.2), -1px -1px 1px #000000, 1px 1px 0 #000000, 3px 3px 0 #000000, 4px 4px 0 #000000, 5px 5px 0 #000000, 6px 6px 0 #000000, 7px 7px 0 #000000, 8px 8px 0 #000000, 9px 9px 0 #000000, 10px 10px 0 #000000, 11px 11px 0 #000000;
	}

/* FUTURIST SHADOW */
.is-slider-title-futurist-shadow .slider-box .entry-title {
	    text-shadow: 0 1px #808d93, -1px 0 #cdd2d5, -1px 2px #808d93, -2px 1px #cdd2d5, -2px 3px #808d93, -3px 2px #cdd2d5, -3px 4px #808d93, -4px 3px #cdd2d5, -4px 5px #808d93, -5px 4px #cdd2d5, -5px 6px #808d93, -6px 5px #cdd2d5, -6px 7px #808d93, -7px 6px #cdd2d5, -7px 8px #808d93, -8px 7px #cdd2d5;
	}

/* OUTLINE */
.is-slider-title-outline .slider-box .entry-title a {
	-webkit-text-fill-color: transparent; -webkit-text-stroke-width: 1px; -webkit-text-stroke-color: #fff;
	}
@media screen and (min-width: 992px) {
	.is-slider-title-outline .slider-box .entry-title a {
		-webkit-text-stroke-width: 3px;
		}
}

/* RETRO SKEW */
.is-slider-title-retro-skew .slider-box .entry-title a {
	transform: rotate3d(2, 2, .5, 22deg) translate3d(10px, 0px, 0px); -webkit-text-stroke: 1px #8c2738; text-shadow: .3vw .3vw 0 rgba(140, 39, 56, 0.8);
	}
/* --------------------------------- */

	


/* BG FULL OVERLAY */
.is-slider-bg-full-overlay .slider-box .entry-header {
	width: 100%; max-width: 100%; padding: 18px 22px; background: rgba(0, 0, 0, 0.5); backface-visibility: hidden;
	}
.is-slider-bg-full-overlay.is-slider-v-align-top .slider-box .entry-header {
	top: 0%;
	}
.is-slider-bg-full-overlay.is-slider-v-align-bottom .slider-box .entry-header {
	bottom: 0%;
	}
.is-slider-bg-full-overlay .slider-box .post-wrap:before {
	display: none;
	}

/* FULL OVERLAY */
.is-slider-full-overlay .slider-box .entry-header {
	width: 100%; max-width: 100%; padding: 18px 22px;
	}
.is-slider-full-overlay.is-slider-v-align-top .slider-box .entry-header {
	top: 0%;
	}
.is-slider-full-overlay.is-slider-v-align-bottom .slider-box .entry-header {
	bottom: 0%;
	}
.is-slider-full-overlay .slider-box .post-wrap:before {
	opacity: .9; background: linear-gradient(to bottom, rgb(0, 0, 0) 0%,rgba(0, 0, 0, 0) 70%);
	}
.is-slider-full-overlay.is-slider-v-align-bottom .slider-box .post-wrap:before {
	background: linear-gradient(to top, rgb(0, 0, 0) 0%,rgba(0, 0, 0, 0) 70%);
	}
.is-slider-full-overlay .slider-box .post-thumbnail:hover .post-wrap:before {
	opacity: .85; 
	}

/* .is-slider-style-boxed */
.is-slider-style-boxed .slider-box .post-thumbnail .entry-header {
	padding: 26px; max-width: 320px; background: #111;
	}
.is-slider-style-boxed .slider-box .entry-title {
	line-height: 1.4; margin-bottom: 12px;
	}
.is-slider-style-boxed .slider-box .more-link {
	margin-top: 10px;
	}

/* .is-slider-style-boxed-round */
.is-slider-style-boxed-round .slider-box .post-thumbnail .entry-header {
	padding: 44px; max-width: 300px; height: 300px; display: flex; justify-content: center; flex-direction: column; border-radius: 50%; background: #111;
	}
.is-slider-style-boxed-round .slider-box .entry-title {
	line-height: 1.4; margin-bottom: 12px;
	}
.is-slider-style-boxed-round .slider-box .more-link {
	margin-top: 10px; margin-left: auto; margin-right: auto;
	}
.is-slider-style-boxed-round.is-slider-text-align-left .slider-box .more-link {
	margin-left: 0;
	}
.is-slider-style-boxed-round.is-slider-text-align-right .slider-box .more-link {
	margin-right: 0;
	}
.is-slider-style-boxed-round.is-slider-v-align-top .slider-box .entry-header {
	top: -10%;
	}
.is-slider-style-boxed-round.is-slider-v-align-bottom .slider-box .entry-header {
	bottom: -8%;
	}
.is-slider-style-boxed-round.is-slider-h-align-left .slider-box .entry-header {
	left: -2%;
	}
.is-slider-style-boxed-round.is-slider-h-align-right .slider-box .entry-header {
	right: -2%;
	}
.is-slider-style-boxed-round.is-slider-v-align-top.is-slider-h-align-left .slider-box .entry-header {
	border-top-left-radius: 0;
	}
.is-slider-style-boxed-round.is-slider-v-align-top.is-slider-h-align-right .slider-box .entry-header {
	border-top-right-radius: 0;
	}
.is-slider-style-boxed-round.is-slider-v-align-bottom.is-slider-h-align-left .slider-box .entry-header {
	border-bottom-left-radius: 0;
	}
.is-slider-style-boxed-round.is-slider-v-align-bottom.is-slider-h-align-right .slider-box .entry-header {
	border-bottom-right-radius: 0;
	}

/* .is-slider-boxed-borders */
.is-slider-boxed-borders .slider-box .post-thumbnail .entry-header {
	padding: 26px; max-width: 260px; border: 6px solid #fff;
	}
.is-slider-boxed-borders .slider-box .entry-title {
	line-height: 1.4; margin-bottom: 0;
	}
.is-slider-boxed-borders .slider-box .more-link {
	margin-top: 0; position: relative; left: auto; right: auto; bottom: -50px; border-radius: 0; color: #111; background: #fff;
	}
.is-slider-boxed-borders .slider-box .more-link:hover {
	color: #fff; background: #111;
	}

/* .is-slider-boxed-borders */
.is-slider-boxed-borders-full .slider-box .post-wrap:after {
	content: ""; position: absolute;top: 40px;left: 40px;right: 40px;bottom: 40px; border: 6px solid #fff; z-index: 10;
	}
.is-slider-boxed-borders-full .entry-header .more-link {
	display: none;
	}
.is-slider-boxed-borders-full .slider-box .more-link.outside {
	display: inline-block; margin-top: 0; position: absolute; left: 50%; margin-left: -50px; top: 46px; border-radius: 0; color: #111; background: #fff;
	}
.is-slider-boxed-borders-full .slider-box .more-link:hover {
	color: #fff; background: #111;
	}


	
/* END : SLIDER STYLES */
/* --------------------------------- */

	
/* TOP CONTENT & FEATURED AREA */
.top-content {
	position: relative; z-index: 650; /* z-index is for slider custom animation to prevent sitebranding overlaps animated slider items */
	}
.featured-area { 
	margin: -3px; text-align: center;
	} 
.is-header-float .featured-area {
	padding-top: 0;
	}

/* Navigation Menu Widget in Featured Area - no support for 2+ levels */
.featured-area ul.menu {
    font-weight: 500; text-transform: uppercase; font-size: 12px; letter-spacing: 1px; padding: 0; margin: 20px 0;
	}
.featured-area ul.menu:first-child {
    margin-top: 0;
	}
.featured-area ul.menu li {
    list-style: none; display: inline-block; margin: 2px 12px;
	}
.featured-area ul.menu li a {
    color: inherit;
	}
.featured-area ul.menu ul {
    display: none;
	}

/* FEATURED AREA FULL WIDTH */
.is-featured-area-full .top-content .layout-medium,
.is-top-content-single-full.top-content-single .layout-medium,
.is-top-content-single-full-screen.top-content-single .layout-medium {
	width: 100%; max-width: 100%; font-size: 14px; overflow: hidden;
	}
.is-top-content-single-full.top-content-single .layout-medium {
	overflow: initial;
	}

/* FEATURED AREA FULL WITH MARGIN WIDTH */
.is-featured-area-full-margins .top-content .layout-medium,
.is-top-content-single-full-margins.top-content-single .layout-medium {
	max-width: 100%;
	}

.block { 
	float: left; width: 100%; padding: 3px; 
	}
.block.slider-box.ratio-fs {
	padding-top: 0; padding-left: 0; padding-right: 0;
	}
.sidebar .block {
	float: none;
	}
.link-box { 
	width: 25%;
	}
.link-box.w-25 { 
	width: 25%; 
	}
.link-box.w-33 { 
	width: 33.33%; 
	}
.link-box.w-50 { 
	width: 50%; 
	}
.link-box.w-100 { 
	width: 100%; 
	}
/* some layout fixes */
.slider-box.w-50 ~ .link-box:nth-child(6),
.intro + .slider-box.w-50 ~ .link-box:nth-child(7) {
	 clear: left;
	}
.intro + .slider-box.w-50 ~ .link-box:nth-child(6) {
	 clear: none;
	}
.slider-box.w-75 ~ .link-box:nth-child(2),
.slider-box.w-75 ~ .link-box:nth-child(3),
.slider-box.w-75 ~ .link-box:nth-child(4) {
	width: 33.33%; 
	}
.link-box.ratio-2-1 .post-wrap {
	padding-top: 203.87%
	}
	

/* OWL CAROUSEL */
.owl-carousel {
	margin-bottom: 2em;
	}
.owl-item img {
	width: 100%;
	}
.slider-box.w-50 {
	min-height: 300px;
	}
.slider-box.w-75 {
	min-height: 30vw;
	}
.slider-box.w-75.ratio-16-9 {
	min-height: 20vw;
	}
.post-slider {
	margin-bottom: 0; background: #222;
	}
.is-post-slider-bg-none {
	background: none;
	}
	
/* BUGGY WITH PSEUDO ELEMENTS */
/*.owl-theme * { 
	-webkit-backface-visibility: hidden;
	}*/
.owl-title {
 	position: absolute; bottom: 14px; right: 16px; margin: 0; padding: .7em 1.2em; font-size: 12px !important; line-height: 1; background: #fff; background: rgba(255, 255, 255, 0.95);
	}
	
/* Post Slider Overflow */
.owl-carousel.is-overflow-visible .owl-stage-outer {
	overflow: visible;
	}
	
/* Post Slider Box Shadow */
.post-slider.has-shadow,
.post-slider.has-slide-shadow .owl-item {
	box-shadow: 0 10px 20px -5px rgba(0,0,0,0.6);
	}
	
/* Custom Animations */	
.owl-carousel .animated { 
	animation-duration: 0.7s; 
	}
.owl-carousel.custom-animation .owl-stage {
	width: 100% !important; transform: none !important;
	}
.owl-carousel.custom-animation .owl-item {
	opacity: 0; z-index: 10; position: absolute; left: 0 !important;
	}
.owl-carousel.custom-animation .owl-item.active {
	opacity: 1; z-index: 12;
	}
.owl-carousel.custom-animation .owl-animated-out {
  	z-index : 15; 
   	}
	
	
/* arrow nav */
.owl-carousel .owl-nav button.owl-next, 
.owl-carousel .owl-nav button.owl-prev {
	position: absolute; z-index: 50; top: 50%; margin-top: -16px; left: 0px; color: #fff; background: rgba(0, 0, 0, 0.3); font-size: 12px; padding: 16px 19px !important; display: inline-block; line-height: 1; cursor: pointer; user-select: none;
	}
.owl-carousel .owl-nav button.disabled {
	opacity: 0; cursor: default;
	}
.owl-carousel button:focus {
	outline: 0;
	}
.owl-carousel .owl-nav button:before {
	font-family: fontello; font-style: normal; font-weight: 400; font-size: 14px; line-height: 1;
	}
.owl-carousel .owl-nav .owl-next:before {
	content: '\e86e';
	}
.owl-carousel .owl-nav .owl-prev:before {
	content: '\e86d';
	}
.owl-carousel .owl-nav button.owl-next {
	right: 0px; left: auto; 
	}
.owl-carousel .owl-nav button:hover {
	background: #222;
	}
/* dot nav */	
/*.owl-theme .owl-buttons.disabled + .owl-dots {
	margin-top: 12px;
	}*/
.owl-carousel .owl-dots {
	position: absolute; z-index: 40; bottom: 22px; width: 100%; text-align: center; -webkit-tap-highlight-color: transparent;
	}
.owl-carousel .owl-dots button.owl-dot {
	display: inline-block; 
	}
.owl-carousel .owl-dots button.owl-dot span {
	width: 8px; height: 8px; margin: 0 4px; display: block; border-radius: 50%; background: #fff; opacity: .4; transition: all .2s;
	}
.owl-carousel .owl-dots button.owl-dot:not(.active):hover span {
	opacity: .6; transform: scale(1.5);
	}
.owl-carousel .owl-dots button.owl-dot.active span {
	opacity: 1; cursor: default; transform: scale(1.8);
	}
/* SLIDER DOTS ROUNDED LINE */
.is-slider-dots-rounded-line-grow .owl-carousel .owl-dots button.owl-dot.active span {
	transform: none; width: 24px; border-radius: 4px;
	}
.is-slider-dots-rounded-line-grow .owl-carousel .owl-dots button.owl-dot span {
	margin: 0 3px;
	}
	
/* SLIDER BUTTONS LIGHT */
.is-slider-buttons-light .owl-carousel .owl-nav button {
	color: #111; background: rgba(255, 255, 255, 1);
	}
.is-slider-buttons-light .owl-carousel .owl-nav button:hover {
	background: rgba(255, 255, 255, 0.88);
	}
	
/* SLIDER BUTTONS DARKER */
.is-slider-buttons-darker .owl-carousel .owl-nav button {
	background: #222; 
	}
.is-slider-buttons-darker .owl-carousel .owl-nav button:hover {
	background: #000;
	}

/* SLIDER BUTTONS CENTER MARGIN */
.is-slider-buttons-center-margin .owl-carousel .owl-nav button {
	left: 20px; padding: 15px 19px !important; 
	}
.is-slider-buttons-center-margin .owl-carousel .owl-nav button.owl-next {
	right: 20px; left: auto; 
	}

/* SLIDER BUTTONS OVERFLOW */
.is-slider-buttons-overflow .owl-carousel .owl-nav button {
	left: -22px; padding: 15px 18px !important; 
	}
.is-slider-buttons-overflow .owl-carousel .owl-nav button.owl-next {
	right: -22px; left: auto;
	}
.is-slider-buttons-overflow .owl-carousel .owl-nav button:hover {
	transform: scale(1.5);
	}

/* SLIDER BUTTONS ROUNDED */
.is-slider-buttons-rounded .owl-carousel .owl-nav button {
	border-radius: 50%;
	}
		
/* SLIDER BUTTONS BORDER */
.is-slider-buttons-border .owl-carousel .owl-nav button {
	padding: 12px 15px !important; background: none; color: #fff; border: 2px solid #fff; opacity: .8;
	}
.is-slider-buttons-border .owl-carousel .owl-nav button:hover {
	opacity: 1;
	}

/* LINK BOX WRITING MODE */
.is-link-box-title-writing-vertical .link-box .post-thumbnail .entry-header {
	writing-mode: vertical-lr;
	}


/*  --------------------------------------------

	6. BLOG (@blog) - blog related styles
	
    -------------------------------------------- */

/* MOBILE ONLY STYLES */
@media screen and (max-width: 991px) {
	.blog-list .cat-links {
		display: none;
		}
}

/* BLOG REGULAR */
.blog-regular .entry-title { 
	font-size: 1.86em; line-height: 1.2;
	}
.entry-meta { 
	margin: 6px 0; font-size: 11px; line-height: 1.4;
	}
.entry-meta.below-content { 
	margin: 24px 0 0 0; padding: 6px 0; border-top: 5px solid #f2f3ee; border-bottom: 1px solid #f2f3ee;
	}
.entry-meta.below-content:before, 
.entry-meta.below-content:after { 
	content: ''; display: table; clear: both; 
	}
.entry-meta:first-child {
	margin-top: 0;
	}
.entry-meta > span {
	display: inline-block; margin-right: calc(6px + .5vw);
	}
.entry-meta .posted-on,
.entry-meta .comment-link {
	margin-top: 6px; display: inline-block;
	}
.entry-meta > span:last-child {
	margin-right: 0;
	}
.entry-meta .prefix {
	display: none; font-style: italic; font-size: 14px; font-weight: 400; font-family: Constantia, "Lucida Bright", "DejaVu Serif", Georgia, serif; color: rgba(255, 255, 255, 0.8); text-transform: lowercase;
	}
.entry-meta .comment-link a,
.entry-meta .posted-on a,
.entry-meta .author a,
.entry-meta .edit-link a {
	color: inherit;
	}
.entry-meta .updated:not(.published) {
	display: none;
	}
.cat-links a {
	position: relative;	display: inline-block; margin-bottom: 6px; 
	}
.cat-links a + a {
    margin-left: 5px;
	}
	
/* META UPPERCASE */
.is-meta-uppercase .entry-meta {
	text-transform: uppercase;
	}
.is-meta-uppercase .cat-links a {
	letter-spacing: 1px; 
	}
.is-meta-uppercase .entry-share-wrap {
	font-size: .875em; letter-spacing: 1px;
	}

/* META PREFIX OPTIONS : css-default: none */

/* META WITH PREFIX */
.is-meta-with-prefix .entry-meta .prefix {
	display: inline;
	}

/* META WITH ICONS */
.is-meta-with-icons .entry-meta > span:before,
.entry-share:before,
.entry-like a:before {
	margin-right: 3px; font-size: 1.2em;
	}
.is-meta-with-icons .entry-meta .posted-on:before {
	content: '\e803';
	}
.is-meta-with-icons .entry-meta .comment-link:before {
	content: '\e806';
	}
.is-meta-with-icons .entry-meta .author:before {
	content: '\e807';
	}
.is-meta-with-icons .entry-meta .edit-link:before {
	content: '\e857';
	}
.is-meta-with-icons .entry-meta .entry-view:before {
	content: '\e87e';
	}
.is-meta-with-icons .entry-meta .entry-reading-time:before {
	content: '\e804';
	}
 .entry-meta .entry-share:before {
	content: '\e801';
	}
 .entry-meta .entry-like a:before {
	content: '\e821'; background: none;
	}

/* I Recommend This Wordpress Like plugin */
.entry-like {
	letter-spacing: 0; display: inline-block; margin-top: 6px;
	}
.entry-like a {
	text-decoration: none !important;
	}
.dot-irecommendthis { 
	padding-left: 0; color: inherit; margin-bottom: 0; display: inline-block; text-decoration: none; min-height: 14px; background: none; 
	}
.dot-irecommendthis:hover, 
.dot-irecommendthis.active { 
	background: none; color: #F56559 !important; border: 0 !important; margin: 0 !important; padding-left: 0 !important; text-decoration: none;
	}
.post-thumbnail .dot-irecommendthis:hover, 
.post-thumbnail .dot-irecommendthis.active { 
	color: #fff !important;
	}
.dot-irecommendthis.active {
	cursor: default;
	}	


/* END : META PREFIX OPTIONS */

/* META : READING TIME */
.entry-reading-time .rt-label:first-child {
	display: none;
	}

/* META : SHARE */
.entry-share {
	position: relative; display: inline-block; cursor: default;
	}
.entry-share a {
	color: inherit; display: block !important; text-align: left !important; line-height: 1; padding: 12px 8px; border-bottom: 1px solid #eee; background: #fff;
	}
.post-thumbnail .entry-meta .entry-share a,
.post-thumbnail .entry-meta .entry-share a:hover {
	color: #333;
	}
.entry-share a:last-child {
	border: 0;
	}
.entry-share a:hover {
	color: #333; background: #fbfbfb;
	}
.entry-share a:before {
	display: inline-block; width: 2.2em; text-align: center; font-size: 1em; text-align: center !important;
	}
.entry-share .share-facebook:before {
	content: '\e825';
	}
.entry-share .share-twitter:before {
	content: '\e826';
	}
.entry-share .share-pinterest:before {
	content: '\e817'; 
	}
.entry-share .share-gplus:before {
	content: '\e828';
	}
.entry-share .share-mail:before {
	content: '\e868';
	}
.entry-share .share-facebook:hover {
	color: #4885ed;
	}
.entry-share .share-twitter:hover {
	color: #1da1f2;
	}
.entry-share .share-pinterest:hover {
	color: #bd081c;
	}
.entry-share .share-gplus:hover {
	color: #dd4b39;
	}
.entry-share .share-mail:hover {
	color: #3cba54;
	}
.entry-share-wrap {
	position: absolute; z-index: 1000; top: 12px; left: -25px; display: block; width: calc(7.5em + 44px); -webkit-filter: blur(18px); filter: blur(18px); visibility: hidden; opacity: 0; text-align: left;
	}
.entry-share:hover .entry-share-wrap {
	opacity: 1; -webkit-filter: blur(0); filter: blur(0); visibility: visible;
	}
.entry-share-inner-wrap {
	display: block;	margin-top: 1.7em; position: relative; background: #fff; border: 1px solid #e3e3e3; box-shadow: 0px 1px 14px 3px rgba(176, 173, 150, 0.1);
	}
.entry-share-inner-wrap:after, .entry-share-inner-wrap:before {
	bottom: 100%; left: 50%; border: solid transparent; content: " "; height: 0; width: 0; position: absolute; pointer-events: none;
	}
.entry-share-inner-wrap:after {
	border-color: rgba(255, 255, 255, 0); border-bottom-color: #fff; border-width: 8px; margin-left: -8px;
	}
.entry-share-inner-wrap:before {
	border-color: rgba(238, 238, 238, 0); border-bottom-color: #ddd; border-width: 9px; margin-left: -9px;
	}


/* POST THUMBNAIL META STYLES */
.post-thumbnail .entry-meta a { 
    color: rgba(255, 255, 255, 0.66); display: inline-block; position: relative; z-index: 2;
    }
.post-thumbnail .dot-irecommendthis:not(.active) .dot-irecommendthis-count {
	color: rgba(255, 255, 255, 0.9);
	}
.post-thumbnail .entry-meta a:hover { 
    color: rgba(255, 255, 255, 1);
    }
	
/* POST THUMBNAIL CATEGORY LINK STYLES */
.post-thumbnail .cat-links a { 
    display: inline-block; position: relative; z-index: 2;
    }

	
/* CATEGORY LINK STYLES */

/* REGULAR TEXT */
.is-cat-link-regular .cat-links a {
	color: inherit; 
	}

/* BORDER BOTTOM */
.is-cat-link-border-bottom .cat-links a {
	color: inherit;	line-height: 15px; border-bottom: 2px solid #333; 
	}
.is-cat-link-border-bottom.is-post-dark .cat-links a {
	color: #fff; border-color: #fff; 
	}

/* BORDERS */
.is-cat-link-borders .cat-links a {
	color: inherit;	line-height: 1; padding: 4px 10px; border: 2px solid #333; 
	}
.is-cat-link-borders.is-post-dark .cat-links a {
	border-color: #fff; 
	}

/* BORDERS LIGHT */
.is-cat-link-borders-light .cat-links a {
	color: inherit;	line-height: 1; padding: 6px 12px; border: 1px solid rgba(0, 0, 0, 0.12); 
	}
.is-cat-link-borders-light.is-post-dark .cat-links a {
 	border-color: #fff; 
	}

/* BORDERS RAUNDED - can be combined with other styles */
.is-cat-link-rounded .cat-links a {
	border-radius: 16px;
	}

/* SOLID */
.is-cat-link-solid .cat-links a,
.is-cat-link-solid-light .cat-links a {
	color: #fff; line-height: 1; padding: 6px 10px; background: #222; 
	}

/* SOLID LIGHT */
.is-cat-link-solid-light .cat-links a {
	color: #222; background: #f4f4f4; 
	}
.is-cat-link-solid-light .cat-links a:hover {
	color: #222;
	}

/* UNDERLINE */	
.is-cat-link-underline .cat-links a {
	color: inherit; line-height: 1; padding: 1px; box-shadow: inset 0 -7px 0 #fffa00;
	}	
.is-cat-link-underline.is-post-dark .cat-links a {
	color: inherit; line-height: 1.1; padding: 2px; box-shadow: inset 0 -7px 0 #002bff;
	}	

/* LINE BEFORE */	
.is-cat-link-line-before .cat-links a {
	color: inherit;
	}	
.is-cat-link-line-before .cat-links a:before {
	content: ""; display: inline-block; width: 28px; margin-right: 8px; vertical-align: middle; border-top: 1px solid #0000001a;
	}	
.is-cat-link-line-before.is-post-dark .cat-links a:before {
	border-top-color: #fff;
	}

/* DOTS BOTTOM */
.is-cat-link-dots-bottom .cat-links a {
	color: inherit;
	}
.is-cat-link-dots-bottom .cat-links a:before {
	position: absolute; top: 20px; left: 50%; content: "•••"; color: inherit; font-size: 13px; -webkit-transform: translateX(-50%); transform: translateX(-50%); pointer-events: none;
	}
.is-cat-link-dots-bottom .cat-links a:after {
	margin-bottom: 22px; display: block; content: "";
	}

/* RIBBON / RIBBON-LEFT / RIBBON RIGHT */ 
.is-cat-link-ribbon .cat-links a + a {
    margin-left: 26px;
	}
.is-cat-link-ribbon .cat-links a,
.is-cat-link-ribbon-left .cat-links a,
.is-cat-link-ribbon-right .cat-links a {
	 display: inline-block; padding: 2px 10px 1px; color: #222; background-color: #f4f4f4; font-size: 10px; position: relative; z-index: 2;
	}
.is-cat-link-ribbon .cat-links a:hover,
.is-cat-link-ribbon-left .cat-links a:hover,
.is-cat-link-ribbon-right .cat-links a:hover {
	color: #222;
	}
.is-cat-link-ribbon .cat-links a:before,
.is-cat-link-ribbon-left .cat-links a:before {
	content: ""; position: absolute; display: block; bottom: 0; border: .86em solid #f4f4f4; z-index: 1; left: -1.3em; border-left-color: transparent  !important;
	}
.is-cat-link-ribbon .cat-links a:after,
.is-cat-link-ribbon-right .cat-links a:after {
	content: ""; position: absolute; display: block; bottom: 0; border: .86em solid #f4f4f4; z-index: 1; right: -1.3em; border-right-color: transparent  !important;
	}
/*.is-post-dark.is-cat-link-ribbon .cat-links a,
.is-post-dark.is-cat-link-ribbon-left .cat-links a,
.is-post-dark.is-cat-link-ribbon-right .cat-links a {
	background: #fff;
	}
.is-post-dark.is-cat-link-ribbon .cat-links a:before,
.is-post-dark.is-cat-link-ribbon .cat-links a:after,
.is-post-dark.is-cat-link-ribbon-left .cat-links a:before,
.is-post-dark.is-cat-link-ribbon-right .cat-links a:after {
	border-color: #fff;
	}*/
/* RIBBON DARK */
.is-cat-link-ribbon-dark .cat-links a,
.is-cat-link-ribbon-dark:hover .cat-links a {
	color: #fff;
	}
.is-cat-link-ribbon-dark.is-cat-link-ribbon-dark .cat-links a,
.is-cat-link-ribbon-dark.is-cat-link-ribbon-left .cat-links a,
.is-cat-link-ribbon-dark.is-cat-link-ribbon-right .cat-links a {
	background: #222;
	}
.is-cat-link-ribbon-dark.is-cat-link-ribbon .cat-links a:before,
.is-cat-link-ribbon-dark.is-cat-link-ribbon .cat-links a:after,
.is-cat-link-ribbon-dark.is-cat-link-ribbon-left .cat-links a:before,
.is-cat-link-ribbon-dark.is-cat-link-ribbon-right .cat-links a:after {
	border-color: #222;
	}

/* DARK POST FIXES */
.is-cat-link-borders.is-post-dark .cat-links a,
.is-cat-link-borders-light.is-post-dark .cat-links a {
	color: inherit;
	}


/* CATEGORY LINK BG/BORDER COLOR OPTION */
/*.is-cat-link-regular .cat-links a,
.is-cat-link-borders .cat-links a,
.is-cat-link-border-bottom .cat-links a,
.is-cat-link-borders-light .cat-links a {
	color: #002bff;
	}
.is-cat-link-borders .cat-links a,
.is-cat-link-borders-light .cat-links a,
.is-cat-link-border-bottom .cat-links a,
.is-cat-link-ribbon .cat-links a:before,
.is-cat-link-ribbon .cat-links a:after,
.is-cat-link-ribbon-left .cat-links a:before,
.is-cat-link-ribbon-right .cat-links a:after,
.is-cat-link-ribbon.is-cat-link-ribbon-dark .cat-links a:before,
.is-cat-link-ribbon.is-cat-link-ribbon-dark .cat-links a:after,
.is-cat-link-ribbon-left.is-cat-link-ribbon-dark .cat-links a:before,
.is-cat-link-ribbon-right.is-cat-link-ribbon-dark .cat-links a:after {
	border-color: #002bff;
	}
.is-cat-link-solid .cat-links a,
.is-cat-link-solid-light .cat-links a,
.is-cat-link-ribbon .cat-links a,
.is-cat-link-ribbon-left .cat-links a,
.is-cat-link-ribbon-right .cat-links a,
.is-cat-link-ribbon.is-cat-link-ribbon-dark .cat-links a,
.is-cat-link-ribbon-left.is-cat-link-ribbon-dark .cat-links a,
.is-cat-link-ribbon-right.is-cat-link-ribbon-dark .cat-links a {
	background: #002bff;
	}
.is-cat-link-underline .cat-links a {
	box-shadow: inset 0 -7px 0 #002bff;
	}*/
/* END : CATEGORY LINK STYLES */




/* SLIDER CATEGORY LINK BG/BORDER COLOR OPTION */
/*.main-slider-post.is-cat-link-regular .cat-links a,
.main-slider-post.is-cat-link-border-bottom .cat-links a,
.main-slider-post.is-cat-link-borders .cat-links a,
.main-slider-post.is-cat-link-borders-light .cat-links a,
.main-slider-post.is-cat-link-line-before .cat-links a,
.main-slider-post.is-cat-link-dots-bottom .cat-links a:before {
	color: #85f9d2;
	}
.main-slider-post.is-cat-link-borders .cat-links a,
.main-slider-post.is-cat-link-borders-light .cat-links a,
.main-slider-post.is-cat-link-border-bottom .cat-links a,
.main-slider-post.is-cat-link-line-before .cat-links a:before,
.main-slider-post.is-cat-link-ribbon .cat-links a:before,
.main-slider-post.is-cat-link-ribbon .cat-links a:after,
.main-slider-post.is-cat-link-ribbon-left .cat-links a:before,
.main-slider-post.is-cat-link-ribbon-right .cat-links a:after {
	border-color: #85f9d2;
	}
.main-slider-post.is-cat-link-solid .cat-links a,
.main-slider-post.is-cat-link-solid-light .cat-links a,
.main-slider-post.is-cat-link-ribbon .cat-links a,
.main-slider-post.is-cat-link-ribbon-left .cat-links a,
.main-slider-post.is-cat-link-ribbon-right .cat-links a {
	background: #85f9d2;
	}
.main-slider-post.is-cat-link-underline .cat-links a {
	box-shadow: inset 0 -7px 0 #85f9d2;
	}*/
/* END : SLIDER CATEGORY LINK STYLES */




/* META STYLES */

/* DOTS BETWEEN */
.is-meta-dots .site-content .entry-meta > span { 
	position: relative; margin-right: 20px; 
	}
.is-meta-dots .site-content .entry-meta > span:after {
	display: list-item; content: ""; list-style: disc; position: absolute; top: -3px; right: -13px; width: 10px; list-style-position: inside; line-height: 1; color: #ddd; font-size: 16px;
	}
.is-meta-dots .site-content .entry-meta > span:last-child {
	margin-right: 0;
	}
.is-meta-dots .site-content .entry-meta > span:last-child:after {
	display: none;
	}

/* END : META STYLES */
		


/* ENTRY TITLE */		
.entry-title a { 
	color: inherit;
	}
.entry-title a:hover { 
	color: #111;
	}
.more { 
	text-align: center; margin-bottom: 0; 
	}
.more-link { 
    color: inherit; text-transform: uppercase; line-height: 1; padding: 13px 22px; margin: .6em 0; display: inline-block; font-size: 11px; letter-spacing: 1px;
	}
.blog-regular .more-link {
	margin-top: 1em;
	}
.more-link:hover {
	background: #111; color: #fff; border-color: transparent;
	}
/* links active effect */
.more-link:active,
.entry-title a:active,
.entry-meta a:active,
.widget li a:active,
.top-bar a:active {
	top: 1px; left: 1px; position: relative;
	}


/* MORE LINK STYLES  */
/* BUTTON STYLE */
.is-more-link-button-style .blog-stream .more-link {
	border: 2px solid currentColor;
	}
.is-more-link-button-style .blog-stream .more-link:hover {
    border-color: transparent;
	}
/* BORDER BOTTOM */
.is-more-link-border-bottom .blog-stream .more-link {
	padding: 6px 0px 4px; border-bottom: 3px solid #333;
	}
.is-more-link-border-bottom .blog-stream .more-link:hover,
.is-more-link-border-bottom-light .blog-stream .more-link:hover {
	color: inherit; background: none; padding: 6px 12px 4px;
	}
/* BORDER BOTTOM LIGHT */
.is-more-link-border-bottom-light .blog-stream .more-link {
	padding: 6px 0px 4px; border-bottom: 3px solid rgba(0, 0, 0, 0.07);
	}
/* BORDER BOTTOM DOTTED */
.is-more-link-border-bottom-dotted .blog-stream .more-link {
	padding: 1.3em 2.6em 1.1em; letter-spacing: .14em; border: 0; border-bottom: 1px dotted #ccc;
	}
.is-more-link-border-bottom-dotted .blog-stream .more-link:after {
	content: " \2192";
	}
.is-more-link-border-bottom-dotted .blog-stream .more-link:hover {
    color: inherit; background-color: #f7f7f7;
	}
/* END : MORE LINK STYLES */


/* SLIDER MORE LINK VISIBILITY OPTIONS - default : show */		
.post-thumbnail .more-link { 
	padding: 10px 16px; color: #fff; margin-top: 30px;
	}
.post-thumbnail .more-link:hover {
	background: #fff; color: #111;
	}
/* SLIDER MORE LINK SHOW ON HOVER */
.is-slider-more-link-show-on-hover .post-thumbnail .more-link {
	opacity: 0;
	}
.is-slider-more-link-show-on-hover .post-thumbnail:hover .more-link {
	opacity: 1;
	}
/* SLIDER MORE LINK HIDDEN */
.is-slider-more-link-hidden .post-thumbnail .more-link {
	display: none;
	}
/* END : SLIDER MORE LINK VISIBILITY OPTIONS */



/* SLIDER MORE LINK STYLES */
/* BUTTON STYLE */
.is-slider-more-link-button-style .post-thumbnail .more-link {
	border: 2px solid #fff;
	}
/* BORDER BOTTOM */
.is-slider-more-link-border-bottom .post-thumbnail .more-link {
	padding: 6px 0px 4px; border-bottom: 3px solid #fff;
	}
.is-slider-more-link-border-bottom .post-thumbnail .more-link:hover {
	color: #fff; background: none; padding-left: 8px; padding-right: 8px;
	}
/* END : SLIDER MORE LINK STYLES */




/* CONTENT STYLE */
/* Boxed */
.is-posts-boxed .blog-list .hentry,
.is-posts-boxed .blog-regular .hentry,
.is-posts-boxed .hentry-wrap {
    padding: calc(1.4em + 1.4vw) calc(1em + 1.2vw); background: #fff;
	}
.is-posts-boxed .woocommerce .hentry-wrap,
.is-posts-boxed .single-portfolio .hentry-wrap {
    padding: calc(1em + 3.2vw) calc(1em + 3.2vw);
	}
.is-posts-boxed .blog-grid .hentry-wrap {
    padding: calc(1em + 1vw);
	}
.is-posts-boxed .blog-grid .hentry-middle {
	padding-bottom: 0;
	}

/* DELAYED ******** */
/* Image Full */
.is-posts-boxed-image-full .hentry-wrap .featured-image,
.is-posts-boxed-image-full .blog-stream.first-full .hentry:first-child .featured-image {
    margin: calc(-1.4em - 1.4vw) calc(-1em - 1.2vw) 2em calc(-1em - 1.2vw);
	}
.is-posts-boxed-image-full .blog-stream.first-full .hentry:first-child .featured-image {
    margin-bottom: 0;
	}
.is-posts-boxed-image-full .hentry-wrap .entry-header + .featured-image,
.is-posts-boxed-image-full .blog-stream.first-full .hentry:first-child .featured-image{
    margin-top: 0;
	}
.is-posts-boxed-image-full .blog-grid .featured-image {
    margin: calc(-1em - 1vw) calc(-1em - 1vw) 0 calc(-1em - 1vw);
	}
.is-posts-boxed-image-full .blog-list .featured-image {
    margin: calc(-1.4em - 1.4vw) calc(-1em - 1.2vw) calc(-1.4em - 1.4vw) calc(-1em - 1.2vw);
	}
/* DELAYED ******** */


/* featured image */
.featured-image { 
	position: relative;
	}
.featured-image,
.entry-header + .full-width-image,
.post-header-overlay-inline,
.hentry-wrap .post-header iframe { 
	margin-bottom: 22px; 
	}
	.featured-image > a:first-of-type:before {
		content: ""; position: absolute; top: 0; right: 0; bottom: 0; left: 0; background: #222; opacity: 0;
		}
	.featured-image img {
 		display: block; margin-left: auto; margin-right: auto;
		}
	.featured-image:hover > a:first-of-type:before {
		opacity: .05;
		}
.featured-image .fluid-width-video-wrapper,
.featured-image iframe,
.featured-image .media-wrap {
    margin: 0;
    }
	
/* BLOG SMALL common styles : list - grid - circles */
.blog-small .entry-title {
	font-size: 1.4em;
	}

/* BLOG LIST */
.blog-list .hentry {
	margin-bottom: 2.8em; padding: 0;
	}
.blog-list .featured-image {
	position: relative; width: 26%; float: left; margin-top: 0; margin-bottom: 0; padding-top: 38%; background-size: cover; background-position: center;
	}
.blog-list .has-post-video .featured-image {
	padding-top: 0;
	}
.blog-list .featured-image a {
	position: absolute; top: 0; left: 0; right: 0; bottom: 0;
	}
.blog-list .featured-image img {
	display: none;
	}
.blog-list .hentry-middle {
	width: 74%; float: left; padding: 1em 2em 0 2em; text-align: center;
	}
.blog-list .hentry:not(.has-post-thumbnail) .hentry-middle {
    padding: 0;
	}
.blog-list .hentry-middle:first-child {
	width: 100%;
	}
.blog-list .hentry.has-post-thumbnail .entry-content,
.blog-list .comment-link {
	display: none; 
	}
.blog-list .entry-header {
	padding: 0; margin-bottom: 0; 
	}
.blog-list.first-full .hentry:first-child .entry-header {
	margin-bottom: 20px; 
	}
.blog-list .entry-content {
	padding-top: 8px; margin-bottom: 0px;
	}
	
/* BLOG LIST FIRST FULL */
.blog-list.first-full .hentry:first-child .featured-image {
	width: auto; float: none; padding: 0;
	}
.blog-list.first-full .hentry:first-child .featured-image a {
	position: static;
	}
.blog-list.first-full .hentry:first-child .featured-image img {
	display: block;
	}
.blog-list.first-full .hentry:first-child .hentry-middle {
	width: 100%; float: none; max-width: 480px; margin-left: auto; margin-right: auto;
	}
.blog-list.first-full .hentry:first-child .entry-content {
	display: block;
	}

/* BLOG CIRCLES */
.blog-circles .hentry {
	overflow: hidden;
	}
/* fix for : share meta dropdown is cut due to overflow hidden */
.blog-circles .hentry:hover {
	overflow: visible; animation: .3s delay-overflow;
	}
@keyframes delay-overflow {
	0% { overflow: hidden; }
	99% { overflow: hidden; }
	100% { overflow: visible; }
	}
.blog-circles .featured-image {
	border-radius: 50%; opacity: 1; -webkit-transform: scale(1.25) translateX(0px) translateY(-18px); transform: scale(1.25) translateX(0px) translateY(-18px);
	}
.blog-circles .has-post-video .featured-image {
	padding-top: 0; -webkit-transform: none !important; transform: none !important;
	}
.blog-circles .featured-image a:before {
	display: none;
	}
.blog-circles .hentry-middle *,
.blog-circles .hentry-middle *:before {
	text-align: right;
	}
.blog-circles .hentry:nth-child(even) .hentry-middle *,
.blog-circles .hentry:nth-child(even) .hentry-middle *:before {
	text-align: left;
	}
.blog-circles .hentry:nth-child(even) .featured-image {
	float: right; -webkit-transform: scale(1.25) translateX(0px) translateY(-18px); transform: scale(1.25) translateX(0px) translateY(-18px);
	}	
.blog-circles .hentry:hover .featured-image {
	opacity: 1;
	-webkit-transform: scale(1) translateX(0px) translateY(0px); transform: scale(1) translateX(0px) translateY(0px);
	}

/* BLOG GRID */
.blog-grid-wrap {
	margin-left: -14px; margin-right: -14px;
	}
.blog-grid .hentry {
	width: 100%; margin-bottom: 2em; padding: 0 14px; text-align: center;
	}
.blog-grid .entry-header {
	padding: 0; margin-bottom: 0; 
	}
.blog-grid .featured-image {
	margin-bottom: 0;
	}
.blog-grid .featured-image img {
	width: 100%;
	}
.blog-grid .entry-meta.above-title {
	margin-bottom: .7em;
	}
.blog-grid .hentry-middle {
	padding: 1.8em 1.2em;
	}
.blog-grid .entry-content {
	padding-top: 0.8em; margin-bottom: 0;
	}
.blog-grid .entry-content p:not(:last-child) {
	margin: 0 0 1em 0;
	}
.blog-grid .more-link {
	padding: 11px 20px;
	}
.blog-grid .post-wrap {
	padding-top: 130%;
	}
	
/* BLOG GRID FIRST FULL */
.blog-grid.first-full .hentry:first-child {
	margin-bottom: 3em;
	}
.blog-grid.first-full .hentry:first-child .hentry-middle {
	max-width: 440px; margin-left: auto; margin-right: auto;
	}

/* BLOG SIMPLE */	
.blog-simple {
	margin: 0 auto 1em auto; max-width: 670px;
	}
.blog-simple .hentry {
	position: relative; height: 60px; margin-bottom: 0; overflow: hidden; border-bottom: 1px solid #f3f3f3;
	}
.is-posts-boxed .blog-simple .hentry {
	background: #fff; margin-bottom: 4px; border: 0;
	}
.blog-simple .hentry:last-child {
	border-bottom: 0;
	}
.blog-simple .hentry-left {
	position: absolute; top: 0; left: 0; width: 60px; height: 60px; 
	}
.blog-simple .hentry-middle {
	float: left; margin-left: 80px; position: relative; top: 50%; -webkit-transform: translateY(-50%); -ms-transform: translateY(-50%);  transform: translateY(-50%);
	}
.blog-simple .post-link {
	position: absolute; top: 0; left: 0; right: 0; bottom: 0; z-index: 3; text-indent: -200%; overflow: hidden;
	}
.blog-simple .featured-image {
	position: absolute; top: 0; left: 0; bottom: 0; right: 0; margin: 0; opacity: 0; /* -webkit-filter: blur(12px); filter: blur(12px); */ background-size: cover; background-position: center center;
	}
.blog-simple .hentry:hover .featured-image {
	opacity: 1; /* -webkit-filter: blur(0px); filter: blur(0px); */
	}
.blog-simple .entry-date {
	padding: 13px 10px; font-size: 10px; letter-spacing: 2px;
	}
.blog-simple .entry-date span {
	display: block; text-align: center; line-height: 1; text-transform: uppercase;
	}
.blog-simple .day {
	font-weight: bold; font-size: 23px; letter-spacing: -1px;
	}
.blog-simple .month {
	color: #ddd;
	}
.blog-simple .entry-date .year {
	color: #ddd; display: none;
	}
.section-launch {
	margin: 2em 0; text-align: center;
	}

/* SECTION TITLE */
.section-title {
	margin: 50px 0 30px !important; text-align: center;
	}
.section-title:first-child,
.vc_row:first-child .section-title {
	margin-top: 0 !important;
	}


/* PAGINATION */
.navigation,
.pagination,
.page-links,
.nav-links { /* nav-links is for learnpress numbered pagination */
	width: 100%; clear: both; margin: 3em 0; padding-bottom: 1px; font-size: 13px; text-transform: uppercase; overflow: hidden;
	}
	.navigation:not(.pagination) > div {
 		width: 50%; float: left; min-height: 1px;
		}
	.navigation a {
 		display: inline-block; padding-bottom: .2em; color: inherit; font-weight: bold; font-size: 14px; line-height: 1.3; border-bottom: 2px solid transparent;
		}
	.navigation a:hover {
		color: #555; border-color: #333;
		}
	.navigation .nav-next {
 		text-align: right;
		}
	.navigation .meta-nav {
 		margin-right: .5em
		}
	.navigation .nav-next .meta-nav {
 		margin-left: .5em; margin-right: 0;
		}
/* NUMBERED PAGINATION */
.page-links {
    margin: 1em 0;
	}
.nav-links,
.page-links {
	font-size: 12px; list-style: none; text-align: center; padding: 0;
	}
.nav-links .page-numbers,
.nav-links{
	display: inline-block; margin: .3em;
	}
    .nav-links li {
        display: inline-block;
        }
	.page-links .post-page-numbers,
	.page-links > .page-number {
		display: inline-block; padding: .2em .8em; margin: 0; font-size: 13px; letter-spacing: 1px; font-weight: 400; color: inherit; background: rgba(93, 77, 30, 0.12); transition: all .2s;
		}
	.page-links a:hover {
		background: rgba(93, 77, 30, 0.18);
		}
	.page-links .post-page-numbers.current {
		background: rgb(0 0 0 / 82%); color: #fff;
		}
.page-numbers.current {
	margin: 0 8px;
	}
.page-numbers.current,
.page-links > .page-number {
	display: inline-block; width: 52px; line-height: 52px; font-weight: bold; text-align: center; cursor: default; border: 0; background: rgba(93, 77, 30, 0.12); border-radius: 50%;
	}

/* PAGING */
.page-links .page-links-title { 
	letter-spacing: 1px; font-size: 12px; font-weight: 700; margin-right: 10px;
	}

/* COMMENT NAV */
.comment-nav {
	margin: 2em 0 4em;
	}

/* POST FORMATS */
.hentry .entry-header:before {
 	font-size: 20px; line-height: 1; color: #555; display: block; text-align: inherit; padding-bottom: 16px;
	}
/* sticky  */
.hentry.sticky .entry-header:before {
 	content: "\e81d";
	}
/* quote  */
.format-quote .entry-header:before {
 	content: "\e81a"
	}
.format-quote blockquote {
 	display: block; width: 100%; max-width: 480px; padding: 0; margin: .4em auto;
	}
/* video  */
.format-video .entry-header:before {
 	content: "\e816"
	}
/* audio  */
.format-audio .entry-header:before {
 	content: "\e885"
	}
/* image  */
.format-image .entry-header:before {
 	content: "\e86a"
	}
/* link  */
.format-link .entry-header:before {
 	content: "\e811"
    }
.format-link .entry-content {
    text-align: center;
    }
.format-link .entry-content > p:first-child a:first-child {
    font-size: 10px; font-weight: bold; word-break: break-all; padding: 1.2em 1.8em; text-transform: uppercase; letter-spacing: 2px; color: #fff; border: 0; background: #25262E; 	
	}
.format-link .entry-content > p:first-child a:first-child:hover {
    color: #aaa; 	
    }
/* chat  */
.format-chat .entry-header:before {
 	content: "\e813"
	}
.format-chat .entry-content {
	text-align: center;
	}
.chat-transcript {
	display: inline-block; text-align: left;
	}
.chat-row:after {
 	display: table; content: ""; line-height: 0;
	}
.chat-author {
	display: inline-block; width: 88px; margin-top: 5px;
	}
.chat-author cite {
 	display: inline-block; min-width: 80%; margin-right: .1em; font-size: 13px; font-style: normal;
	}
.chat-text {
	display: inline-block;
	}
.chat-text p {
    position: relative; display: inline-block; max-width: 460px; padding: 6px 12px; margin-top: 0; margin-bottom: 1em; background: #FFF; border-radius: 4px; font-size: 12px; line-height: 1.5; background-color: rgba(37, 38, 46, 0.86); color: #fff;
    }
.chat-text p:empty {
    display: none;
	}
.chat-speaker-2 .chat-text p {
	background: rgba(221, 221, 221, 0.35); color: #333;
    }
.chat-speaker-2 .chat-text p:before {
        background-color: #212933; 
    }
/* status  */
.format-status .entry-content { 
    text-align: center; 
    }
.format-status .entry-header:before {
 	content: "\e84e"
	}
.format-status .entry-content > p:first-child {
    display: inline-block; padding: .7em 1em; font-size: 1.0em; font-style: italic; line-height: 1.5; background: rgb(0 0 0 / 8%);
	}
/* aside  */
.format-aside .entry-header:before {
 	content: "\e81e"
	}
.format-aside .entry-content {
	margin-top: 2.2em; max-width: 440px; margin-left: auto; margin-right: auto; padding: 1.9em 2.4em 1.9em; line-height: 2;
	background-image: -webkit-linear-gradient(rgba(0, 0, 0, 0.08) 1px, transparent 1px);
	background-image: -moz-linear-gradient(rgba(0, 0, 0, 0.1) 1px, transparent 1px);
	background-image: -ms-linear-gradient(rgba(0, 0, 0, 0.1) 1px, transparent 1px);
	border: 1px solid rgba(51, 51, 51, 0.09);
	background-size: 100% 2.0em;
	background-position: 0 1.5em;
	background-origin: content-box;
	background-color: rgba(255, 255, 0, 0.15);
	}
.format-aside h3 {
	font-size: 14px; margin-bottom: .5em;
	}
.format-aside .entry-content p {
	margin-top: 0.1em;
	}
/* gallery  */
.format-gallery .entry-header:before {
 	content: "\e86a";
	}
.gallery {
	margin: 2em 0;
	width: 100%;
	width: -webkit-calc(100% + 18px);
	width:         calc(100% + 18px);
    }
/* fix for: Elementor Basic Gallery css conflict with collage layout */
.gallery.has-collage-layout .gallery-item {
	max-width: 100% !important;
	}
.gallery:first-child { margin-top: 0; }
.gallery:before,
.gallery:after {
 	content: " "; display: table;
	}
.gallery:after {
 	clear: both
	}
.gallery-item {
	display: block; position: relative; float: left; margin: 0 4px 4px 0;
    }
.gallery-item a {
	display: block; position: relative; float: left; /*margin: 0 4px 4px 0;*/ cursor: -webkit-zoom-in; cursor: -moz-zoom-in; cursor: zoom-in;
    }
.gallery-item a:before {
	content: ""; opacity: 0; position: absolute; width: 100%; height: 100%; top: 0; left: 0; bottom: 0; right: 0; text-align: center; overflow: hidden; background: #33475D; background: rgba(51, 71, 93, 0.15); 
	}
.gallery-item a:hover:before { 
	opacity: 1; 
	}
.gallery-item img {
	width: 100%;
    }
.gallery-columns-1 .gallery-item {
	max-width: 100%;
    }
.gallery-columns-2 .gallery-item {
	width: 48%;
	width: -webkit-calc(50% - 4px);
	width:         calc(50% - 4px);
    }
.gallery-columns-3 .gallery-item {
	width: 32%;
	width: -webkit-calc(33.3% - 4px);
	width:         calc(33.3% - 4px);
    }
.gallery-columns-4 .gallery-item {
	width: 23%;
	width: -webkit-calc(25% - 4px);
	width:         calc(25% - 4px);
    }
.gallery-columns-5 .gallery-item {
	width: 19%;
	width: -webkit-calc(20% - 4px);
	width:         calc(20% - 4px);
    }
.gallery-columns-6 .gallery-item {
	width: 15%;
	width: -webkit-calc(16.7% - 4px);
	width:         calc(16.7% - 4px);
    }
.gallery-columns-7 .gallery-item {
	width: 13%;
	width: -webkit-calc(14.28% - 4px);
	width:         calc(14.28% - 4px);
    }
.gallery-columns-8 .gallery-item {
	width: 11%;
	width: -webkit-calc(12.5% - 4px);
	width:         calc(12.5% - 4px);
    }
.gallery-columns-9 .gallery-item {
	width: 9%;
	width: -webkit-calc(11.1% - 4px);
	width:         calc(11.1% - 4px);
    }
.gallery-columns-1 .gallery-item:nth-of-type(1n),
.gallery-columns-2 .gallery-item:nth-of-type(2n),
.gallery-columns-3 .gallery-item:nth-of-type(3n),
.gallery-columns-4 .gallery-item:nth-of-type(4n),
.gallery-columns-5 .gallery-item:nth-of-type(5n),
.gallery-columns-6 .gallery-item:nth-of-type(6n),
.gallery-columns-7 .gallery-item:nth-of-type(7n),
.gallery-columns-8 .gallery-item:nth-of-type(8n),
.gallery-columns-9 .gallery-item:nth-of-type(9n) {
	margin-right: 0;
    }
.gallery-columns-1.gallery-size-medium figure.gallery-item:nth-of-type(1n+1),
.gallery-columns-1.gallery-size-thumbnail figure.gallery-item:nth-of-type(1n+1),
.gallery-columns-2.gallery-size-thumbnail figure.gallery-item:nth-of-type(2n+1),
.gallery-columns-3.gallery-size-thumbnail figure.gallery-item:nth-of-type(3n+1) {
	clear: left;
    }
.gallery .gallery-caption {
    position: absolute; bottom: 0; left: 0; right: 0; max-height: 45%; margin: 0; padding: 6px 8px; text-align: center; opacity: 0; font-size: .9rem; line-height: 1.4; overflow: hidden; z-index: 999; color: #fff;
	display: flex;
    min-height: 20%;
    align-items: flex-end;
    justify-content: center;
    padding-bottom: 16px;
	background: linear-gradient(0deg, rgb(0 0 0 / 74%) 0%, rgba(0,0,0,0) 100%);
	transition: opacity .2s;
    }
.gallery-caption:before {
	content: ""; height: 100%; min-height: 49px; position: absolute; top: 0; left: 0; width: 100%;
    }
.gallery-item:hover .gallery-caption {
    opacity: 1;
    }
.gallery-columns-7 .gallery-caption,
.gallery-columns-8 .gallery-caption,
.gallery-columns-9 .gallery-caption {
	display: none;
    }
	
/* Magnific Popup */
.mfp-bg {
	z-index: 2042;
	}
.mfp-wrap {
	z-index: 2043;
	}
.mfp-content {
    min-width: 240px;
	}
button.mfp-close,
button.mfp-arrow:before {
	background: url(https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/js/jquery.magnific-popup/default-skin.svg) 0 0 no-repeat; background-size: 264px 88px;
	}
button.mfp-close {
	width: 44px; height: 44px; color: transparent; background-position: 0 -44px;
	}
button.mfp-arrow { 
	width: 70px; height: 100px; opacity: .9;
	}
button.mfp-arrow:hover,
button.mfp-close:hover { 
	background-color: transparent; 
	}
button.mfp-arrow:after { 
	display: none; 
	}
button.mfp-arrow:before {
	content: ''; position: absolute; top: 35px; height: 30px; width: 32px; border: 0; margin: 0;
	}
button.mfp-arrow-left:before {
	left: 6px; background-position: -138px -44px; 
	}
button.mfp-arrow-right:before {
	left: auto; right: 6px; background-position: -94px -44px; 
	}
.mfp-title {
	position: absolute; width: 100%; line-height: 32px; text-transform: uppercase; font-size: 11px; padding-right: 0; text-align: center; font-weight: 400; letter-spacing: .08em;
	}
.mfp-counter {
	position: fixed; top: 10px; left: 0; text-align: center; font-size: 12px; line-height: 1.6; font-weight: 400; color: #fff; opacity: .5;
	}
.mfp-removing button,
.mfp-removing .mfp-title { 
	opacity: 0; 
	}
body .mfp-figure:after {
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
	}	
.mfp-iframe-holder .mfp-close {
	background: none; 
	}
.mfp-zoom-in .mfp-content {  
	opacity: 0; transition: all 0.2s ease-out; transform: scale(0.9);
	}
.mfp-zoom-in.mfp-bg {
	opacity: 0; transition: all 0.2s ease-out;
	}
.mfp-zoom-in.mfp-image-loaded .mfp-content {
	opacity: 1; -webkit-transform: scale(1); transform: scale(1);
	}
div.mfp-bg { 
	background: #25262E;
	}
.mfp-zoom-in.mfp-ready.mfp-bg {
	opacity: .95;
	}
	.mfp-zoom-in.mfp-image-loaded .mfp-content {
		opacity: 1;
		}
.mfp-zoom-in.mfp-removing .mfp-content {
	-webkit-transform: scale(0.9); transform: scale(0.9); opacity: 0;
	}
.mfp-zoom-in.mfp-removing.mfp-bg {
	opacity: 0;
	}
.mfp-preloader {
	width: 24px; height: 24px; opacity: 1; margin: 0 auto; background: url(../images/bckg/loading-light.gif) center center no-repeat; background-size: 24px 24px;
	}
.mfp-zoom-in.mfp-ready .mfp-preloader {
	display: block;
	}
.mfp-zoom-in.mfp-image-loaded .mfp-preloader,
.mfp-zoom-in.mfp-removing .mfp-preloader {
	display: none;
	}
/* fade image change */
.mfp-image-loaded .mfp-content {
	-webkit-animation-name: fadeIn; animation-name: fadeIn; -webkit-animation-duration: .2s; animation-duration: .2s; -webkit-backface-visibility: hidden;
	}
@-webkit-keyframes fadeIn{
	0%{ opacity:0 }
	100%{ opacity:1 }
	}
@keyframes fadeIn{
	0%{ opacity:0 }
	100%{ opacity:1 }
	}
/* soundcloud */ 
.is-soundcloud .mfp-content {
	max-width: 550px;
	}
.is-soundcloud .mfp-iframe-scaler {
	padding-top: 100%;
	}	
	
/* GALLERY COLLAGE LAYOUT */
.gallery {
	margin: 2em -8px; margin-bottom: 12px; padding: 8px; padding-top: 0; font-size: 0;
	}
.pw-collage-loading {
	background-color: #fff; min-height: 120px; background: url(../images/bckg/loading.gif) 50% 60px no-repeat; background-size: 88px 8px;
	}
.gallery figure {
	display: block; opacity:0; 
	}
/* fix for: images not visible when editing gallery in Elementor */
body.elementor-editor-active .gallery figure {
	opacity:1; 
	}
.no-csstransforms3d .gallery figure {
	opacity: 1 !important;
	}
.gallery img {
 	display: block; width: 100%;
	}

/*
from transitions.css
A library of transitions for revealing the loaded images
(Heavily) Inspired by http://tympanus.net/codrops/2013/07/02/loading-effects-for-grid-items-with-css-animations/
*/
.gallery {
    -webkit-perspective: 1300px;
    -moz-perspective: 1300px;
    perspective: 1300px;
    }
	
/* EFFECT 1 */
.effect-1 {
    -webkit-transform-style: preserve-3d;
    -moz-transform-style: preserve-3d;
    transform-style: preserve-3d;
    -webkit-transform-origin: 50% 50% -300px;
    -moz-transform-origin: 50% 50% -300px;
    transform-origin: 50% 50% -300px;
    -webkit-transform: rotateX(-180deg);
    -moz-transform: rotateX(-180deg);
    transform: rotateX(-180deg);
    -webkit-animation: fly ease-in-out forwards;
    -moz-animation: fly ease-in-out forwards;
    animation: fly ease-in-out forwards;
}
@-webkit-keyframes fly {
    100% { -webkit-transform: rotateX(0deg); opacity: 1; -webkit-transform-origin:50% 50% 0; }
}
@-moz-keyframes fly {
    100% { -moz-transform: rotateX(0deg); opacity: 1; -moz-transform-origin:50% 50% 0; }
}
@keyframes fly {
    100% { transform: rotateX(0deg); opacity: 1; transform-origin:50% 50% 0; }
}

/* EFFECT 2 */
.effect-2 {
    -webkit-transform: translateY(200px);
    -moz-transform: translateY(200px);
    transform: translateY(200px);
    -webkit-animation: moveUp ease forwards;
    -moz-animation: moveUp ease forwards;
    animation: moveUp ease forwards;
}
@-webkit-keyframes moveUp {
    to { -webkit-transform: translateY(0); opacity: 1; }
}
@-moz-keyframes moveUp {
    to { -moz-transform: translateY(0); opacity: 1; }
}
@keyframes moveUp {
    to { transform: translateY(0); opacity: 1; }
}

/* EFFECT 3 */
.effect-3 {
    -webkit-transform-style: preserve-3d;
    -moz-transform-style: preserve-3d;
    transform-style: preserve-3d;
    -webkit-transform: translateZ(400px) translateY(300px) rotateX(-90deg);
    -moz-transform: translateZ(400px) translateY(300px) rotateX(-90deg);
    transform: translateZ(400px) translateY(300px) rotateX(-90deg);
    -webkit-animation: fallPerspective ease-in-out forwards;
    -moz-animation: fallPerspective ease-in-out forwards;
    animation: fallPerspective ease-in-out forwards;
}

@-webkit-keyframes fallPerspective {
    100% { -webkit-transform: translateZ(0px) translateY(0px) rotateX(0deg); opacity: 1; }
}
@-moz-keyframes fallPerspective {
    100% { -moz-transform: translateZ(0px) translateY(0px) rotateX(0deg); opacity: 1; }
}
@keyframes fallPerspective {
    100% { transform: translateZ(0px) translateY(0px) rotateX(0deg); opacity: 1; }
}

/* EFFECT 4 */
.effect-4 {
    -webkit-transform-style: preserve-3d;
    -moz-transform-style: preserve-3d;
    transform-style: preserve-3d;
    -webkit-transform-origin: 0% 0%;
    -moz-transform-origin: 0% 0%;
    transform-origin: 0% 0%;
    -webkit-transform: rotateX(-80deg);
    -moz-transform: rotateX(-80deg);
    transform: rotateX(-80deg);
    -webkit-animation: flip ease-in-out forwards;
    -moz-animation: flip ease-in-out forwards;
    animation: flip ease-in-out forwards;
}

@-webkit-keyframes flip {
    100% { -webkit-transform: rotateX(0deg); opacity: 1; }
}
@-moz-keyframes flip {
    100% { -moz-transform: rotateX(0deg); opacity: 1; }
}
@keyframes flip {
    100% { transform: rotateX(0deg); opacity: 1; }
}

/* EFFECT 5 */
.effect-5 {
    -webkit-transform-style: preserve-3d;
    -moz-transform-style: preserve-3d;
    transform-style: preserve-3d;
    -webkit-transform: rotateY(-180deg);
    -moz-transform: rotateY(-180deg);
    transform: rotateY(-180deg);
    -webkit-animation: moveUp ease-in-out forwards;
    -moz-animation: moveUp ease-in-out forwards;
    animation: moveUp ease-in-out forwards;
}

/* EFFECT 6 */
.effect-6 {
    -webkit-transform:  scale(0.638) translate(-179px);
    -moz-transform:     scale(0.638) translate(-179px);
    transform:          scale(0.638) translate(-179px);
    -webkit-animation: moveUp ease-in-out forwards;
    -moz-animation: moveUp ease-in-out forwards;
    animation: moveUp ease-in-out forwards;
}

/* Universal durations */
.effect-duration-1{
    -webkit-animation-duration: .4s;
    -moz-animation-duration:    .4s;
    animation-duration:         .4s;
}
.effect-duration-2{
    -webkit-animation-duration: .5s;
    -moz-animation-duration:    .5s;

    animation-duration:         .5s;
}
.effect-duration-3{
    -webkit-animation-duration: .6s;
    -moz-animation-duration:    .6s;
    animation-duration:         .6s;
}
.effect-duration-4{
    -webkit-animation-duration: .7s;
    -moz-animation-duration:    .7s;
    animation-duration:         .7s;
}
.effect-duration-5{
    -webkit-animation-duration: .8s;
    -moz-animation-duration:    .8s;
    animation-duration:         .8s;
}
.effect-duration-6{
    -webkit-animation-duration: .9s;
    -moz-animation-duration:    .9s;
    animation-duration:         .9s;
}
.effect-duration-7{
    -webkit-animation-duration: .95s;
    -moz-animation-duration:    .95s;
    animation-duration:         .95s;
}
.effect-duration-8{
    -webkit-animation-duration: 1s;
    -moz-animation-duration:    1s;
    animation-duration:         1s;
}
.effect-duration-9{
    -webkit-animation-duration: 1.05s;
    -moz-animation-duration:    1.05s;
    animation-duration:         1.05s;
}
.effect-duration-10{
    -webkit-animation-duration: 1.1s;
    -moz-animation-duration:    1.1s;
    animation-duration:         1.1s;
}




/* WIDGET AREA */
.widget-area .widget { 
    margin: 0 0 4em 0;
    }
.widget-area .widget:empty {
    margin: 0 !important;
	}
.sidebar.widget-area .widget:last-child {
	margin-bottom: 0;
    }
	
/* SIDEBAR WIDGETS ALIGN CENTER */
.is-sidebar-align-center .sidebar .widget,
.is-sidebar-align-center .sidebar input { 
    text-align: center;
    } 

/* FOOTER WIDGETS ALIGN CENTER */
.is-footer-widgets-align-center .footer-widgets .widget { 
    text-align: center;
    }
	

/* WIDGET TITLE ALIGN CENTER */
.is-widget-title-align-center .site-main .widget-title { 
    text-align: center;
    }

/* BLOG SINGLE WIDGET TITTLES */
.single .site-content .widget-title span {
	display: inline-block;
	}
	
.widget-area a:not(.button) { 
	color: inherit;
    }
.sidebar.widget-area a:not(.button):hover { 
    color:#000; 
    }
.widget-title { 
    margin-bottom: 20px; font-size: 11px; letter-spacing: 2px; line-height: 1.3; font-weight: 400; text-transform: uppercase;
	}
.widget-title span {
	display: inline-block;
	}
.sidebar .widget-title { 
    display: block; margin-bottom: 30px; position: relative;
		}
.sidebar .widget-title,
.sidebar .widget-title a { 
    	color: inherit;
		}
		

/* RIBBON STYLE */
.is-widget-ribbon .site-main .widget-title span { 
	display: inline-block; position: relative; line-height: 1.3; padding: 6px 20px; color: #fff; background: #333;
		}
		.is-widget-ribbon .site-main .widget-title span:before {
			content: ""; position: absolute; display: block; bottom: 0; border: 13px solid #333; left: -1.2em; border-right-width: 14px; border-left-color: transparent !important;
			}
		.is-widget-ribbon .site-main .widget-title span:after {
			content: ""; position: absolute; display: block; bottom: 0; border: 13px solid #333; right: -1.2em; border-left-width: 14px; border-right-color: transparent !important;
			}			

/* BORDER STYLE */
.is-widget-border .site-main .widget-title span {
	display: block; padding: 16px 20px; border: 2px solid #333;
	}

/* BORDER ARROW STYLE */
.is-widget-border-arrow .site-main .widget-title span {
	position: relative; display: block; padding: 16px 20px; border: 2px solid #333;
	}
.is-widget-border-arrow .site-main .widget-title span:after, 
.is-widget-border-arrow .site-main .widget-title span:before {
	top: 100%; left: 30px; border: solid transparent; content: " "; height: 0; width: 0; position: absolute; pointer-events: none;
	}
.is-widget-border-arrow .site-main .widget-title span:after {
	border-color: rgba(255, 255, 255, 0); border-top-color: #fff; border-width: 6px; margin-left: -6px;
	}
.is-widget-border-arrow .site-main .widget-title span:before {
	border-color: rgba(51, 51, 51, 0); border-top-color: #333; border-width: 9px; margin-left: -9px;
	}
.is-widget-border-arrow.is-widget-title-align-center .site-main .widget-title span:after, 
.is-widget-border-arrow.is-widget-title-align-center .site-main .widget-title span:before {
	left: 50%;
	}			

/* SOLID STYLE */
.is-widget-solid .site-main .widget-title span {
	display: block;	padding: 16px 20px; background: #333; color: #fff;
	}		

/* SOLID ARROW STYLE */
.is-widget-solid-arrow .site-main .widget-title span {
	position: relative; display: block; padding: 16px 20px; background: #333; color: #fff;
	}	
.is-widget-solid-arrow .site-main .widget-title span:after {
	top: 100%; left: 30px; border: solid transparent; content: " "; height: 0; width: 0; position: absolute; pointer-events: none; border-color: rgba(51, 51, 51, 0); border-top-color: #333;	border-width: 6px; margin-left: -6px; z-index: 1;
	}	
.is-widget-solid-arrow.is-widget-title-align-center .site-main .widget-title span:after {
	left: 50%;
	}

/* UNDERLINE STYLE */
.is-widget-underline .site-main .widget-title span {
	box-shadow: inset 0 -7px 0 #FFFC7F;
	}		

/* BOTTOMLINE STYLE */
.is-widget-bottomline .site-main .widget-title span {
	display: block !important;
	}		
.is-widget-bottomline .site-main .widget-title:after {
	content: ""; display: inline-block; width: 32px; margin-top: 20px; border-bottom: 4px solid;
	}		

/* FIRST LETTER BORDER STYLE */
.is-widget-first-letter-border .site-main .widget-title span:first-letter {
	padding: 8px 11px; font-size: 14px; font-weight: bold; margin-right: 7px; letter-spacing: 0; border: 3px solid;
	}

/* FIRST LETTER SOLID STYLE */
.is-widget-first-letter-solid .site-main .widget-title span:first-letter {
	padding: 10px 13px; font-size: 14px; font-weight: bold; margin-right: 7px; letter-spacing: 0; background: #333; color: #fff;
	}

/* LINE CUT STYLE */
.is-widget-line-cut .site-main .widget-title span:before,
.is-widget-line-cut .site-main .widget-title span:after {
	content: ''; display: inline-block; width: 44px; margin: 0 -2px; border-bottom: 2px solid;
	}

/* LINE CUT CENTER STYLE */
.is-widget-line-cut-center .site-main .widget-title span:before,
.is-widget-line-cut-center .site-main .widget-title span:after {
	content: ''; display: inline-block; width: 24px; margin: 3px 11px 3px 0; border-bottom: 2px solid;
	}
.is-widget-line-cut-center .site-main .widget-title span:after {
	margin: 3px 0 3px 11px;
	}








/* SPLASH STYLE */
.is-widget-splash .site-main .widget-title span {
	padding: 0 16px;
	}
.is-widget-splash .site-main .widget-title:after {
	display: block; position: relative; z-index: -1; margin-top: 18px; line-height: 0; font-size: 80px; font-weight: 400; font-family: "fontello"; content: '\e8c2'; 
	}
	
/* SPLASH 2 STYLE */
.is-widget-splash2 .site-main .widget-title span {
	padding: 0 16px;
	}
.is-widget-splash2 .site-main .widget-title:after {
	display: block; position: relative; z-index: -1; margin-top: 28px; text-indent: -20px; line-height: 0; font-size: 220px; font-weight: 400; font-family: "fontello"; content: '\e8c3'; 
	}

/* COFFEE DROP STYLE */
.is-widget-coffee-drop .site-main .widget-title span {
	padding: 4px; background: #fff;
	}
.is-widget-coffee-drop .site-main .widget-title:after {
	display: block; position: relative; z-index: -1; margin-top: 15px; line-height: 0; font-size: 180px; font-weight: 400; font-family: "fontello"; content: '\e8bb';
	}

/* CURVED LINES STYLE */
.is-widget-curved-lines .site-main .widget-title:after {
	display: block; position: relative; z-index: -1; margin-top: 12px; line-height: 0; font-size: 104px; font-weight: 400; font-family: "fontello"; content: '\e8be';
	}

/* MUSTACHE STYLE */
.is-widget-mustache .site-main .widget-title:after {
	display: block; position: relative; z-index: -1; margin-top: 24px; line-height: 0; font-size: 44px; font-weight: 400; font-family: "fontello"; content: '\e8bc';
	}


/* WIDGET TITLE BG/BORDER COLOR */
/*.is-widget-ribbon .site-main .widget-title span,
.is-widget-solid .site-main .widget-title span,
.is-widget-solid-arrow .site-main .widget-title span,
.is-widget-first-letter-solid .site-main .widget-title span:first-letter { 
	background: #333;
	}
.is-widget-ribbon .site-main .widget-title span:before,
.is-widget-ribbon .site-main .widget-title span:after,
.is-widget-border .site-main .widget-title span,
.is-widget-border-arrow .site-main .widget-title span,
.is-widget-bottomline .widget-title:after,
.is-widget-first-letter-border .site-main .widget-title span:first-letter,
.is-widget-line-cut .site-main .widget-title span:before,
.is-widget-line-cut .site-main .widget-title span:after,
.is-widget-line-cut-center .site-main .widget-title span:before,
.is-widget-line-cut-center .site-main .widget-title span:after { 
	border-color: #333;
	}
.is-widget-border-arrow .site-main .widget-title span:before,
.is-widget-solid-arrow .site-main .widget-title span:after {
	border-top-color: #333;
	}
.is-widget-underline .site-main .widget-title span {
	box-shadow: inset 0 -6px 0 #333;
	}	
.site-main .widget-title:after {
	color: #bdf3ce;
	}*/
	


/* TRENDING POSTS STYLE */
.is-trending-posts-rounded .tptn_posts_widget img {
	border-radius: 50%;
	}
	

/* .widget-area ul li { 
    font-size: 12px; 
    } */
.widget-area ul ul ul li {
    font-size: inherit;
	}
.widget-area a:active { 
    color: #aaa;
    }
.widget label input[type=checkbox],
.widget label input[type=radio] {
	font-size: 13px !important;	max-width: 240px !important; padding: .9em 1em !important;
	}
.widget form p { 
	margin-top: 0; 
	}
.widget-area iframe {
 	width: 100% !important;
	}
.adsbygoogle iframe {
    max-height: none;
	}
/* Buttons in widgets */
.widget input[type=submit], 
.widget input[type=button], 
.widget button, 
.widget a.button {
	font-size: 11px;
	}

/* SOCIAL LINKS IN SIDEBAR AND FOOTER */
.widget_theblogger_widget_social_media_icon,
.widget_pixelwars_core_widget_social_media_icon {
    display: inline-block; margin-right: 2px !important;
	}

/* DEFAULT WIDGETS */
.widget-area ul { 
    margin: 0 0 1em 0; padding-left: 0; list-style: none;
    }
.widget-area ul li { 
    position:relative; margin-bottom:.4em; line-height:1.5; 
    }
.widget-area ul ul {
    margin: 0.6em 0 0;
	}

/* Search Widget */
.widget_search .search-form,
.wp-block-search__inside-wrapper,
.learn-press-search-course-form {
	position: relative;
	}
.widget_search .search-form .search-field,
.wp-block-search__input,
html form[name="search-course"] .search-course-input {
	max-width: none; padding: 16px 40px 16px 24px; border-color: rgba(0, 0, 0, 0.1); border-width: 1px; border-radius: 32px;
	}
.widget_search .search-form .search-field::placeholder,
.wp-block-search__input::placeholder,
form[name="search-course"] .search-course-input::placeholder {
  	color: rgba(0, 0, 0, 0.6);
	}
.widget_search input.search-submit,
.wp-block-search__button,
form[name="search-course"] .search-course-button {
    position: absolute; top: 0; right: 0; margin: 0; width: 1em; height: 100%; opacity: 0;
	}
.widget_search .search-form label:after,
.wp-block-search__inside-wrapper:after,
form[name="search-course"]:after {
	position: absolute; top: 7px; right: 0; padding-right: 14px; content: ''; font-family: fontello; font-size: 20px; content: "\e802"; z-index: -1;
	}
.wp-block-search__inside-wrapper {
    margin-bottom: 1.6em;
	}
.wp-block-search .wp-block-search__input {
	max-width: 100%;
	}
.wp-block-search .wp-block-search__input {
	padding: 16px; border-color: #00000026;
	}

/* ANIMATED SEARCH WIDGET */
.pw-search {
	display: table;
  	}
.pw-search input {
	background: none; border: none; outline: none; padding: 0 28px 0 0; width: 0px; min-width: 0; z-index: 1; position: relative; line-height: 18px; margin: 5px 0; font-size: 14px; -webkit-appearance: none; transition: all .6s ease; cursor: pointer; color: inherit;
  	}
.pw-search input + div {
	position: relative; height: 28px; width: 100%; margin: -28px 0 0 0;
 	}
.pw-search input + div svg {
	display: block; position: absolute; height: 28px; width: 160px; right: 0; top: 0; fill: none; stroke: currentColor; stroke-width: 1.5px; stroke-dashoffset: 271.908; stroke-dasharray: 59 212.908; transition: all .6s ease;
	}
.pw-search input:focus {
	width: 160px; padding: 0 4px; cursor: text;
	}
.pw-search input:focus + div svg {
	stroke-dasharray: 150 212.908; stroke-dashoffset: 300;
	}
.pw-search ::-webkit-input-placeholder { opacity: 0.2; }
.pw-search ::-moz-placeholder { opacity: 0.2;  }
.pw-search ::-ms-placeholder { opacity: 0.2;  }

/* recent entries widget */
.widget_recent_entries ul,
.widget_pages ul { 
    margin-left: 0; list-style: none;
    }
/* .widget_recent_entries ul li a,
.widget_pages ul li a,
.widget_most_recommended_posts ul li a { 
    margin: 0; display: inline-block; padding: .8em 0 .6em 0; font-size: 18px; letter-spacing: .03em; border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    } */

/* categories widget */
.widget_categories ul,
.widget_nav_menu,
.widget_product_categories ul { 
    list-style: none; margin-left: 0; 
    }
.widget_product_categories ul ul {
	margin-top: .3em;
	}
.widget_categories ul ul,
.widget_nav_menu ul ul {
    margin-bottom: .6em;
	}
.widget_categories ul li,
.widget_recent_entries ul li,
.widget_archive ul li,
.widget_pages ul li,
.widget_nav_menu ul li,
.widget_recent_comments ul li,
.widget_product_categories ul li,
.widget_meta ul li,
.widget_rss ul a.rsswidget {
    margin: 0px; font-size: 15px; font-weight: 500; display: block; margin-bottom: .6em;
    }
.widget_categories ul ul li,
.widget_archive ul ul li,
.widget_nav_menu ul ul li,
.widget_pages ul ul li,
.widget_product_categories ul ul li {
    font-size: 15px; margin-left: 5%;
    }
.widget_categories ul ul ul li,
.widget_nav_menu ul ul ul li,
.widget_archive ul ul ul li,
.widget_pages ul ul ul li,
.widget_product_categories ul ul ul li {
    font-size: 15px; margin-left: 4%;
    }


/* woocommerce widgets */
.woocommerce ul.cart_list, 
.woocommerce ul.product_list_widget {
    text-align: left;
	}
.sidebar ul.product_list_widget li .star-rating {
	width: 5.4em; margin: 10px 0;
	}
.woocommerce ul.cart_list li img, 
.woocommerce ul.product_list_widget li img {
	width: 62px;
	}
.widget_product_search input[type='submit'] {
    margin-top: 18px;
	}
.woocommerce ul.cart_list li, 
.woocommerce ul.product_list_widget li {
    padding: 0 0 8px 0; margin-bottom: 14px; border-bottom: 1px solid #eee;
	}
.woocommerce ul.cart_list li:last-child, 
.woocommerce ul.product_list_widget li:last-child {
	border-bottom: 0;
	}
.woocommerce .widget_shopping_cart .total, 
.woocommerce.widget_shopping_cart .total {
    border: 0; background: #fafafa; padding: 12px;
	}

/* recent comments widget */
.widget_recent_comments ul li > a {
	display: block;
	}
.widget_recent_comments ul li a {
    font-size: 13px; font-weight: 500; text-decoration: underline;
    }
.widget_recent_comments ul li a:not(.url) {
    text-transform: uppercase;
    }


/* about me widget */
.widget_theblogger_widget_about_me img,
.widget_pixelwars_core_widget_about_me img { 
	display: block; margin: 0 0 1.2em;
    }
.widget_theblogger_widget_about_me .is-about-me-widget-round img,
.widget_pixelwars_core_widget_about_me .is-about-me-widget-round img { 
	border-radius: 50%; max-width: 160px;
    }
.is-sidebar-align-center .sidebar .widget_theblogger_widget_about_me img,
.is-sidebar-align-center .sidebar .widget_pixelwars_core_widget_about_me img { 
	margin-left: auto; margin-right: auto;
    }
.widget_theblogger_widget_about_me .social-link,
.widget_pixelwars_core_widget_about_me .social-link {
	width: 17px; line-height: 25px; color: #666; border: 0;
	}
.widget_theblogger_widget_about_me .social-link:hover,
.widget_pixelwars_core_widget_about_me .social-link:hover {
	background: none; color: inherit;
	}
.widget_theblogger_widget_about_me a,
.widget_pixelwars_core_widget_about_me a {
	border-bottom: 2px solid rgb(0 0 0 / 9%);
	}
.widget_theblogger_widget_about_me a:hover,
.widget_pixelwars_core_widget_about_me a:hover {
	border-color: #555;
	}

/* rss widget */
.widget_rss .rss-date, 
.widget_rss cite:before { 
    opacity: .5;
    }
.widget_rss .rss-date {
	display: block; font-size: .925em; 
    }
.widget_rss ul { 
	list-style: none; margin: 0;
    }
.widget_rss ul li { 
    padding-bottom: 1.4em; line-height: 1.8;
    }
.widget_rss ul li a.rsswidget { 
	display: inline;
    }
.widget_rss .widget-title img { 
    margin-right:.1em; 
    }
.widget_rss cite { 
    display:block; margin-top:.6em;
    }
.widget_rss cite:before { 
    content:"by "; 
    }
.widget_rss .rssSummary { 
    padding-top: 1em; 
    }
.rss-widget-icon {
    display: none;
	}

/* tag cloud widget */
.tagcloud {
	text-align: left;
	}
.sidebar .tagcloud {
	text-align: inherit;
	}
.tagcloud a,
.tagged_as a { 
    color: inherit; opacity: .7; font-size: 13px !important; margin: 0 8px 8px 0; display: inline-block; line-height: 1;
    }
.tagcloud a:before,
.tagged_as a:before {
	content: '#';
    }
.tagcloud a:hover,
.tagged_as a:hover { 
	opacity: 1;
    }

/* SOLID */
.is-tagcloud-solid .tagcloud a { 
    margin: 0 2px 8px 0; padding: .6em .9em; color: inherit; background-color: rgba(99, 92, 83, 0.1);
    }
.is-tagcloud-solid .tagcloud a:hover { 
    color: #111; background-color: rgba(99, 92, 83, 0.15);
    }
.is-tagcloud-solid .tagcloud a:before {
	content: '';
    }

/* calendar widget */
.wp-calendar-table { 
    width: 100%; margin-bottom: 0; font-size: 14px; table-layout: fixed;
    }
.wp-calendar-table caption { 
    margin-bottom: 6px;
    }
.widget_calendar .wp-calendar-table thead { 
    display: none;
    }
.wp-calendar-table table caption { 
    padding-bottom: 8px; font-size: 16px;
    }
.wp-calendar-table table tr td, 
.wp-calendar-table table tr th { 
    padding: .75em 0;
    }
.wp-calendar-table tr td { 
    text-align: center;
    }
.wp-calendar-table tbody a { 
    font-weight: bold; 
    }
.wp-calendar-table tfoot { 
    font-size: 13px; 
    }
.wp-calendar-table tfoot a { 
	color: #555; 
    }

/* CUSTOM WIDGETS */

/* SOCIAL FEED */
.social-feed ul {
    margin-left: 0; list-style: none; 
    }
.social-feed ul li,
.flickr_badge_image { 
    display:inline-block; padding: 0 4px 4px 0; font-size: 0;
    }
.social-feed ul li img,
.flickr_badge_image img { 
    max-height: 100%; 
    }

/* WP Instagram Widget */
.sidebar .instagram-pics {
	margin: -8px; overflow: auto;
	}
.sidebar .instagram-pics li {
	display: block; width: 50%; float: left; margin: 0; padding: 8px;
	}
.sidebar .instagram-pics + p a {
	padding: 10px 16px;
	}

/* TOP10 Popular Posts Widget */
.tptn_posts_widget ul {
	list-style: none; margin: 0;
	}
.tptn_posts_widget ul li {
	display: block; clear: left; overflow: hidden; margin-bottom: 1em; display: flex; align-items: center; justify-content: start;
	}
.tptn_title {
	display: block; font-size: 16px; font-weight: 500;
	}
.tptn_posts_widget img {
	float: left; max-width: 144px;
	}
.tptn_after_thumb {
	padding-left: 16px;
	}
.tptn_posts_widget .tptn_link {
	display: block;
	}
.tptn_posts_widget .tptn_after_thumb > span,
.widget_recent_entries .post-date {
	font-size: 11px; opacity: .6; text-transform: uppercase; letter-spacing: .07em;
	}
.widget_recent_entries .post-date {
	display: block; margin-top: 2px;
	}
.tptn_counter {
    font-size: 10px; text-transform: uppercase; letter-spacing: 1px; font-weight: bold; opacity: .4;
	}
.woocommerce + .tptn_counter {
    margin-top: 2rem; clear: left;
	}
/* TOP10 in footer */
.site-footer .tptn_title {
	font-size: 15px; font-weight: 500; display: block; margin-top: 6px;
	}

/* NEWSLETTER FORM IN SIDEBAR */
.widget_mc4wp_form_widget {
	padding: 4px;
    background-image: repeating-linear-gradient(135deg, #ff6969, #ff6969 10px, #FFF 0px, #FFF 20px, #85adff 0px, #85adff 30px, #FFF 0px, #FFF 40px);
	}
.sidebar .mc4wp-form,
.footer-widgets .mc4wp-form,
.site-header .mc4wp-form {
	padding: 26px; background: #faf8ef;
	}
.sidebar .mc4wp-form p:last-of-type {
	margin-bottom: 0;
	}
.mc4wp-form label.error {
	top: 36px; bottom: auto;
	}




/* COOKED RECIPE PLUGIN STYLES */
/* recipe gallery */
.cooked-recipe-gallery .fotorama__caption__wrap {
	background: #ffffff; color: #222; padding: 12px 16px; line-height: 1; border-radius: 7px 7px 0 0; box-shadow: 0 -3px 6px rgba(0, 0, 0, 0.1);
	}
.fotorama__caption {
	font-family: inherit; text-align: center; font-size: 13px; letter-spacing: .03em;
	}
.fotorama__dot {
	width: 8px; border: 1px solid #dfdfdf; height: 8px; border-radius: 50%;
	}
.fotorama__nav__frame.fotorama__active .fotorama__dot {
	border-color: #333; border-width: 5px;
	}
.fotorama__nav-wrap {
	padding-top: 6px;
	}

/* ingredients */
.entry-content .cooked-recipe-ingredients {
	padding: 12px 24px; border: 1px solid #f2ece3;
	}

/* directions */
.cooked-recipe-directions .cooked-direction {
    margin-bottom: 14px;
	}
.cooked-single-direction + .cooked-single-direction.cooked-heading {
    margin-top: 30px;
	}
.cooked-recipe-directions .cooked-direction-number {
	font-size: 30px; font-weight: 700; background: #a8a8a8; opacity: 1; color: #fff; line-height: 1; height: 26px; min-width: 24px; text-align: center; margin-left: -8px; margin-top: 6px;
	}
.cooked-recipe-info strong {
	font-size: 11px; letter-spacing: 2px;
	}
.cooked-category a {
	text-transform: uppercase; font-size: 10px; letter-spacing: 1px; font-weight: 700; color: inherit; background: #feeebe; padding: 4px 12px; border-radius: 14px; display: inline-block;
	}
.cooked-recipe-info strong {
	margin-bottom: 2px;
	}
.cooked-recipe-info span.cooked-time-icon {
	opacity: .3;
	}
.cooked-recipe-info span.cooked-servings-icon {
	opacity: .4;
	}
.cooked-servings a, .cooked-author a {
	text-transform: uppercase; font-size: 11px; letter-spacing: 1px;	
	}
/* nutrition */	
.cooked-nutrition-label .cooked-nutrition-title {
	font-weight: 700;
	}
.cooked-nutrition-label dt {
	display: block;
	}
body .cooked-nutrition-label strong.cooked-nut-heading {
	font-size: 11px; text-transform: uppercase; opacity: .6; letter-spacing: 1px;
	}
body .cooked-nutrition-label strong.cooked-nut-heading,
body .cooked-nutrition-label strong {
	font-weight: 700;
	}
.cooked-nutrition-label {
	border-radius: 0; border-width: 4px;
	}
.widget .cooked-nutrition-label {
	border-width: 2px;padding: 30px; font-size: 12px !important;
	}
body .cooked-nutrition-label dt.cooked-nut-servings p,
body .cooked-nutrition-label dt.cooked-serving-size strong, 
body .cooked-nutrition-label dt.cooked-serving-size strong.cooked-nut-label {
	font-size: 1em;
	}
body .cooked-nutrition-label dt.cooked-calories strong,
body .cooked-nutrition-label dt.cooked-calories strong.cooked-nut-label{
    font-size: 1.1em;	
	}
.widget .cooked-nutrition-label .cooked-nutrition-title {
	font-size: 14px; text-transform: uppercase; letter-spacing: 1px; margin-bottom: 1.4rem;
	}
body .cooked-nutrition-label p.cooked-daily-value-text {
	font-size: 12px; opacity: .6;
	}
body .cooked-nutrition-label dt.cooked-nut-spacer {
	background: #f2ece3; height: 3px;
	}
body .cooked-nutrition-label dt,
body .cooked-nutrition-label dl.cooked-nut-bottom dt,
body .cooked-nutrition-label .cooked-nut-hr,
.cooked-nutrition-label {
	border-color: #f2ece3;
	}
body .cooked-nutrition-label .cooked-nut-hr {
	border-top-width: 8px;
	}
body .cooked-nutrition-label p > strong.cooked-nut-label {
    font-weight: 700;
	}
/* fullscreen mode */
.cooked-fsm .cooked-fsm-top {
	text-transform: uppercase; font-size: 16px; font-weight: 700;
	}
.cooked-fsm .cooked-fsm-directions p {
	font-size: 1.1rem; max-width: 700px;
	}
/* single recipe : print and fs icons */
.cooked-recipe-info span.cooked-print .cooked-icon,
.cooked-recipe-info span.cooked-fsm-button .cooked-icon {
    font-size: 20px !important;
 }
.cooked-recipe-info>section.cooked-right>span {
    margin-left: 20px;
	}
	
	
/* WP RECIPE MAKER PLUGINS STYLES */
.wprm-recipe-instruction-image {
	margin: 18px 0; 
	}
.wprm-recipe-container .wprm-recipe-tastefully-simple, 
.wprm-print .wprm-recipe-tastefully-simple {
	font-family: inherit; font-size: 1rem;
	}	






/* BLOG SINGLE */
.single .entry-content {
	clear: both; margin-bottom: 1em;
	}
.single .entry-content p:last-child {
	margin-bottom: 0;
	}
.single .entry-meta.below-content {
	margin: 3em 0;
	}
	
/* POST TAGS */
.post-tags { 
	margin: 2em 0; 
	}	
	
/* NAV SINGLE */	
.nav-single {
	margin: 2em 0; overflow: hidden;
	}
	.nav-single:last-child {
		margin-bottom: 0;
		}
	.nav-single > div {
		position: relative; padding: 2em 0; text-align: left;
		}
	.nav-overlay-link {
		position: absolute; top: 0; right: 0; bottom: 0; left: 0; display: block; text-indent: -200%; font-size: 0; overflow: hidden; z-index: 4;
		}
	.nav-single .nav-image-link {
		display: block; width: 110px; float: left;
		}
	.nav-single .nav-next .nav-image-link {
		float: right;
		}
	.nav-single .nav-image-link img {
		width: 110px; height: 110px; object-fit: cover;
		}
	.nav-single .nav-desc {
		margin-top: 1em; position: relative; z-index: 2;
		}
	.nav-previous .nav-image-link + .nav-desc {
		margin-left: 140px;
		}
	.nav-next .nav-image-link + .nav-desc {
		margin-right: 140px;
		}
	.nav-single h4 {
		display: block;	margin-bottom: 4px;	font-weight: bold; letter-spacing: 3px; font-size: 12px; opacity: .36; text-transform: uppercase;
		}
	.nav-single .nav-desc a {
        display:inline-block; max-width: 260px; width: 100%; font-size: 18px; font-weight: bold; line-height: 1.5; color: inherit; margin-bottom: 1em; word-break: break-word;
		}
	.nav-single .meta-nav {
 		margin-right: .6em; font-weight: 400;
		}
	.nav-single .nav-next .meta-nav {
 		margin-left: .6em; margin-right: 0;
		}
	.nav-single .nav-next {
 		text-align: right;
		}
@media screen and (min-width: 992px) and (max-width: 1200px) {
	.content-area.with-sidebar .nav-single > div {
		width: 100%; float: none;
		}
} 

/* ROUNDED */
.is-nav-single-rounded .nav-single .nav-image-link img {
	border-radius: 50%;
	}
/* ANIMATED */
.is-nav-single-animated .nav-single .nav-image-link img {
	-webkit-transform: scale(1.8) translateX(-10px); transform: scale(1.8) translateX(-10px);
	}
.is-nav-single-animated .nav-single > div.nav-next img {
	-webkit-transform: scale(1.8) translateX(10px); transform: scale(1.8) translateX(10px);
	}
.is-nav-single-animated .nav-single > div:hover img {
	-webkit-transform: scale(1) translateX(0px); transform: scale(1) translateX(0px);
	}
.is-nav-single-animated .nav-previous .nav-image-link + .nav-desc {
	margin-left: 160px;
	}
.is-nav-single-animated .nav-next .nav-image-link + .nav-desc {
	margin-right: 160px;
	}
	

/* ABOUT THE AUTHOR */
.about-author {
 	margin: 2em 0;
	}
.author-bio {
 	text-align: left; padding: 1em 0; position: relative; overflow: auto;
	}
/* About the author center */
.is-about-author-center .about-author .author-bio {
	text-align: center; overflow: hidden;
	}
.is-about-author-center .about-author .author-img {
	float: none; margin: 0 auto 24px auto; 
	}
.is-about-author-center .about-author .author-info {
	margin-left: 0;
	}
	
/* BOXED */
.is-about-author-boxed .author-bio,
.is-about-author-boxed-dark .author-bio,
.is-about-author-border .author-bio,
.is-about-author-border-arrow .author-bio {
 	padding: 1.4em 1.4em .6em;
	}
.is-about-author-boxed .author-bio {
 	background: #fff;
	}
/* BOXED DARK */
.is-about-author-boxed-dark .author-bio {
 	color: #fff; background: #212121;
	}
.is-about-author-boxed-dark .author-bio h4 {
	color: inherit;
	}
.is-about-author-boxed-dark .author-info p,
.is-about-author-boxed-dark .about-author .social-link {
 	color: rgba(255, 255, 255, 0.5);
	}
.is-about-author-boxed-dark .about-author .social-link:hover {
	color: #fff; background: #222;
	}
.is-about-author-boxed-dark .about-author .author-info:before {
	color: rgba(255, 255, 255, 0.08);
	}
/* BORDER */
.is-about-author-border .author-bio,
.is-about-author-border-arrow .author-bio {
	position: relative;	border: 3px solid;
	}
/* BORDER ARROW */
.is-about-author-border-arrow .author-bio:before,
.is-about-author-border-arrow .author-bio:after {
	bottom: 100%; left: 40px; border: solid transparent; content: " "; height: 0; width: 0; position: absolute; pointer-events: none;
	}
.is-about-author-border-arrow .author-bio:before {
	border-color: rgba(51, 51, 51, 0); border-bottom-color: #333333; border-width: 10px; margin-left: -10px;
	}
.is-about-author-border-arrow .author-bio:after {
	border-color: rgba(255, 255, 255, 0); border-bottom-color: #ffffff; border-width: 6px; margin-left: -6px;
	}
.is-widget-title-align-center.is-about-author-border-arrow .author-bio:before,
.is-widget-title-align-center.is-about-author-border-arrow .author-bio:after {
	left: 50%;
	}

	
.author-info:before {
    font-family: "fontello"; content: '\e862'; font-size: 84px; position:  absolute; top: -30px; right: 0; opacity: .05; z-index: -1;
    }
.author-bio h4 {
	font-size: 16px; margin-bottom: .6em;
	}
.author-img {
 	width: 54px; float: left;
	}
	.author-img img {
		display: block; border-radius: 50%;
		}
.author-info {
	margin-left: 72px; position: relative; z-index: 3;
	}
.author-info p {
 	font-size: 0.9rem; line-height: 1.7; margin: 0 0 1em 0;
	}
.author-info *:first-child {
 	margin-top: 0
	}
.author-info *:last-child {
 	margin-bottom: 0
	}
	.about-author .social-link {
		border: 0; width: 30px; line-height: 30px; margin-right: -8px; background: transparent;
		}

/* SHARE LINKS */
.share-links {
	margin: 3em 0; font-size: 14px;
	}
.share-links h3 {
	font-size: 11px; letter-spacing: 2px; text-transform: uppercase;
	}
.share-links h3:before {
    content: "— ";
	}
.share-links a {
	display: inline-block; padding: 5px 8px; color: inherit;
	}

/* CENTER */
.is-share-links-center .share-links {
	text-align: center;
	}

/* BOXED */
.is-share-links-boxed .single .share-links a,
.is-share-links-boxed-color .single .share-links a {
	padding: 8px 12px;
	}
.is-share-links-boxed .single .share-links a:not(:hover) {
	background: #fff; color: inherit;
	}
/* BOXED COLOR */
.is-share-links-boxed-color .share-links a:hover {
	transform: scale(1.2);
	}

/* BORDER */
.is-share-links-border .single .share-links a {
	padding: 8px 12px; border: 2px solid;
	}
.is-share-links-border .single .share-links a:not(:hover) {
	background: none;
	}
.is-share-links-border .single .share-links a:hover {
	border-color: transparent;
	}

.share-links a:hover,
.is-share-links-boxed-color .share-links a {
	color: #fff;
	}
.share-mail:hover,
.is-share-links-boxed-color .share-links .share-mail {
	background: #333;
	}
.share-facebook:hover,
.is-share-links-boxed-color .share-links .share-facebook {
	background: #3b5998;
	}
.share-twitter:hover,
.is-share-links-boxed-color .share-links .share-twitter {
	background: #55acee;
	}
.share-pinterest:hover,
.is-share-links-boxed-color .share-links .share-pinterest {
	background: #bd081c;
	}
.share-gplus:hover,
.is-share-links-boxed-color .share-links .share-gplus {
	background: #dc4e41;
	}
	
/* RELATED POSTS */
.related-posts {
    margin: 2em 0; clear: both;
    }
.blocks {
	margin: 0 -3px; overflow: hidden;
	}
.related-post {
	overflow: hidden; padding-bottom: 1em;
	}
	.related-posts .post-wrap {
		padding-top: 0; min-height: 220px;
		}
	.related-posts .post-wrap:before {
		opacity: .5;
		}
	.related-posts .post-thumbnail:hover .post-wrap:before {
		opacity: .6;
		}
	.related-posts .post-thumbnail .entry-meta {
		display: none;
		}
	.related-post h4 {
		margin-top: .6em; font-size: 13px; text-transform: uppercase;
		}
	.related-post h4 a {
		color: inherit;
		}
	.related-post-image {
		width: 25%; float: left; margin-right: 1em;	
		}
	.related-posts .post-thumbnail .entry-title,
	.related-posts .post-classic .entry-title { 
		font-size: 20px;
		}
	.related-posts .post-classic .featured-image {
		margin-bottom: 8px;
		}
	.related-posts .post-classic .entry-header {
		max-width: 250px; margin-left: auto; margin-right: auto; margin-bottom: 0;
		}
	.related-posts .post-thumbnail .more-link {
		margin-top: 16px;
		}
@media screen and (min-width: 992px) and (max-width: 1200px) {
	.content-area.with-sidebar .related-posts .block{
		width: 100%;
		}
	.content-area.with-sidebar .related-posts .post-wrap {
		padding-top: 0;
		}
} 

/* COMMENTS */
.comments-area {
 	margin-top: 2em; max-width: 1000px; margin-left: auto; margin-right: auto;
	}
.comments-area ol.commentlist {
 	list-style: none; margin: 0; padding: 0; text-align: left;
	}
	.comments-area ol.children {
		list-style: none; padding: 0; text-align: left;
		}
	.comments-area .comment-content > ol {
		margin-left: 0;
		}
	.comments-area ol ol,
	.comments-area ol #respond {
 		margin-left: 6%
		}
	.comments-area ol ol ol,
	.comments-area ol ol #respond  {
    	margin-left: 3%;
		}
    .commentlist {
 		padding: 0;
		}
.comments-area article {
    position: relative; padding: 1em 0; margin-bottom: 6px;
    }
/* DISQUS COMMENTS */
#disqus_thread {
	margin: 3em 0;
	}
/* JETPACK COMMENT SUBSCRIPTION */
.comment-subscription-form + .comment-subscription-form {
    margin-top: 0;
	}
	   
/* BOXED */
.is-comments-boxed .comments-area article {
	padding: 1em 1.8em;
	}
.is-comments-boxed-solid .comments-area article {
	background: #fff;
	}     
/* BORDER */
.is-comments-border .comments-area article {
	border: 3px solid;
	}
	
.comment-meta > cite, 
.comment-meta > span {
    position: relative; z-index: 1;
	}
.comment-meta a,
.comment-reply-link {
	color: inherit;
	}
.comment-meta img {
    display: block; float: left; margin: 0 1.0em 0 0; max-width: 42px; 
	}
/* SOFT ROUNDED */
.is-comments-image-soft-rounded .comment-meta img {
	border-radius: 14px;
	}
/* ROUNDED */
.is-comments-image-rounded .comment-meta img {
	border-radius: 50%;
	}
	
.comment-meta .fn {
    display: block; font-size: 16px; font-weight: 700; font-style: normal; padding-top: 0.2em;
    }
.comment-meta .fn i {
	font-style: normal; font-weight: normal; font-size: 10px; text-transform: uppercase; letter-spacing: 1px;
    }
.comment-meta .comment-date {
    display: block; font-size: 12px; opacity: .6; text-transform: uppercase; letter-spacing: .02em;
    }
.comments-area .nav-single {
    margin: 0;
	}
	.comments-area .nav-single a {
    	color: inherit;
		}
.comment-content {
    padding-top: .1em; clear: left; position: relative;
	}
	.comment-content p {
 		margin: .8em 0 .2em 0; font-size: 11px; line-height: 1.6;
		}
.comment .reply {
	position: relative; text-align: right; font-weight: 700; font-size: 12px; letter-spacing: 1px; text-transform: uppercase;
	}
.comment-reply-link {
	font-size: 12px; font-weight: 400;
	}
.comment-awaiting-moderation,
.logged-in-as {
	display: inline-block; padding: .6em 1.2em; font-size: 12px; background: #F6F7EA;
	}
.logged-in-as a {
    color: inherit; border-bottom: 1px solid rgb(0 0 0 / 14%); text-transform: uppercase; font-size: 10px; letter-spacing: .04em;
	}
p.logged-in-as a:hover {
    border-color: inherit;
	}
.comment-edit-link {
	display: inline-block; margin-left: 1em;
	}
/* comment by post author  */
.bypostauthor > article:before {
    font-family: "fontello"; content: '\e862'; font-size: 48px; position:  absolute; top: 0; right: 13px; opacity: .06;
    }
	
/* COMMENT FORM */
#respond {
	position: relative;	margin: 2em 0; text-align: left;
	}
#respond:only-child {
    margin: 0;
	}
.commentlist #respond {
	margin: 1em 0 2em;
	}
/* BOXED */
.is-comment-form-boxed #respond {
	padding: 2em;
    }
/* BOXED SOLID */
.is-comment-form-boxed-solid #respond {
	background: #fff;
    }
/* BORDER */
.is-comment-form-border #respond {
	border: 3px solid;
    }
/* BORDER ARROW */
.is-comment-form-border-arrow #respond {
	position: relative;	border: 3px solid;
	}
.is-comment-form-border-arrow #respond:before,
.is-comment-form-border-arrow #respond:after {
	bottom: 100%; left: 40px; border: solid transparent; content: " "; height: 0; width: 0; position: absolute; pointer-events: none;
	}
.is-comment-form-border-arrow #respond:before {
	border-color: rgba(51, 51, 51, 0); border-bottom-color: currentColor; border-width: 10px; margin-left: -10px;
	}
.is-comment-form-border-arrow #respond:after {
	border-color: rgba(255, 255, 255, 0); border-bottom-color: #ffffff; border-width: 6px; margin-left: -6px;
	}
/*.is-widget-title-align-center.is-about-author-border-arrow .author-bio:before,
.is-widget-title-align-center.is-about-author-border-arrow .author-bio:after {
	left: 50%;
	}*/
	
    #reply-title {
        text-transform: uppercase; font-size: 14px; letter-spacing: 2px; margin: 0;
    	}
    #reply-title small a {
		display: inline-block; margin-left: 1em;
    	}
.form-allowed-tags,
.comment-notes {
 	display: none; font-size: .8em;
	}
	.form-allowed-tags code {
 		margin-top: 1em
		}
.form-submit {
 	padding-top: .5em; margin: 0;
	}
.comments-area .input-text, 
.comments-area textarea, 
.comments-area select {
	max-width: 100%; border-width: 1px;
	}
.comment-form .comment-form-cookies-consent {
	clear: both; padding-top: 18px;
	}
.comment-form .comment-form-cookies-consent label {
	display: inline-block;
	}

/* PINGBACKS */
.comments-area .pingback,
.comments-area .trackback {
    font-size: 12px; text-transform: uppercase; font-weight: 700; letter-spacing: .04em; margin-bottom: 6px;
	}
.comments-area .pingback p,
.comments-area .trackback p {
    margin-bottom: 12px;
	}
.comments-area .pingback a,
.comments-area .trackback a {
    text-transform: none; letter-spacing: 0; color: inherit; font-weight: 400; font-size: 14px; opacity: .86; border-bottom: 1px solid #00000030; transition: all .5s;
	}
.comments-area .pingback a:hover,
.comments-area .trackback a:hover  {
    opacity: 1; border-bottom-color: currentColor;
	}
.comments-area .pingback .edit-link,
.comments-area .trackback .edit-link {
	margin-left: 10px;
	}
.comments-area .pingback .comment-edit-link,
.comments-area .trackback .comment-edit-link {
    display: inline; text-transform: uppercase; font-size: 11px; font-weight: 400; margin-left: 5px;
	}



/*  --------------------------------------------

	7. SHOP @shop - woocommerce shop styles...
	
    -------------------------------------------- */
	
/* SHOP */
.woocommerce .woocommerce-ordering, 
.woocommerce .woocommerce-result-count {
    display: block; float: none;
	}
.shop-filters {
	margin-bottom: 1.6em; overflow: hidden;
	}
.shop-filters .woocommerce-result-count {
	opacity: .6;
	}
.shop-filters select {
	padding-right: 36px; font-size: 11px; letter-spacing: .03em; text-transform: uppercase; border-width: 1px;
	}
.product .featured-image .cart-button {
	position: absolute; top: 50%; margin-top: -24px; left: 0; right: 0; z-index: 12; text-align: center; opacity: 0;
	}
.product .featured-image:hover .cart-button {
	opacity: 1;
	}
.product .featured-image a:before {
	background: #fff;
	}
.product .featured-image:hover a:before {
	opacity: .9;
	}
.woocommerce span.onsale {
    display: block; width: 60px; line-height: 60px; min-height: 0; padding: 0; text-align: center; background: #111; color: #fff; position: absolute; top: -28px; left: 28px; border-radius: 50%; font-size: 11px; text-transform: uppercase; font-weight: bold; letter-spacing: 1px; pointer-events: none; z-index: 99;
	}
mark.count {
    display: inline-block; width: 26px; line-height: 26px; font-size: 11px; font-weight: 400; vertical-align: top; margin-left: -9px; margin-top: -9px; background: #ffec88; border-radius: 50%; text-align: center;
	}
	


/* SHOP SINGLE */

/* Back To Shop Button */
.entry-header .button {
	letter-spacing: .06em; display: inline-block; margin-top: 6px; padding: 8px 16px !important; border: 0 !important;
	}
.entry-header .button:before {
	display: inline-block; padding-right: 6px; font-size: 14px;
	}
.entry-header .button.back:before {
	content: '\e860';
	}

/* Hide the title in the summary */
.woocommerce .entry-summary .entry-title {
	display: none;
	}

/* Right Side */
.woocommerce a.woocommerce-review-link {
    text-transform: uppercase; font-size: 10px; letter-spacing: 1px; display: inline-block; margin-left: 16px; opacity: .8;
	}
.woocommerce div.product p.price {
    margin: 20px 0;
	}
.woocommerce div.product p.price del {
    font-size: 18px; color: #9c5050;
	}
.woocommerce div.product p.price, 
.woocommerce div.product span.price {
    color: #0f0f0f;
	}
.woocommerce div.product .summary p.price {
    font-size: 2em;
	}
.woocommerce div.product p.price del, 
.woocommerce div.product span.price del {
    opacity: .8;
	}
.woocommerce div.product p.price ins,
.woocommerce div.product span.price ins,
.woocommerce ins,
.entry-meta .price {
	text-decoration: none; font-weight: 400;
	}
.woocommerce .summary .woocommerce-Price-currencySymbol {
    font-size: 18px; font-family: -apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif;
	}
.woocommerce .summary {
	font-size: .9rem;
	}
.woocommerce .product_meta {
	margin-top: 40px; font-size: 11px; letter-spacing: .1em; text-transform: uppercase;
	}
.woocommerce .product_meta > span {
	display: block; margin-bottom: 12px;
	}
.woocommerce .product_meta > span span,
.woocommerce .product_meta > span a {
	display: inline-block; margin-left: 5px;
	}
/* variations options */
.woocommerce div.product form.cart .variations td {
    padding-bottom: 1.2rem;
    }

/* Left Side */
.woocommerce-product-gallery figure {
    margin: 0;
	}
	
/* Tags */
.tagged_as {
	display: block; margin: 10px 0 0 0;
	}
.tagged_as a {
	font-size: 11px !important;
	}

/* Cart Form */
.woocommerce .summary .cart {
	margin-top: 30px;
	}
.shipping-calculator-button {
	color: inherit; font-size: 12px; text-transform: uppercase;
	}

/* Quantity */
.woocommerce .quantity .qty {
    width: 3.631em; text-align: center; padding: 1.06em .4em; border-width: 1px; margin-right: 24px;
	}

/* Thumbnails */
.woocommerce #content div.product div.thumbnails a, 
.woocommerce div.product div.thumbnails a, 
.woocommerce-page #content div.product div.thumbnails a, 
.woocommerce-page div.product div.thumbnails a {
    width: 31.33%; margin-right: 3%;
	}
.woocommerce .thumbnails.columns-3 a:nth-child(3n) {
	margin-right: 0 !important;
	}

/* Tabs */
.woocommerce-tabs.wc-tabs-wrapper {
    padding: 3em 0 0 0; clear: left;
	}
.woocommerce .wc-tab {
	font-size: .9rem;
	}
.woocommerce div.product .woocommerce-tabs ul.tabs {
	text-align: center; padding: 2px 20px;
	}
.woocommerce div.product .woocommerce-tabs ul.tabs li {
    background: none; border-radius: 0;
	}
.woocommerce div.product .woocommerce-tabs ul.tabs li:after, 
.woocommerce div.product .woocommerce-tabs ul.tabs li:before {
	display: none;
	}
.woocommerce div.product .woocommerce-tabs ul.tabs:before,
.woocommerce div.product .woocommerce-tabs ul.tabs li {
	border-color: rgb(0 0 0 / 13%);
	}
.woocommerce div.product .woocommerce-tabs ul.tabs li {
	margin: -1px -4px 0; padding: 0; line-height: 1;
	}
.woocommerce div.product .woocommerce-tabs ul.tabs li a {
    font-weight: 400; font-size: 11px; text-transform: uppercase; letter-spacing: 1px; padding: 13px 21px; color: #777; background: #fff;
	}
.woocommerce div.product .woocommerce-tabs ul.tabs li:not(.active) a:hover {
    background: #fafafa;
	}
.woocommerce div.product .woocommerce-tabs ul.tabs li.active {
	border-color: #222;
	}
.woocommerce div.product .woocommerce-tabs ul.tabs li.active a {
	color: #fff; background: #222; display: inline-block;
	}
.woocommerce div.product .woocommerce-tabs ul.tabs:before {
	bottom: 50%; z-index: 0;
	}
.woocommerce div.product .woocommerce-tabs .panel {
    margin: 2.2em 0 0 0;
	}

/* Table */
.woocommerce table.shop_attributes {
    margin: 2.4em 0 3em 0; border-collapse: collapse; border-top: 1px solid rgb(0 0 0 / 13%); border-left: 1px solid rgb(0 0 0 / 13%); 
	}
.woocommerce table.shop_attributes td {
    font-style: normal; padding-left: 16px; color: #666;
	}
.woocommerce table.shop_attributes tr {
    border-left: 1px solid rgb(0 0 0 / 13%);
	}
.woocommerce table.shop_attributes td, 
.woocommerce table.shop_attributes th {
    border-bottom: 1px solid rgb(0 0 0 / 13%); border-right: 1px solid rgb(0 0 0 / 13%);
	}
.woocommerce table.shop_attributes th {
	font-size: 11px; letter-spacing: 1px; padding: 10px 10px 10px 16px;
	}
.woocommerce table.shop_attributes .alt td, 
.woocommerce table.shop_attributes .alt th {
    background: rgb(0 0 0 / 9%);
	}

/* Variations */
.woocommerce div.product form.cart .variations label {
    font-weight: 700; text-align: left; padding: 12px 24px 13px; color: #333; background: rgb(0 0 0 / 9%);
	}
.woocommerce div.product form.cart .variations td {
    padding: 0;
	}
.woocommerce div.product form.cart .variations td.label {
	font-size: 13px;
	}
.woocommerce div.product form.cart .reset_variations {
    text-transform: uppercase; letter-spacing: 2px; font-size: 9px; float: right; padding: 0px 12px; background: #333; color: #fff;
	}
.woocommerce div.product form.cart .variations select {
    padding: 16px;
	}
.woocommerce-variation p {
    margin: 0;
	}
.woocommerce-variation {
    padding: 12px 20px; margin-bottom: 20px; border: 1px solid rgb(0 0 0 / 13%); font-size: 13px;
	}
.woocommerce-variation-price {
    font-size: 18px;
	}

/* Grouped Products */
.woocommerce-grouped-product-list a {
    text-transform: none; color: inherit; font-size: 18px; line-height: 1.4; letter-spacing: 0; padding-top: 10px; display: block;
	}
.woocommerce div.product form.cart table {
    padding-bottom: 22px; margin-bottom: 26px;
	}
table.woocommerce-grouped-product-list.group_table {
    border-bottom-color: #0000001f;
	}
.woocommerce div.product form.cart table div.quantity {
    text-align: left;
	}
.woocommerce-grouped-product-list-item__quantity .quantity .qty {
	margin-right: 0;
	}
.woocommerce-grouped-product-list-item__label label a {
    font-size: 15px; font-weight: 700;
	}
.woocommerce-grouped-product-list-item__price .amount {
    font-size: 16px;
	}
.woocommerce-grouped-product-list-item__price del {
    opacity: .6; margin-right: 6px;
	}

/* Reviews */
.woocommerce .panel h2.woocommerce-Reviews-title {
    text-transform: uppercase; font-size: 16px; letter-spacing: .06em;
	}
.woocommerce .panel h2.woocommerce-Reviews-title span {
    font-size: 200%; text-transform: none; letter-spacing: 0; color: #333; display: inline-block; margin-left: 8px;
	}
.woocommerce #reviews #comments ol.commentlist { 
	margin: 40px 0;
	}
.woocommerce #reviews #comments ol.commentlist li img.avatar {
    background: none; border: 0; border-radius: 50%;
	}
.woocommerce #reviews #comments ol.commentlist li .comment-text {
	position: relative; padding: 12px 16px; background: #fff; border-color: rgb(0 0 0 / 12%);
	}
.woocommerce #reviews #comments ol.commentlist li .comment-text p:last-child {
	margin-bottom: 0;
	}
.woocommerce #reviews #comments .meta strong {
    font-size: 13px; color: #222;
	}
.woocommerce #reviews #comments ol.commentlist li .meta time {
    text-transform: uppercase; font-size: 10px; letter-spacing: .05em;
	}
.woocommerce .star-rating {
	width: 5em;
	}
.woocommerce .star-rating:before,
.woocommerce .star-rating span:before {
	font-size: 12px;
	}
.comment-form-rating {
    margin-top: 24px;
    }
.woocommerce-Reviews #reply-title {
    display: block; text-align: center; font-weight: 700; letter-spacing: 0.06em; font-size: 16px;
    }

/* comment box arrow */
.woocommerce .comment-text:after, 
.woocommerce .comment-text:before {
	right: 100%; top: 32px; border: solid transparent; content: " "; height: 0; width: 0; position: absolute; pointer-events: none;
	}
.woocommerce .comment-text:after {
	border-color: rgba(255, 255, 255, 0); border-right-color: #fff; border-width: 7px; margin-top: -7px;
	}
.woocommerce .comment-text:before {
	border-color: rgba(238, 238, 238, 0); border-right-color: rgb(0 0 0 / 9%); border-width: 8px; margin-top: -8px;
	}

/* Review Form */
.woocommerce #review_form #respond::before {
	border: 0; /* fix for conflict with .is-comment-form-border-arrow class */
	}
.woocommerce #review_form_wrapper { 
	margin: 40px 0 0 0; overflow: hidden; padding: 32px 22px; border: 10px solid rgb(0 0 0 / 9%);
	}
.woocommerce #reviews h3,
.woocommerce #reviews .comment-form-rating,
.woocommerce #reviews .stars {
	text-align: center;
	}
.woocommerce #review_form #respond p.comment-form-rating {
    margin-bottom: 0;
	}
.woocommerce #reviews h3#reply-title {
    margin-bottom: 24px; letter-spacing: 0; text-transform: none; font-size: 24px;
	}
.comment-respond .input-text, 
.comment-respond textarea, 
.comment-respond select {
	border-width: 1px; max-width: 100%;
	}
.woocommerce #review_form_wrapper #respond .stars {
	font-size: 18px; max-width: 120px; margin-left: auto; margin-right: auto;
	}

/* Related Products & Cross & Up Sells */
.related.products,
.up-sells {
    margin-top: calc(2em + 2vw);
	}
.woocommerce .related.products > h2,
.upsells > h2,
.up-sells > h2 {
	text-align: center; margin-bottom: 1.8em;
	}
.cross-sells .entry-meta {
	text-align: center;
	}


/* CART */
.woocommerce table.shop_table th {
    font-size: 12px;
    }
.woocommerce table.shop_table {
    border-collapse: collapse; border-left: 0; border-color: #eee; border-bottom: 0; border-right: 0;
    }
.woocommerce table.shop_table th {
    border: 0;
    }
.woocommerce table.shop_table tbody tr.cart_item {
    border-left: 1px solid rgb(0 0 0 / 7%); border-right: 1px solid rgb(0 0 0 / 7%);
    }
.woocommerce #content table.cart img, 
.woocommerce table.cart img, 
.woocommerce-page #content table.cart img, 
.woocommerce-page table.cart img {
    max-height: 60px; width: auto; display: block; margin: 0 auto;
    }
.woocommerce table.shop_table td {
    border-color: rgb(0 0 0 / 7%); border-bottom: 0; border-right: 0;
	}
.woocommerce table.shop_table td.product-name a {
    color: inherit; font-size: 14px; font-weight: 700;
    }
.woocommerce table.shop_table td.actions {
    padding: 26px 0;
    }
.woocommerce table.shop_table tbody th, 
.woocommerce table.shop_table tfoot td, 
.woocommerce table.shop_table tfoot th {
    border-color: rgb(0 0 0 / 7%);
	}
.woocommerce a.remove {
    font-size: 24px; font-weight: 400; color: #ff5f5f !important; width: 29px; line-height: 28px; height: 29px; transition: all .3s;
	}
.woocommerce a.remove:hover {
    background: #ff5f5f !important;
	}
.woocommerce table.shop_table .quantity .qty {
	margin-right: 0;
	}
/* variations */
.woocommerce td.product-name dl.variation dd, 
.woocommerce td.product-name dl.variation dt {
    display: block; float: none;
    }
.woocommerce td.product-name dl.variation dt {
    font-size: 11px; line-height: 1.3; text-transform: uppercase; letter-spacing: 1px; opacity: .5; margin: 0;
    }
.woocommerce td.product-name dl.variation dd {
    padding: 0; margin-left: 0; font-size: 12px;
    }
.woocommerce td.product-name dl.variation dd p {
    margin-top: 0;
    }
	
/* Cart Totals */
.woocommerce .cart_totals .woocommerce-Price-amount.amount {
    font-size: 22px;
    }
.woocommerce .cart-collaterals .cart_totals, 
.woocommerce-page .cart-collaterals .cart_totals {
    max-width: 350px; margin: 0 auto; float: none; padding: 42px 32px; border: 5px solid #222;
    }

.woocommerce .cart-collaterals .cart_totals:before,
.woocommerce-page .cart-collaterals .cart_totals:before {
	content: '\e8c0'; display: block; font-size: 30px; width: 82px; line-height: 82px; margin: -86px auto 20px auto; color: #fff; background: #222; border-radius: 50%;
	}
.woocommerce .cart-collaterals h2,
.woocommerce .wc-proceed-to-checkout {
    text-align: center;
    }
.woocommerce #content table.cart td.actions .coupon .input-text, 
.woocommerce #content table.cart td.actions .coupon input,
.woocommerce table.cart td.actions .coupon .input-text, 
.woocommerce table.cart td.actions .coupon input {
    float: left; padding: 9px 20px 9px 0 !important; margin-right: 16px; border-bottom: 1px solid #0000002b !important;
	}
.woocommerce #content table.cart td.actions .coupon .input-text:focus, 
.woocommerce #content table.cart td.actions .coupon input:focus,
.woocommerce table.cart td.actions .coupon .input-text:focus, 
.woocommerce table.cart td.actions .coupon input:focus {
    border-bottom: 1px solid #222 !important;
	}
.woocommerce table.cart td.actions .button {
	padding: 10px 20px !important; border-width: 1px; font-size: 11px;
	}
.woocommerce #content table.cart td.actions .input-text, 
.woocommerce table.cart td.actions .input-text,
.woocommerce-cart table.cart td.actions .coupon .input-text {
    border: 0;
	}
.woocommerce #content table.cart td.actions .coupon, 
.woocommerce table.cart td.actions .coupon {
    overflow: hidden; padding-bottom: 20px;
    }
.woocommerce #content table.cart td.actions .input-text, 
.woocommerce table.cart td.actions .input-text, 
.woocommerce-page #content table.cart td.actions .input-text, 
.woocommerce-page table.cart td.actions .input-text {
    width: 132px;
	}
.woocommerce .cart-collaterals, 
.woocommerce-page .cart-collaterals {
    margin: 3em 0;
	}
.woocommerce .cart_item .product-subtotal .woocommerce-Price-amount.amount {
    font-size: 14px; font-weight: bold;
    }
.cart_totals form p {
    margin: 0;
	}
.cart_totals form p + p {
    margin-top: 1.2rem;	
	}
#add_payment_method .wc-proceed-to-checkout, 
.woocommerce-cart .wc-proceed-to-checkout, 
.woocommerce-checkout .wc-proceed-to-checkout {
	padding-bottom: 0;
	}
#add_payment_method .cart-collaterals .cart_totals tr td,
#add_payment_method .cart-collaterals .cart_totals tr th, 
.woocommerce-cart .cart-collaterals .cart_totals tr td, 
.woocommerce-cart .cart-collaterals .cart_totals tr th, 
.woocommerce-checkout .cart-collaterals .cart_totals tr td, 
.woocommerce-checkout .cart-collaterals .cart_totals tr th {
	border-color: rgb(0 0 0 / 7%);
	}
#add_payment_method .wc-proceed-to-checkout a.checkout-button, 
.woocommerce-cart .wc-proceed-to-checkout a.checkout-button, 
.woocommerce-checkout .wc-proceed-to-checkout a.checkout-button {
	font-size: 16px; margin-bottom: 0;
	}

/* cart page cross-sells product grid */
.woocommerce .cart-collaterals .cross-sells + .cart_totals {
    float: right;
    }
.woocommerce .cross-sells .blog-grid .hentry-wrap {
    padding: 0;
    }
.woocommerce .cross-sells > h2 {
    margin-bottom: 3rem;
    }


/* CHECKOUT */
.woocommerce #payment #place_order, 
.woocommerce-page #payment #place_order {
    visibility: visible;
	}
.woocommerce form .form-row {
    margin-bottom: 19px;
	}
.woocommerce form.checkout_coupon, .woocommerce form.login, .woocommerce form.register {
    border-color: #333; border-width: 3px; padding: 24px; margin-bottom: 4em;
	}
.woocommerce .checkout_coupon p.form-row {
    margin-bottom: 0;
	}
.checkout_coupon input#coupon_code {
    padding: 14px 20px;
	}
.woocommerce form .form-row input.input-text, 
.woocommerce form .form-row textarea,
.woocommerce form .form-row select {
    max-width: 100%;
	}
.woocommerce-checkout tfoot tr {
    border-left: 1px solid rgb(0 0 0 / 8%); border-right: 1px solid rgb(0 0 0 / 8%);
	}
.woocommerce-checkout tfoot {
    border-bottom: 1px solid rgb(0 0 0 / 8%);
	}
#add_payment_method #payment, 
.woocommerce-cart #payment, 
.woocommerce-checkout #payment {
    background: #fff; border: 3px solid #333; font-size: 13px; margin: 5em 0 3em 0;
    }
#add_payment_method #payment ul.payment_methods, 
.woocommerce-cart #payment ul.payment_methods, 
.woocommerce-checkout #payment ul.payment_methods {
    border-bottom: 1px solid #eee; padding: 26px 32px;
    }
#add_payment_method #payment div.form-row, 
.woocommerce-cart #payment div.form-row, 
.woocommerce-checkout #payment div.form-row {
    padding: 19px 32px;
    }
.woocommerce form.checkout.woocommerce-checkout {
    margin-top: 3em;
	}
.woocommerce-checkout-payment label {
    display: inline-block;
	}
#add_payment_method #payment div.payment_box, 
.woocommerce-cart #payment div.payment_box, 
.woocommerce-checkout #payment div.payment_box {
    background: none; border: 2px solid #eee;
	}
.woocommerce-checkout-payment p {
    margin-top: 0;
	}
.woocommerce-checkout #payment div.form-row:last-child {
    padding-bottom: 0;
	}
#add_payment_method #payment div.payment_box:before, 
.woocommerce-cart #payment div.payment_box:before, 
.woocommerce-checkout #payment div.payment_box:before {
    border-bottom: 9px solid #ddd;
	}
#add_payment_method #payment .payment_method_paypal .about_paypal, 
.woocommerce-cart #payment .payment_method_paypal .about_paypal, 
.woocommerce-checkout #payment .payment_method_paypal .about_paypal {
    margin-left: 20px; color: inherit; font-size: 10px;
	}
@media screen and (max-width: 767px) {
	.woocommerce form .form-row-first, 
	.woocommerce form .form-row-last, 
	.woocommerce-page form .form-row-first, 
	.woocommerce-page form .form-row-last {
		float: none;
		width: 100%;
		}
}
.woocommerce form.login .inline {
  	margin-left: 22px;
	}
.woocommerce form.login .inline input[type="checkbox"] { 
	margin-right:  0; 	
	}
.woocommerce form .form-row .input-checkbox {
    display: inline-block; text-align: left;
    }
/* country select */
.select2-container--default .select2-selection--single {
	font-size: 14px; border: 2px solid rgba(0, 0, 0, 0.1); border-radius: 0; height: 47px; background: transparent;
	}
.select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: 45px; padding-left: 12px;
	}
.select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 47px; right: 8px;
	}
.select2-dropdown {
	font-size: 14px; border: 2px solid rgba(0, 0, 0, 0.1);
	}
/* checkout coupon box padding fix */
.woocommerce form .form-row-first, 
.woocommerce form .form-row-last, 
.woocommerce-page form .form-row-first, 
.woocommerce-page form .form-row-last {
    width: 49%;
	}
/* checkout coupon button */
form.checkout_coupon.woocommerce-form-coupon .button {
    font-size: 11px; padding: 20px;
	}
/* checkout place order button */
html .woocommerce-checkout-payment .button {
    padding: 20px; font-size: 12px;
	}


/* ORDER RECEIVED */
.woocommerce .woocommerce-thankyou-order-received {
    font-size: 17px; text-align: center; padding: 22px; color: #cacac2;
    }
.woocommerce-thankyou-order-received:before {
	content: '\e85b'; font-size: 124px; line-height: 1.6; margin-bottom: 24px; color: #e6e6d8; display: block;	
	}
ul.woocommerce-thankyou-order-details.order_details {
    padding: 20px; background: #f9f5e5;
    }
.woocommerce .order_details li {
    font-size: 10px; letter-spacing: 1px; color: rgba(0, 0, 0, 0.6); border-right-color: rgba(0, 0, 0, 0.17); padding: 6px 27px 6px 6px;
    }
.woocommerce .order_details li strong {
    letter-spacing: 0; margin-top: 4px; font-size: 14px; color: #222;
	}
tr.product-purchase-note p {
    font-size: 13px; opacity: .6; max-width: 480px;
	}
.woocommerce table.order_details td,
.woocommerce table.order_details th,
.woocommerce table.customer_details td,
.woocommerce table.customer_details th  {
    padding-left: 0;
	}
@media screen and (max-width: 767px) {
	.woocommerce .order_details li {
    	display: block; float: none; margin-bottom: 10px; border-right: 0; border-bottom: 1px dashed rgba(51, 51, 51, 0.19); padding-bottom: 14px;
		}
	.woocommerce .order_details li:last-of-type {
		margin-bottom: 0;
		}
}


/* BUTTONS OVERRIDE */
.woocommerce #respond input#submit, 
.woocommerce .site-content  a.button, 
.woocommerce .site-content button.button, 
.woocommerce .site-content input.button {
	font-size: 11px; line-height: 1; text-transform: uppercase; padding: 16px 26px !important; font-weight: 700; border-radius: 0; color: #111 !important; background-color: transparent; border: 2px solid #111;
	}
/* alt buttons */
.woocommerce #respond input#submit.alt, 
.woocommerce .site-content a.button.alt, 
.woocommerce .site-content button.button.alt, 
.woocommerce .site-content input.button.alt {
	color: #fff !important; background-color: #222 !important; border: 0;
	}
/* alt buttons hover */
.woocommerce #respond input#submit.alt:hover, 
.woocommerce .site-content a.button.alt:hover, 
.woocommerce .site-content button.button.alt:hover, 
.woocommerce .site-content input.button.alt:hover {
	color: #fff !important; background-color: #000 !important;
	}
/* buttons hover */
.woocommerce #respond input#submit:hover, 
.woocommerce .site-content a.button:hover, 
.woocommerce .site-content button.button:hover, 
.woocommerce .site-content input.button:hover {
	color: #fff !important; background: #222;
	}
/* buttons disabled */
.woocommerce #respond input#submit:disabled:hover,  
.woocommerce .site-content button.button:disabled:hover, 
.woocommerce .site-content input.button:disabled:hover {
	color: #111 !important;
	}
.woocommerce #respond input#submit.loading, 
.woocommerce .site-content a.button.loading, 
.woocommerce .site-content button.button.loading, 
.woocommerce .site-content input.button.loading {
	padding-right: 36px;
	}
.woocommerce #respond input#submit:after, 
.woocommerce .site-content a.button:after, 
.woocommerce .site-content button.button:after, 
.woocommerce .site-content input.button:after {
	top: 13px !important; font-size: 14px;
	}
.woocommerce #respond input#submit.added:after, 
.woocommerce .site-content a.button.added:after, 
.woocommerce .site-content button.button.added:after, 
.woocommerce .site-content input.button.added:after {
    line-height: .8;
	}

/* PRETTY PHOTO */
div.pp_woocommerce div.ppt {
    text-align: left; font-weight: 400; font-size: 11px; text-transform: uppercase; letter-spacing: .08em;
	}
div.pp_woocommerce .pp_description, 
div.pp_woocommerce .pp_nav {
    font-size: 12px;
	}
div.pp_woocommerce .pp_arrow_next, 
div.pp_woocommerce .pp_arrow_previous {
	margin-top: 1px;
	}
.currentTextHolder {
	padding: 1px 1px 0; letter-spacing: 3px;
	}
div.pp_woocommerce .pp_close {
	background: none; top: -40px; right: 0; font-size: 21px!important;
	}
div.pp_woocommerce a.pp_contract, 
div.pp_woocommerce a.pp_expand {
	background: none; left: auto; right: 36px; top: -40px; font-size: 21px!important;
	}
div.ppt {
	margin: 0 0 -6px 0;
	}
div.pp_woocommerce .pp_arrow_next, 
div.pp_woocommerce .pp_arrow_previous {
	background: none; font-size: 21px !important; text-shadow: none; color: #333 !important;
	}
div.pp_woocommerce .pp_arrow_next:hover, 
div.pp_woocommerce .pp_arrow_previous:hover {
	color: #000 !important; background: none;
	}
div.pp_woocommerce .pp_next:before, 
div.pp_woocommerce .pp_previous:before {
	background: none; text-shadow: 0 1px 2px rgba(0,0,0,.2); font-size: 26px !important;
	}
div.pp_woocommerce .pp_gallery ul li a {
	border: 0; border-radius: 0;
	}
div.pp_woocommerce .pp_gallery ul li.selected a {
	border: 1px solid #fff;
	}
	
/* MESSAGES */
.woocommerce-error, .woocommerce-info, .woocommerce-message {
    background: none; border: 1px solid rgb(0 0 0 / 13%); font-size: .9rem; background: #fff;
	}
.woocommerce-error a, .woocommerce-info a, .woocommerce-message a {
    color: inherit;  text-transform: uppercase; font-size: 11px; display: inline-block; margin-left: 11px; letter-spacing: .05em;
	}
.woocommerce-message {
    border-top: 5px solid #79d69a;
	}
.woocommerce-message:before {
	color: #79d69a;
	}
.woocommerce-info {
    border-top: 5px solid #eae5bf;
	}
.woocommerce-info:before {
	color: #c3bf9b;
	}
.woocommerce-error {
    border-top: 5px solid #e29191;
	}
.woocommerce-error:before {
	color: #e29191;
	}
	
/* Breadcrumb */
.woocommerce .woocommerce-breadcrumb {
    margin-bottom: 50px; color: #ccc; text-transform: uppercase; font-size: 11px; letter-spacing: .07em; padding: 12px 24px; border: 1px solid #eee; display: inline-block; 
	}
.woocommerce .woocommerce-breadcrumb a {
    color: #333;
	}
.woocommerce a.added_to_cart {
    text-transform: uppercase; font-size: 11px; display: block; margin-top: 13px; letter-spacing: 1px; color: inherit;
	}
	
	
/* MY ACCOUNT PAGE */
.woocommerce-MyAccount-navigation ul {
    list-style: none; padding: 0; margin: 30px 0;
	}
.woocommerce-MyAccount-navigation ul li a {
    padding: 6px 12px; display: inline-block; border: 1px solid rgb(0 0 0 / 7%); color: inherit; text-transform: uppercase; font-size: 10px; letter-spacing: .05em; margin-bottom: 5px; transition: all 0.3s;
	}
.woocommerce-MyAccount-navigation ul li.is-active a {
	background: #222; color: #fff; cursor: default; border-color: transparent;
	}
.woocommerce-MyAccount-navigation ul li {
    display: inline-block;
	}
.woocommerce-MyAccount-navigation ul li:not(.is-active) a:hover {
    background: rgb(0 0 0 / 7%);
	}
.woocommerce-MyAccount-content,
.woocommerce table.my_account_orders {
	font-size: 13px;
	}
.woocommerce-MyAccount-content a:not(.button) {
	text-decoration: underline;
	}
.woocommerce-MyAccount-content a:not(.button):not(:hover) {
	color: inherit;
	}
.woocommerce-MyAccount-content legend {
    font-size: 16px; padding: 2em 0 1.3em 0; clear: left;
    }
.woocommerce-MyAccount-content a.button {
	padding: 10px 20px;
    }


/* Fix for W3 Total Cache Minify breaks single product layout */
@media screen and (min-width: 768px) {
	.woocommerce #content div.product div.summary, .woocommerce div.product div.summary, .woocommerce-page #content div.product div.summary, .woocommerce-page div.product div.summary {
		float: right;
		width: 48%;
		clear: none;
	}
}



/*  --------------------------------------------

	8. PORTFOLIO @portfolio - portfolio related styles...
	
    -------------------------------------------- */

/* FILTERS */
.filters {
 	list-style: none; padding: 0; margin: 0 0 40px 0; text-align: center; text-transform: uppercase;
	}
	.filters li {
 		display: inline-block; margin: .4em .1em .4em 0;
		}
		.filters li a {
 			color: inherit; opacity: .32; font-size: 11px; letter-spacing: .05em; font-weight: bold; padding: 5px 12px; border: 2px solid rgba(255, 255, 255, 0);
			}
		.filters li a:hover {
			opacity: .5;
			}
        .filters .current a,
        .filters .current a:hover {
            border-color: currentColor; opacity: 1;
            }

/* ITEMS */
.portfolio-grid {
    transition: height 0.5s ease 0s;
	}
.portfolio-grid .featured-image > a:first-of-type:after {
	position: absolute; top: -22px; right: 20px; width: 50px; line-height: 50px; font-size: 14px; text-align: center; border-radius: 50%; color: #fff; background: #222;
	}
.portfolio-grid .hentry .entry-header:before,
.single-portfolio .hentry .entry-header:before {
	display: none;
	}
/* video */
.portfolio-grid .hentry.format-video .featured-image > a:first-of-type:after {
	content: '\e834';
	}
/* audio */
.portfolio-grid .hentry.format-audio .featured-image > a:first-of-type:after {
	content: "\e885";
	}
/* image */
.portfolio-grid .hentry.format-image .featured-image > a:first-of-type:after {
	content: "\e8ca";
	}
/* gallery */
.portfolio-grid .hentry.format-gallery .featured-image > a:first-of-type:after {
	content: '\e870';
	}
/* link */
.portfolio-grid .hentry.format-link .featured-image > a:first-of-type:after {
	content: "\e811";
	}
.portfolio-excerpt {
	opacity: .5; font-size: 12px;
	}

/* PORTFOLIO SINGLE */
body.single-portfolio .hentry.portfolio .featured-image {
    display: none;
	}

/* ELEMENTOR */

/* Buttons */
.elementor-element .elementor-button.elementor-size-lg {
	font-size: 14px;
	}
.elementor-element .elementor-button.elementor-size-xl {
	font-size: 16px;
	}
/* elementskit button svg icon height fix */
.elementor-button-icon svg {
    height: auto;
	}
/* elementskit testimonial slider double nav icon fix */
.swiper-navigation-button:has(i.icon):after {
    display: none;
	}
/* carousel initalization layout jump fix */
.ekit-main-swiper:not(.swiper-initialized) {
    opacity: 0;
	}


/* LearnPress Latest Course Widget Title in Elementor */
.elementor-widget-wp-widget-lp-widget-recent-courses h5 {
    text-align: center; font-size: calc(1.2rem + 0.8vw); margin-bottom: 3rem;
	}

/* JetPopups Z-Index Fix */
.jet-popup.jet-popup--front-mode {
    z-index: 2000 !important;
	}
/* input max width issue */
.jet-popup .input-text {
	max-width: none;
	}


/* Social Feed Gallery - Lightbox fix */
.qligg-mfp-wrap .mfp-container .mfp-content {
    opacity: 1;
	}
.qligg-mfp-wrap .mfp-arrow:before {
	color: transparent;
	}
.qligg-mfp-wrap .mfp-container .mfp-content .mfp-figure.bottom .mfp-bottom-bar {
    overflow: hidden;
    }
.qligg-mfp-wrap .mfp-container .mfp-content .mfp-bottom-bar .mfp-title .mfp-icons {
    padding: 0 !important; line-height: 46px;
	}
/* Social Feed Gallery: hide when error */
.instagram-gallery__alert {
    display: none;
	}
.instagram-gallery__actions {
    display: none;
	}
	

/* PODCAST PLAYER */
/* https://wordpress.org/plugins/podcast-player/ */
html .pp-podcast[id^=pp-podcast-], html .pp-podcast[id^=pp-podcast-] button, html .pp-podcast[id^=pp-podcast-] input, html .pp-podcast[id^=pp-podcast-] .ppjs__audio-timer, html .pp-podcast[id^=pp-podcast-] .ppjs__time-float, html .pp-modal-window, .pp-modal-window button, html .pp-modal-window input, html .pp-modal-window .ppjs__audio-timer, html .pp-modal-window .ppjs__time-float {
    font-family: inherit !important;
	}
html .pod-entry__date {
    font-size: 11px; letter-spacing: .02em; color: rgb(0 0 0 / 35%);; margin-top: 5px;
	}
.pp-podcast[id^=pp-podcast-] .pod-entry__title a, .pp-modal-window .pod-entry__title a {
    font-weight: 700; font-size: 13px; text-transform: uppercase; letter-spacing: .01em;
	}
@media only screen and (min-width: 450px) {
	html .episode-list__wrapper {
		max-height: 274px;
		}
}
html button.episode-list__load-more {
    font-size: 12px; letter-spacing: 4px;
	}
html .ppjs__img-wrapper img {
	border-radius: 24px; box-shadow: 0 10px 20px rgb(105 113 64 / 50%);
	}


/* Seriously Simple Podcasting */
/* https://wordpress.org/plugins/seriously-simple-podcasting/ */
html .podcast_player:not(:first-child) {
	margin: 28px 0;
	}
/* Top Ovrflowing style for audio players */
.top-content-single + .site-main .has-post-thumbnail .podcast_player:first-child .castos-player, /* SSP Player */
.post.has-post-thumbnail .post-header + .entry-content .podcast_player:first-child .castos-player,
.top-content-single + .site-main .has-post-thumbnail .podlove-web-player:first-child, /* Podlove Player */
.post.has-post-thumbnail .post-header + .entry-content .podlove-web-player:first-child,
html:not(.is-audio-embeds-sticky) .top-content-single + .site-main .has-post-thumbnail .entry-content > .wp-block-embed:first-child,
html:not(.is-audio-embeds-sticky) .post.has-post-thumbnail .post-header + .entry-content > .wp-block-embed:first-child,
html:not(.is-audio-embeds-sticky) .top-content-single + .site-main .has-post-thumbnail .entry-content > iframe:first-child,
html:not(.is-audio-embeds-sticky) .post.has-post-thumbnail .post-header + .entry-content > iframe:first-child  {
	position: relative; margin-top: calc(-2vw - 60px); z-index: 500; border-radius: 6px; overflow: hidden; box-shadow: rgba(33, 33, 33, 0.4) 0px 0px 30px 6px;
	}
/* spotify embed bottom gap fix */
.wp-block-embed.is-provider-spotify .wp-block-embed__wrapper {
	margin-bottom: -1px;
	}
.podcast_player .castos-player {
	border-radius: 12px !important;
	}
.entry-content iframe {
	background-color: #fff;
	}
html.loaded .entry-content iframe {
	background-color: transparent;
	}
/* podcast meta */
.podcast_meta {
    margin: 24px 0 32px 0; font-size: 12px; text-transform: uppercase; font-weight: bold; color: transparent;
	}
/* hide duplicate subscribe links */
.podcast_meta p + p {
    display: none;
	}
.podcast_meta a {
    color: rgb(0 0 0 / 54%); display: inline-block; padding: 2px 17px; background: #00000021; border-radius: 12px; transition: all .2s ease-in-out;
	}
.podcast_meta a:hover {
    color: #333; background: rgb(0 0 0 / 16%);
	}

/* STICKY AUDIO EMBEDS - sticky-audio class is added with js*/
.sticky-audio {
	position: fixed !important; top: auto; bottom: 0; left: 0; width: 100%; margin: 0 !important; z-index: 10000; max-height: 150px; box-shadow: 0 0px 10px rgba(33, 33, 33, 0.24) !important; background: #fff; border-radius: 0 !important;
	}
iframe[src*="spotify.com"].sticky-audio {
	max-height: 152px;
	}
iframe[src*="soundcloud.com"].sticky-audio {
	max-height: 166px;
	}
iframe[src*="anchor.fm"].sticky-audio {
	height: 160px; max-height: none;
	}
iframe[src*="castbox.fm"].sticky-audio {
	max-height: 196px; background: #fff !important;
	}
iframe[src*="buzzsprout.com"].sticky-audio,
iframe[src*="ausha.co"].sticky-audio {
	height: 200px; max-height: none;
	}
frame[src*="blogtalkradio.com"].sticky-audio,
iframe[src*="transistor.fm"].sticky-audio {
	height: 180px; max-height: none;
	}
iframe[src*="captivate.fm"].sticky-audio {
	max-height: 170px;
	}

/* PODLOVE PODCAST PLAYER */
.podlove-web-player {
    margin-bottom: 1.6em;
	}
	
/* LEARNDASH : quiz selected radio button style broken fix */
.ldx-plugin .learndash .wpProQuiz_content .wpProQuiz_questionList input[type=checkbox], 
.ldx-plugin .learndash .wpProQuiz_content .wpProQuiz_questionList input[type=radio] {
    position: relative !important;
	}


/* Ultimate Post Kit Addon for Elementor */
/* fix: Alice Carousel Coverflow border radius issue */
.upk-item.swiper-slide {
    overflow: hidden;
	}
/* carousel loading glitch */
.swiper-container,
.swiper-carousel {
	opacity: 1; transition: opacity .3s;
	}
.swiper-container:not(.swiper-container-initialized),
.swiper-carousel:not(.swiper-initialized)  {
    opacity: 0;
	}

/* Delicious Recipes */
.entry-share a[class^="PIN_"],
.share-links a[class^="PIN_"] {
	display: none !important; /* disabled: not compatible with delicious recipes plugin with foodsy theme */
	}

/* QI ADDONS FIXES */
h1.qodef-m-title {
    margin: 0;
	}
.qodef-m-title {
    padding-bottom: 12px !important;
	}
/* accordion toggle icon svg height fix */
.qodef-qi-accordion .qodef-e-icon>svg {
    max-height: 100%;
	}

/* PRIME SLIDER */
/* add full class for fullscreen sliders */
.elementor-element.full ul.bdt-slideshow-items,
.elementor-element.full .bdt-sniper-slider {
    height: calc(100vh + 2px) !important;
	}
/* pagepiling slider full by default */
.elementor-element .bdt-pagepiling-slider {
    height: calc(100vh + 1px) !important;
	}


/* JET ELEMENTS */
/* fix border issue in post widget */ 
.jet-posts__inner-box .post-thumbnail {
    background: transparent;
	}

/* AMELIA BOOKING */
.amelia-booking label,
.amelia-booking button,
.amelia-v2-booking label,
.amelia-v2-booking button,
.am-step-booking-catalog label,
.am-step-booking-catalog button {
    letter-spacing: 0;
}
/* hide footer credit on free version */
.amelia-v2-booking .am-lite-footer {
    display: none !important;
	}