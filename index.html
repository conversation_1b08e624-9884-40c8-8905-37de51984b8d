<!doctype html>
<html lang="en-US"
	class="no-featured-area is-header-small is-body-full-width is-single-post-title-default is-post-title-align-center is-post-media-fixed is-blog-text-align-left is-meta-with-icons is-header-light is-header-full-width is-header-parallax-no is-menu-sticky is-menu-fixed-width is-menu-align-center is-menu-light is-submenu-light is-submenu-align-left is-menu-uppercase is-featured-area-full is-slider-buttons-center-margin is-slider-buttons-rounded is-slider-buttons-dark is-slider-title-label is-slider-parallax is-slider-title-none-uppercase is-slider-more-link-show-on-hover is-slider-more-link-border-bottom is-slider-text-align-center is-slider-v-align-center is-slider-h-align-center is-link-box-title-default is-link-box-title-transform-none is-link-box-text-align-center is-link-box-v-align-center is-link-box-parallax is-intro-align-center is-intro-text-dark is-intro-parallax-no is-more-link-border-bottom-light is-about-author-minimal is-related-posts-parallax is-related-posts-fixed is-share-links-boxed is-tagcloud-minimal is-nav-single-rounded is-nav-single-no-animated is-comments-minimal is-comments-image-rounded is-comment-form-boxed is-comment-form-border is-sidebar-right is-sidebar-sticky is-sidebar-align-left is-widget-title-align-left is-widget-bottomline is-trending-posts-default is-footer-subscribe-light is-footer-widgets-align-left is-footer-full-width is-meta-uppercase is-slider-dots-rounded-line-grow is-site-title-uppercase is-top-bar-mobile-left-visible is-top-bar-uppercase is-top-bar-full is-sub-menu-ani-fade-in-left is-menu-hover-badge is-menu-hover-badge-round is-copyright-uppercase is-logo-bg-stretch-left is-header-sticky-shadow-soft-shorter is-header-transparent-border-bottom is-header-bg-blur-slightly is-footer-border-top is-footer-border-light is-header-float is-header-transparent"
	data-title-ratio="0.7" data-link-box-title-ratio="0.5" data-generic-button-style="" data-header-bg-shape="">

<meta http-equiv="content-type" content="text/html;charset=UTF-8" />

<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="profile" href="http://gmpg.org/xfn/11">
	<title>Motiff Square</title>
	<meta name='robots' content='max-image-preview:large' />
	<style>
		img:is([sizes="auto" i], [sizes^="auto," i]) {
			contain-intrinsic-size: 3000px 1500px
		}
	</style>
	<link rel='dns-prefetch' href='https://fonts.googleapis.com/' />
	<link rel="alternate" type="application/rss+xml" title="Motiff Square &raquo; Feed"
		href="https://themes.pixelwars.org/interique/demo-01/feed/" />
	<link rel="alternate" type="application/rss+xml" title="Motiff Square &raquo; Comments Feed"
		href="https://themes.pixelwars.org/interique/demo-01/comments/feed/" />
	<script type="text/javascript">
		/* <![CDATA[ */
		window._wpemojiSettings = { "baseUrl": "https:\/\/s.w.org\/images\/core\/emoji\/15.1.0\/72x72\/", "ext": ".png", "svgUrl": "https:\/\/s.w.org\/images\/core\/emoji\/15.1.0\/svg\/", "svgExt": ".svg", "source": { "concatemoji": "https:\/\/themes.pixelwars.org\/interique\/demo-01\/wp-includes\/js\/wp-emoji-release.min.js?ver=6.8.1" } };
		/*! This file is auto-generated */
		!function (i, n) { var o, s, e; function c(e) { try { var t = { supportTests: e, timestamp: (new Date).valueOf() }; sessionStorage.setItem(o, JSON.stringify(t)) } catch (e) { } } function p(e, t, n) { e.clearRect(0, 0, e.canvas.width, e.canvas.height), e.fillText(t, 0, 0); var t = new Uint32Array(e.getImageData(0, 0, e.canvas.width, e.canvas.height).data), r = (e.clearRect(0, 0, e.canvas.width, e.canvas.height), e.fillText(n, 0, 0), new Uint32Array(e.getImageData(0, 0, e.canvas.width, e.canvas.height).data)); return t.every(function (e, t) { return e === r[t] }) } function u(e, t, n) { switch (t) { case "flag": return n(e, "\ud83c\udff3\ufe0f\u200d\u26a7\ufe0f", "\ud83c\udff3\ufe0f\u200b\u26a7\ufe0f") ? !1 : !n(e, "\ud83c\uddfa\ud83c\uddf3", "\ud83c\uddfa\u200b\ud83c\uddf3") && !n(e, "\ud83c\udff4\udb40\udc67\udb40\udc62\udb40\udc65\udb40\udc6e\udb40\udc67\udb40\udc7f", "\ud83c\udff4\u200b\udb40\udc67\u200b\udb40\udc62\u200b\udb40\udc65\u200b\udb40\udc6e\u200b\udb40\udc67\u200b\udb40\udc7f"); case "emoji": return !n(e, "\ud83d\udc26\u200d\ud83d\udd25", "\ud83d\udc26\u200b\ud83d\udd25") }return !1 } function f(e, t, n) { var r = "undefined" != typeof WorkerGlobalScope && self instanceof WorkerGlobalScope ? new OffscreenCanvas(300, 150) : i.createElement("canvas"), a = r.getContext("2d", { willReadFrequently: !0 }), o = (a.textBaseline = "top", a.font = "600 32px Arial", {}); return e.forEach(function (e) { o[e] = t(a, e, n) }), o } function t(e) { var t = i.createElement("script"); t.src = e, t.defer = !0, i.head.appendChild(t) } "undefined" != typeof Promise && (o = "wpEmojiSettingsSupports", s = ["flag", "emoji"], n.supports = { everything: !0, everythingExceptFlag: !0 }, e = new Promise(function (e) { i.addEventListener("DOMContentLoaded", e, { once: !0 }) }), new Promise(function (t) { var n = function () { try { var e = JSON.parse(sessionStorage.getItem(o)); if ("object" == typeof e && "number" == typeof e.timestamp && (new Date).valueOf() < e.timestamp + 604800 && "object" == typeof e.supportTests) return e.supportTests } catch (e) { } return null }(); if (!n) { if ("undefined" != typeof Worker && "undefined" != typeof OffscreenCanvas && "undefined" != typeof URL && URL.createObjectURL && "undefined" != typeof Blob) try { var e = "postMessage(" + f.toString() + "(" + [JSON.stringify(s), u.toString(), p.toString()].join(",") + "));", r = new Blob([e], { type: "text/javascript" }), a = new Worker(URL.createObjectURL(r), { name: "wpTestEmojiSupports" }); return void (a.onmessage = function (e) { c(n = e.data), a.terminate(), t(n) }) } catch (e) { } c(n = f(s, u, p)) } t(n) }).then(function (e) { for (var t in e) n.supports[t] = e[t], n.supports.everything = n.supports.everything && n.supports[t], "flag" !== t && (n.supports.everythingExceptFlag = n.supports.everythingExceptFlag && n.supports[t]); n.supports.everythingExceptFlag = n.supports.everythingExceptFlag && !n.supports.flag, n.DOMReady = !1, n.readyCallback = function () { n.DOMReady = !0 } }).then(function () { return e }).then(function () { var e; n.supports.everything || (n.readyCallback(), (e = n.source || {}).concatemoji ? t(e.concatemoji) : e.wpemoji && e.twemoji && (t(e.twemoji), t(e.wpemoji))) })) }((window, document), window._wpemojiSettings);
		/* ]]> */
	</script>
	<style id='wp-emoji-styles-inline-css' type='text/css'>
		img.wp-smiley,
		img.emoji {
			display: inline !important;
			border: none !important;
			box-shadow: none !important;
			height: 1em !important;
			width: 1em !important;
			margin: 0 0.07em !important;
			vertical-align: -0.1em !important;
			background: none !important;
			padding: 0 !important;
		}
	</style>
	<style id='classic-theme-styles-inline-css' type='text/css'>
		/*! This file is auto-generated */
		.wp-block-button__link {
			color: #fff;
			background-color: #32373c;
			border-radius: 9999px;
			box-shadow: none;
			text-decoration: none;
			padding: calc(.667em + 2px) calc(1.333em + 2px);
			font-size: 1.125em
		}

		.wp-block-file__button {
			background: #32373c;
			color: #fff;
			text-decoration: none
		}
	</style>
	<style id='global-styles-inline-css' type='text/css'>
		:root {
			--wp--preset--aspect-ratio--square: 1;
			--wp--preset--aspect-ratio--4-3: 4/3;
			--wp--preset--aspect-ratio--3-4: 3/4;
			--wp--preset--aspect-ratio--3-2: 3/2;
			--wp--preset--aspect-ratio--2-3: 2/3;
			--wp--preset--aspect-ratio--16-9: 16/9;
			--wp--preset--aspect-ratio--9-16: 9/16;
			--wp--preset--color--black: #000000;
			--wp--preset--color--cyan-bluish-gray: #abb8c3;
			--wp--preset--color--white: #ffffff;
			--wp--preset--color--pale-pink: #f78da7;
			--wp--preset--color--vivid-red: #cf2e2e;
			--wp--preset--color--luminous-vivid-orange: #ff6900;
			--wp--preset--color--luminous-vivid-amber: #fcb900;
			--wp--preset--color--light-green-cyan: #7bdcb5;
			--wp--preset--color--vivid-green-cyan: #00d084;
			--wp--preset--color--pale-cyan-blue: #8ed1fc;
			--wp--preset--color--vivid-cyan-blue: #0693e3;
			--wp--preset--color--vivid-purple: #9b51e0;
			--wp--preset--gradient--vivid-cyan-blue-to-vivid-purple: linear-gradient(135deg, rgba(6, 147, 227, 1) 0%, rgb(155, 81, 224) 100%);
			--wp--preset--gradient--light-green-cyan-to-vivid-green-cyan: linear-gradient(135deg, rgb(122, 220, 180) 0%, rgb(0, 208, 130) 100%);
			--wp--preset--gradient--luminous-vivid-amber-to-luminous-vivid-orange: linear-gradient(135deg, rgba(252, 185, 0, 1) 0%, rgba(255, 105, 0, 1) 100%);
			--wp--preset--gradient--luminous-vivid-orange-to-vivid-red: linear-gradient(135deg, rgba(255, 105, 0, 1) 0%, rgb(207, 46, 46) 100%);
			--wp--preset--gradient--very-light-gray-to-cyan-bluish-gray: linear-gradient(135deg, rgb(238, 238, 238) 0%, rgb(169, 184, 195) 100%);
			--wp--preset--gradient--cool-to-warm-spectrum: linear-gradient(135deg, rgb(74, 234, 220) 0%, rgb(151, 120, 209) 20%, rgb(207, 42, 186) 40%, rgb(238, 44, 130) 60%, rgb(251, 105, 98) 80%, rgb(254, 248, 76) 100%);
			--wp--preset--gradient--blush-light-purple: linear-gradient(135deg, rgb(255, 206, 236) 0%, rgb(152, 150, 240) 100%);
			--wp--preset--gradient--blush-bordeaux: linear-gradient(135deg, rgb(254, 205, 165) 0%, rgb(254, 45, 45) 50%, rgb(107, 0, 62) 100%);
			--wp--preset--gradient--luminous-dusk: linear-gradient(135deg, rgb(255, 203, 112) 0%, rgb(199, 81, 192) 50%, rgb(65, 88, 208) 100%);
			--wp--preset--gradient--pale-ocean: linear-gradient(135deg, rgb(255, 245, 203) 0%, rgb(182, 227, 212) 50%, rgb(51, 167, 181) 100%);
			--wp--preset--gradient--electric-grass: linear-gradient(135deg, rgb(202, 248, 128) 0%, rgb(113, 206, 126) 100%);
			--wp--preset--gradient--midnight: linear-gradient(135deg, rgb(2, 3, 129) 0%, rgb(40, 116, 252) 100%);
			--wp--preset--font-size--small: 13px;
			--wp--preset--font-size--medium: 20px;
			--wp--preset--font-size--large: 36px;
			--wp--preset--font-size--x-large: 42px;
			--wp--preset--spacing--20: 0.44rem;
			--wp--preset--spacing--30: 0.67rem;
			--wp--preset--spacing--40: 1rem;
			--wp--preset--spacing--50: 1.5rem;
			--wp--preset--spacing--60: 2.25rem;
			--wp--preset--spacing--70: 3.38rem;
			--wp--preset--spacing--80: 5.06rem;
			--wp--preset--shadow--natural: 6px 6px 9px rgba(0, 0, 0, 0.2);
			--wp--preset--shadow--deep: 12px 12px 50px rgba(0, 0, 0, 0.4);
			--wp--preset--shadow--sharp: 6px 6px 0px rgba(0, 0, 0, 0.2);
			--wp--preset--shadow--outlined: 6px 6px 0px -3px rgba(255, 255, 255, 1), 6px 6px rgba(0, 0, 0, 1);
			--wp--preset--shadow--crisp: 6px 6px 0px rgba(0, 0, 0, 1);
		}

		:where(.is-layout-flex) {
			gap: 0.5em;
		}

		:where(.is-layout-grid) {
			gap: 0.5em;
		}

		body .is-layout-flex {
			display: flex;
		}

		.is-layout-flex {
			flex-wrap: wrap;
			align-items: center;
		}

		.is-layout-flex> :is(*, div) {
			margin: 0;
		}

		body .is-layout-grid {
			display: grid;
		}

		.is-layout-grid> :is(*, div) {
			margin: 0;
		}

		:where(.wp-block-columns.is-layout-flex) {
			gap: 2em;
		}

		:where(.wp-block-columns.is-layout-grid) {
			gap: 2em;
		}

		:where(.wp-block-post-template.is-layout-flex) {
			gap: 1.25em;
		}

		:where(.wp-block-post-template.is-layout-grid) {
			gap: 1.25em;
		}

		.has-black-color {
			color: var(--wp--preset--color--black) !important;
		}

		.has-cyan-bluish-gray-color {
			color: var(--wp--preset--color--cyan-bluish-gray) !important;
		}

		.has-white-color {
			color: var(--wp--preset--color--white) !important;
		}

		.has-pale-pink-color {
			color: var(--wp--preset--color--pale-pink) !important;
		}

		.has-vivid-red-color {
			color: var(--wp--preset--color--vivid-red) !important;
		}

		.has-luminous-vivid-orange-color {
			color: var(--wp--preset--color--luminous-vivid-orange) !important;
		}

		.has-luminous-vivid-amber-color {
			color: var(--wp--preset--color--luminous-vivid-amber) !important;
		}

		.has-light-green-cyan-color {
			color: var(--wp--preset--color--light-green-cyan) !important;
		}

		.has-vivid-green-cyan-color {
			color: var(--wp--preset--color--vivid-green-cyan) !important;
		}

		.has-pale-cyan-blue-color {
			color: var(--wp--preset--color--pale-cyan-blue) !important;
		}

		.has-vivid-cyan-blue-color {
			color: var(--wp--preset--color--vivid-cyan-blue) !important;
		}

		.has-vivid-purple-color {
			color: var(--wp--preset--color--vivid-purple) !important;
		}

		.has-black-background-color {
			background-color: var(--wp--preset--color--black) !important;
		}

		.has-cyan-bluish-gray-background-color {
			background-color: var(--wp--preset--color--cyan-bluish-gray) !important;
		}

		.has-white-background-color {
			background-color: var(--wp--preset--color--white) !important;
		}

		.has-pale-pink-background-color {
			background-color: var(--wp--preset--color--pale-pink) !important;
		}

		.has-vivid-red-background-color {
			background-color: var(--wp--preset--color--vivid-red) !important;
		}

		.has-luminous-vivid-orange-background-color {
			background-color: var(--wp--preset--color--luminous-vivid-orange) !important;
		}

		.has-luminous-vivid-amber-background-color {
			background-color: var(--wp--preset--color--luminous-vivid-amber) !important;
		}

		.has-light-green-cyan-background-color {
			background-color: var(--wp--preset--color--light-green-cyan) !important;
		}

		.has-vivid-green-cyan-background-color {
			background-color: var(--wp--preset--color--vivid-green-cyan) !important;
		}

		.has-pale-cyan-blue-background-color {
			background-color: var(--wp--preset--color--pale-cyan-blue) !important;
		}

		.has-vivid-cyan-blue-background-color {
			background-color: var(--wp--preset--color--vivid-cyan-blue) !important;
		}

		.has-vivid-purple-background-color {
			background-color: var(--wp--preset--color--vivid-purple) !important;
		}

		.has-black-border-color {
			border-color: var(--wp--preset--color--black) !important;
		}

		.has-cyan-bluish-gray-border-color {
			border-color: var(--wp--preset--color--cyan-bluish-gray) !important;
		}

		.has-white-border-color {
			border-color: var(--wp--preset--color--white) !important;
		}

		.has-pale-pink-border-color {
			border-color: var(--wp--preset--color--pale-pink) !important;
		}

		.has-vivid-red-border-color {
			border-color: var(--wp--preset--color--vivid-red) !important;
		}

		.has-luminous-vivid-orange-border-color {
			border-color: var(--wp--preset--color--luminous-vivid-orange) !important;
		}

		.has-luminous-vivid-amber-border-color {
			border-color: var(--wp--preset--color--luminous-vivid-amber) !important;
		}

		.has-light-green-cyan-border-color {
			border-color: var(--wp--preset--color--light-green-cyan) !important;
		}

		.has-vivid-green-cyan-border-color {
			border-color: var(--wp--preset--color--vivid-green-cyan) !important;
		}

		.has-pale-cyan-blue-border-color {
			border-color: var(--wp--preset--color--pale-cyan-blue) !important;
		}

		.has-vivid-cyan-blue-border-color {
			border-color: var(--wp--preset--color--vivid-cyan-blue) !important;
		}

		.has-vivid-purple-border-color {
			border-color: var(--wp--preset--color--vivid-purple) !important;
		}

		.has-vivid-cyan-blue-to-vivid-purple-gradient-background {
			background: var(--wp--preset--gradient--vivid-cyan-blue-to-vivid-purple) !important;
		}

		.has-light-green-cyan-to-vivid-green-cyan-gradient-background {
			background: var(--wp--preset--gradient--light-green-cyan-to-vivid-green-cyan) !important;
		}

		.has-luminous-vivid-amber-to-luminous-vivid-orange-gradient-background {
			background: var(--wp--preset--gradient--luminous-vivid-amber-to-luminous-vivid-orange) !important;
		}

		.has-luminous-vivid-orange-to-vivid-red-gradient-background {
			background: var(--wp--preset--gradient--luminous-vivid-orange-to-vivid-red) !important;
		}

		.has-very-light-gray-to-cyan-bluish-gray-gradient-background {
			background: var(--wp--preset--gradient--very-light-gray-to-cyan-bluish-gray) !important;
		}

		.has-cool-to-warm-spectrum-gradient-background {
			background: var(--wp--preset--gradient--cool-to-warm-spectrum) !important;
		}

		.has-blush-light-purple-gradient-background {
			background: var(--wp--preset--gradient--blush-light-purple) !important;
		}

		.has-blush-bordeaux-gradient-background {
			background: var(--wp--preset--gradient--blush-bordeaux) !important;
		}

		.has-luminous-dusk-gradient-background {
			background: var(--wp--preset--gradient--luminous-dusk) !important;
		}

		.has-pale-ocean-gradient-background {
			background: var(--wp--preset--gradient--pale-ocean) !important;
		}

		.has-electric-grass-gradient-background {
			background: var(--wp--preset--gradient--electric-grass) !important;
		}

		.has-midnight-gradient-background {
			background: var(--wp--preset--gradient--midnight) !important;
		}

		.has-small-font-size {
			font-size: var(--wp--preset--font-size--small) !important;
		}

		.has-medium-font-size {
			font-size: var(--wp--preset--font-size--medium) !important;
		}

		.has-large-font-size {
			font-size: var(--wp--preset--font-size--large) !important;
		}

		.has-x-large-font-size {
			font-size: var(--wp--preset--font-size--x-large) !important;
		}

		:where(.wp-block-post-template.is-layout-flex) {
			gap: 1.25em;
		}

		:where(.wp-block-post-template.is-layout-grid) {
			gap: 1.25em;
		}

		:where(.wp-block-columns.is-layout-flex) {
			gap: 2em;
		}

		:where(.wp-block-columns.is-layout-grid) {
			gap: 2em;
		}

		:root :where(.wp-block-pullquote) {
			font-size: 1.5em;
			line-height: 1.6;
		}
	</style>
	<link rel='stylesheet' id='fontello-css'
		href='./wp-content/plugins/pixelwars-core/themes/global/css/fonts/fontello/css/fontello.css' type='text/css'
		media='all' />
	<link rel='stylesheet' id='pixelwars-core-shortcodes-css'
		href='./wp-content/plugins/pixelwars-core/themes/global/css/shortcodes.css' type='text/css' media='all' />
	<link rel='stylesheet' id='qi-addons-for-elementor-grid-style-css'
		href='./wp-content/plugins/qi-addons-for-elementor/assets/css/grid.minf5a9.css?ver=1.8.9' type='text/css'
		media='all' />
	<link rel='stylesheet' id='qi-addons-for-elementor-helper-parts-style-css'
		href='./wp-content/plugins/qi-addons-for-elementor/assets/css/helper-parts.minf5a9.css?ver=1.8.9'
		type='text/css' media='all' />
	<link rel='stylesheet' id='qi-addons-for-elementor-style-css'
		href='./wp-content/plugins/qi-addons-for-elementor/assets/css/main.minf5a9.css?ver=1.8.9' type='text/css'
		media='all' />
	<link rel='stylesheet' id='interique-font-texgyreadventor-css'
		href='./wp-content/themes/interique/css/fonts/texgyreadventor/stylesheet0899.css?ver=6.8.1' type='text/css'
		media='all' />
	<link rel='stylesheet' id='interique-font-now-css'
		href='./wp-content/themes/interique/css/fonts/now/stylesheet0899.css?ver=6.8.1' type='text/css' media='all' />
	<link rel='stylesheet' id='interique-fonts-css'
		href='https://fonts.googleapis.com/css?family=Jost%3A100%2C100i%2C200%2C200i%2C300%2C300i%2C400%2C400i%2C500%2C500i%2C600%2C600i%2C700%2C700i%2C800%2C800i%2C900%2C900i%7CBricolage+Grotesque%3A100%2C100i%2C200%2C200i%2C300%2C300i%2C400%2C400i%2C500%2C500i%2C600%2C600i%2C700%2C700i%2C800%2C800i%2C900%2C900i&amp;ver=6.8.1'
		type='text/css' media='all' />
	<link rel='stylesheet' id='normalize-css' href='./wp-content/themes/interique/css/normalize0899.css?ver=6.8.1'
		type='text/css' media='all' />
	<link rel='stylesheet' id='bootstrap-css' href='./wp-content/themes/interique/css/bootstrap0899.css?ver=6.8.1'
		type='text/css' media='all' />
	<link rel='stylesheet' id='fluidbox-css' href='./wp-content/themes/interique/js/fluidbox/fluidbox0899.css?ver=6.8.1'
		type='text/css' media='all' />
	<link rel='stylesheet' id='magnific-popup-css'
		href='./wp-content/themes/interique/js/jquery.magnific-popup/magnific-popup0899.css?ver=6.8.1' type='text/css'
		media='all' />
	<link rel='stylesheet' id='owl-carousel-css'
		href='./wp-content/themes/interique/js/owl-carousel/owl.carousel0899.css?ver=6.8.1' type='text/css'
		media='all' />
	<link rel='stylesheet' id='interique-main-css' href='./wp-content/themes/interique/css/main0899.css?ver=6.8.1'
		type='text/css' media='all' />
	<link rel='stylesheet' id='interique-768-css' href='./wp-content/themes/interique/css/7680899.css?ver=6.8.1'
		type='text/css' media='all' />
	<link rel='stylesheet' id='interique-992-css' href='./wp-content/themes/interique/css/9920899.css?ver=6.8.1'
		type='text/css' media='all' />
	<link rel='stylesheet' id='interique-style-css' href='./wp-content/themes/interique/style0899.css?ver=6.8.1'
		type='text/css' media='all' />
	<style id='interique-style-inline-css' type='text/css'>
		.site-title {
			font-family: 'TeXGyreAdventor', sans-serif;
		}

		.nav-menu,
		.entry-meta,
		.owl-nav,
		label,
		.page-links,
		.navigation,
		.entry-title i,
		.site-info,
		.filters {
			font-family: 'Jost';
		}

		.widget-title {
			font-family: 'TeXGyreAdventor', sans-serif;
		}

		h1,
		.entry-title,
		.footer-subscribe h3,
		.widget_categories ul li,
		.widget_recent_entries ul li a,
		.widget_pages ul li,
		.widget_nav_menu ul li,
		.widget_archive ul li,
		.widget_most_recommended_posts ul li a,
		.widget_calendar table caption,
		.tptn_title,
		.nav-single a,
		.widget_recent_comments ul li,
		.widget_product_categories ul li,
		.widget_meta ul li,
		.widget_rss ul a.rsswidget {
			font-family: 'Bricolage Grotesque';
		}

		h2,
		h3,
		h4,
		h5,
		h6,
		blockquote,
		.tab-titles {
			font-family: 'Jost';
		}

		.slider-box .entry-title {
			font-family: 'Now', sans-serif;
		}

		body {
			font-family: 'Jost';
		}

		.link-box .entry-title {
			font-family: 'Now', sans-serif;
		}

		.button,
		button,
		html .elementor-button,
		html .ekit-wid-con .elementskit-btn,
		html .ekit-wid-con .ekit_creative_button,
		.more-link {
			font-family: 'Jost';
		}

		.top-bar {
			font-family: 'Jost';
		}

		@media screen and (min-width: 992px) {
			.site-header .site-title {
				font-size: 24px;
			}
		}

		@media screen and (min-width: 992px) {
			.is-header-smaller .site-header.clone .site-title {
				font-size: 24px;
			}
		}

		@media screen and (max-width: 991px) {
			.site-header .site-title {
				font-size: 14px;
			}
		}

		@media screen and (min-width: 992px) {
			.blog-small .entry-title {
				font-size: 24px;
			}
		}

		@media screen and (min-width: 992px) {
			h1 {
				font-size: 102px;
			}
		}

		@media screen and (min-width: 992px) {
			html {
				font-size: 16px;
			}
		}

		@media screen and (max-width: 991px) {
			html {
				font-size: 15px;
			}
		}

		@media screen and (min-width: 992px) {
			.nav-menu>ul {
				font-size: 13px;
			}
		}

		@media screen and (min-width: 992px) {
			.blog-stream .entry-content {
				font-size: 16px;
			}
		}

		@media screen and (min-width: 992px) {
			.blog-stream.blog-small .entry-content {
				font-size: 15px;
			}
		}

		.widget-title {
			font-size: 13px;
		}

		@media screen and (min-width: 992px) {
			.nav-menu ul ul {
				font-size: 12px;
			}
		}

		.top-bar {
			font-size: 12px;
		}

		.site-footer .site-info {
			font-size: 12px;
		}

		.site-title {
			font-weight: 400;
		}

		h1,
		.entry-title,
		.footer-subscribe h3 {
			font-weight: 500;
		}

		h2,
		h3,
		h4,
		h5,
		h6,
		blockquote,
		.comment-meta .fn {
			font-weight: 500;
		}

		.slider-box .entry-title {
			font-weight: 700;
		}

		.widget-title {
			font-weight: 700;
		}

		@media screen and (min-width: 992px) {
			.nav-menu>ul {
				font-weight: 500;
			}
		}

		@media screen and (min-width: 992px) {
			.nav-menu ul ul {
				font-weight: 400;
			}
		}

		.link-box .entry-title {
			font-weight: 700;
		}

		.site-description {
			font-weight: 400;
		}

		.top-bar {
			font-weight: 500;
		}

		.site-footer .site-info {
			font-weight: 500;
		}

		.entry-meta {
			font-weight: 500;
		}

		@media screen and (min-width: 992px) {
			.nav-menu>ul {
				letter-spacing: 0px;
			}
		}

		@media screen and (min-width: 992px) {
			.nav-menu ul ul {
				letter-spacing: 0px;
			}
		}

		.widget-title {
			letter-spacing: 3px;
		}

		.site-footer .site-info {
			letter-spacing: 3px;
		}

		h1,
		.entry-title,
		.footer-subscribe h3,
		.widget_categories ul li,
		.widget_recent_entries ul li,
		.widget_pages ul li,
		.widget_archive ul li,
		.widget_calendar table caption,
		.tptn_title,
		.nav-single a {
			text-transform: none;
		}

		h2,
		h3,
		h4,
		h5,
		h6,
		blockquote,
		.comment-meta .fn {
			text-transform: none;
		}

		@media screen and (min-width: 992px) {
			html {
				line-height: 1.6;
			}
		}

		@media screen and (min-width: 992px) {
			.header-bg-shape {
				height: 50px;
			}
		}

		.header-wrap:after {
			bottom: -1px;
		}

		@media screen and (min-width: 992px) {
			.site-title img {
				max-height: 44px;
			}
		}

		@media screen and (max-width: 991px) {
			.site-title img {
				max-height: 24px;
			}
		}

		.top-bar {
			line-height: 36px;
		}

		html .site-header .site-title a {
			padding: 12px 24px;
		}

		@media screen and (min-width: 992px) {
			.site {
				margin-top: 0px;
				margin-bottom: 0px;
			}
		}

		.layout-medium,
		.is-header-row .header-wrap-inner,
		.is-header-small .header-wrap-inner,
		.is-menu-bar.is-menu-fixed-bg .menu-wrap,
		.is-header-fixed-width .header-wrap,
		.is-header-fixed-width.is-menu-bar .site-navigation,
		.is-header-float-box:not(.is-header-float-box-menu) .site-header:not(.clone) .header-wrap,
		.is-header-float-box.is-menu-bar .site-header:not(.clone) .site-navigation:not(.clone),
		.is-body-boxed .site,
		.is-body-boxed .header-wrap,
		.is-body-boxed.is-menu-bar .site-navigation,
		.is-body-boxed:not(.is-menu-bar) .site-header,
		.is-middle-boxed .site-main,
		.intro-content,
		.is-footer-boxed .site-footer,
		.is-content-boxed .site-main .layout-fixed,
		.top-bar .top-bar-wrap,
		.is-top-bar-fixed .top-bar,
		.is-top-bar-fixed-bg .top-bar,
		.is-menu-bottom.is-menu-bottom-overflow .site-header:not(.clone) .site-navigation:not(.clone) .menu-wrap,
		.site-branding-wrap,
		.is-header-border-fixed .header-wrap:after,
		.is-header-border-fixed .menu-wrap:after,
		html .tutor-container,
		html .lp-content-area,
		html .learn-press-breadcrumb {
			max-width: 1140px;
			margin-left: auto;
			margin-right: auto;
		}

		.layout-fixed,
		.blog-list,
		.blog-regular,
		.is-content-boxed .single .site-content,
		.is-content-boxed .page .site-content {
			max-width: 800px;
		}

		@media screen and (min-width: 992px) {

			.is-header-small .header-wrap,
			.is-menu-bar .nav-menu>ul>li,
			.is-header-vertical .nav-menu>ul>li {
				line-height: 80px;
			}

			.is-header-small .site-branding {
				max-height: 80px;
			}

		}

		@media screen and (min-width: 992px) {

			.is-header-small.is-header-smaller .site-header.clone .header-wrap,
			.is-header-row.is-header-smaller .site-header.clone .nav-menu>ul>li,
			.is-menu-bar.is-header-smaller .site-navigation.clone .nav-menu>ul>li,
			.is-menu-bar.is-header-smaller .site-header.clone .site-navigation .nav-menu>ul>li {
				line-height: 70px;
			}

			.is-header-small.is-header-smaller .site-header.clone .site-branding {
				max-height: 70px;
			}

			/* Fix for logo visibility in cloned header */
			.site-header.clone .site-title img {
				display: block !important;
				opacity: 1 !important;
				visibility: visible !important;
			}

			.site-header.clone .site-title-text {
				display: block !important;
				opacity: 1 !important;
				visibility: visible !important;
			}

			/* Ensure cloned header has proper background */
			.site-header.clone .header-wrap {
				background-color: #ffffff !important;
				opacity: 1 !important;
			}

			/* Make sure cloned header is visible when sticky */
			.site-header.clone.is-visible {
				display: block !important;
				opacity: 1 !important;
				visibility: visible !important;
			}

			/* Ensure logo is always visible on page load and refresh */
			.site-title img,
			.site-title-text img {
				display: block !important;
				opacity: 1 !important;
				visibility: visible !important;
				max-width: 100%;
				height: auto;
			}

			/* Force logo visibility in all header states */
			.site-header .site-title img,
			.site-header .site-title-text img {
				display: block !important;
				opacity: 1 !important;
				visibility: visible !important;
			}

		}

		.button.is-primary,
		.button.is-primary:after,
		html .elementor-button,
		.elementor-button.elementor-size-xs,
		.elementor-button.elementor-size-sm,
		.elementor-button.elementor-size-md,
		.elementor-button.elementor-size-lg,
		.elementor-button.elementor-size-xl,
		html .ekit-wid-con .elementskit-btn,
		html .ekit-wid-con .ekit_creative_button {
			border-radius: 0px;
		}

		.button.is-secondary,
		.button.is-secondary:after,
		.elementor-element.elementor-button-info .elementor-button {
			border-radius: 30px;
		}

		a {
			color: #d84156;
		}

		a:hover {
			color: #c60035;
		}

		.site-header .header-wrap {
			background-color: #fffefc;
		}

		html:not(.is-menu-bottom) .site-header .header-bg-shape {
			color: #fffefc;
		}

		.header-wrap:before {
			background: #fffefc;
		}

		.header-wrap:before {
			opacity: 0;
		}

		.is-header-half-transparent:not(.is-menu-toggled-on) .site-header:not(.clone) .header-wrap:before {
			opacity: 0.45;
		}

		.header-wrap:after {
			opacity: 0.08;
		}

		.site-header .menu-wrap {
			background-color: #ffffff;
		}

		html.is-menu-bottom .site-header .header-bg-shape {
			color: #ffffff;
		}

		@media screen and (min-width: 992px) {
			.nav-menu>ul>li.current-menu-item>a {
				color: #0a0a0a !important;
			}
		}

		@media screen and (min-width: 992px) {
			.nav-menu li.current-menu-item>a .link-text:before {
				background-color: #bbcfbd !important;
				border-color: #bbcfbd !important;
			}
		}

		@media screen and (min-width: 992px) {

			html .nav-menu>ul>li>a:hover,
			.nav-menu>ul>li.has-submenu:hover>a {
				color: #1c1a17;
			}
		}

		@media screen and (min-width: 992px) {

			html.loaded .nav-menu ul li a .link-text:before,
			.nav-menu li.has-submenu:hover>a .link-text:before {
				background-color: #d6cfc2;
				border-color: #d6cfc2;
			}
		}

		@media screen and (min-width: 992px) {
			.nav-menu ul ul li.current-menu-item>a .link-text {
				color: #0a0606 !important;
			}
		}

		@media screen and (min-width: 992px) {

			html .nav-menu ul ul li a:hover .link-text,
			.nav-menu ul ul li.has-submenu:hover>a .link-text {
				color: #111111;
			}
		}

		h1,
		h2,
		h3,
		h4,
		h5,
		h6,
		blockquote,
		.tab-titles {
			color: #020014;
		}

		body {
			color: #32455e;
		}

		body {
			background: #fffefc;
		}

		.site .footer-subscribe {
			background: #ebe6da;
		}

		.site-footer .site-info {
			background-color: #ffffff;
		}

		.site-footer .site-info {
			color: #0a0a0a;
		}

		.button.is-primary {
			color: #222222;
		}

		.button.is-primary.is-shadow,
		.button.is-primary.is-solid,
		.button.is-primary.is-solid-light,
		html .elementor-button,
		html .ekit-wid-con .elementskit-btn {
			background-color: #222222;
		}

		.button.is-primary.is-shadow {
			box-shadow: 0px 18px 23px -6px #222222;
		}

		.button.is-primary:hover {
			color: #215aed;
		}

		.button.is-primary.is-shadow:hover,
		.button.is-primary.is-solid:hover,
		.button.is-primary.is-solid-light:hover,
		.button.is-primary.is-shift:after,
		.button.is-primary.is-circle:before,
		html .elementor-button:hover,
		html .ekit-wid-con .elementskit-btn:hover {
			background-color: #215aed;
		}

		.button.is-primary.is-shadow:hover {
			box-shadow: 0px 2px 10px -5px #215aed;
		}

		.button.is-secondary {
			color: #f22000;
		}

		.button.is-secondary.is-shadow,
		.button.is-secondary.is-solid,
		.button.is-secondary.is-solid-light,
		.elementor-element.elementor-button-info .elementor-button {
			background-color: #f22000;
		}

		.button.is-secondary.is-shadow {
			box-shadow: 0px 18px 23px -6px #f22000;
		}

		.button.is-secondary:hover {
			color: #0026ff;
		}

		.button.is-secondary.is-shadow:hover,
		.button.is-secondary.is-solid:hover,
		.button.is-secondary.is-solid-light:hover,
		.button.is-secondary.is-shift:after,
		.button.is-secondary.is-circle:before,
		.elementor-element.elementor-button-info .elementor-button:hover {
			background-color: #0026ff;
		}

		.button.is-secondary.is-shadow:hover {
			box-shadow: 0px 2px 10px -5px #0026ff;
		}

		.top-bar,
		.top-bar select option {
			background-color: #262251;
		}

		.top-bar {
			background: linear-gradient(90deg, #262251 30%, #d10600 100%);
		}

		.header-wrap:after {
			color: #222222;
		}

		.is-menu-bar .menu-wrap:after {
			color: #222222;
		}
	</style>
	<link rel='stylesheet' id='swiper-css'
		href='./wp-content/plugins/qi-addons-for-elementor/assets/plugins/swiper/8.4.5/swiper.min94a4.css?ver=8.4.5'
		type='text/css' media='all' />
	<link rel='stylesheet' id='elementor-frontend-css'
		href='./wp-content/plugins/elementor/assets/css/frontend.min0d09.css?ver=3.29.0' type='text/css' media='all' />
	<link rel='stylesheet' id='elementor-post-6-css'
		href='./wp-content/uploads/sites/2/elementor/css/post-65ff0.css?ver=1748102204' type='text/css' media='all' />
	<link rel='stylesheet' id='ps-general-css'
		href='./wp-content/plugins/bdthemes-prime-slider-lite/assets/css/ps-generalca7c.css?ver=3.17.12' type='text/css'
		media='all' />
	<link rel='stylesheet' id='e-animation-fadeIn-css'
		href='./wp-content/plugins/elementor/assets/lib/animations/styles/fadeIn.min0d09.css?ver=3.29.0' type='text/css'
		media='all' />
	<link rel='stylesheet' id='widget-heading-css'
		href='./wp-content/plugins/elementor/assets/css/widget-heading.min0d09.css?ver=3.29.0' type='text/css'
		media='all' />
	<link rel='stylesheet' id='e-animation-fadeInLeft-css'
		href='./wp-content/plugins/elementor/assets/lib/animations/styles/fadeInLeft.min0d09.css?ver=3.29.0'
		type='text/css' media='all' />
	<link rel='stylesheet' id='e-animation-fadeInUp-css'
		href='./wp-content/plugins/elementor/assets/lib/animations/styles/fadeInUp.min0d09.css?ver=3.29.0'
		type='text/css' media='all' />
	<link rel='stylesheet' id='ps-blog-css'
		href='./wp-content/plugins/bdthemes-prime-slider-lite/assets/css/ps-blogca7c.css?ver=3.17.12' type='text/css'
		media='all' />
	<link rel='stylesheet' id='mediaelement-css'
		href='./wp-includes/js/mediaelement/mediaelementplayer-legacy.min1f61.css?ver=4.2.17' type='text/css'
		media='all' />
	<link rel='stylesheet' id='wp-mediaelement-css'
		href='./wp-includes/js/mediaelement/wp-mediaelement.min0899.css?ver=6.8.1' type='text/css' media='all' />
	<link rel='stylesheet' id='widget-spacer-css'
		href='./wp-content/plugins/elementor/assets/css/widget-spacer.min0d09.css?ver=3.29.0' type='text/css'
		media='all' />
	<link rel='stylesheet' id='elementor-post-17837-css'
		href='./wp-content/uploads/sites/2/elementor/css/post-178377a04.css?ver=1748345376' type='text/css'
		media='all' />
	<link rel='stylesheet' id='bdt-uikit-css'
		href='./wp-content/plugins/bdthemes-prime-slider-lite/assets/css/bdt-uikit4ecf.css?ver=3.21.7' type='text/css'
		media='all' />
	<link rel='stylesheet' id='prime-slider-site-css'
		href='./wp-content/plugins/bdthemes-prime-slider-lite/assets/css/prime-slider-siteca7c.css?ver=3.17.12'
		type='text/css' media='all' />
	<link rel='stylesheet' id='elementor-gf-local-roboto-css'
		href='./wp-content/uploads/sites/2/elementor/google-fonts/css/roboto9eda.css?ver=1745821354' type='text/css'
		media='all' />
	<link rel='stylesheet' id='elementor-gf-local-robotoslab-css'
		href='./wp-content/uploads/sites/2/elementor/google-fonts/css/robotoslab7d09.css?ver=1745821356' type='text/css'
		media='all' />
	<script type="text/javascript" src="./wp-includes/js/jquery/jquery.minf43b.js?ver=3.7.1"
		id="jquery-core-js"></script>
	<script type="text/javascript" src="./wp-includes/js/jquery/jquery-migrate.min5589.js?ver=3.4.1"
		id="jquery-migrate-js"></script>
	<script type="text/javascript"
		src="./wp-content/plugins/bdthemes-prime-slider-lite/assets/js/bdt-uikit.min4ecf.js?ver=3.21.7"
		id="bdt-uikit-js"></script>
	<link rel="https://api.w.org/" href="https://themes.pixelwars.org/interique/demo-01/wp-json/" />
	<link rel="alternate" title="JSON" type="application/json"
		href="https://themes.pixelwars.org/interique/demo-01/wp-json/wp/v2/pages/17837" />
	<link rel="EditURI" type="application/rsd+xml" title="RSD"
		href="https://themes.pixelwars.org/interique/demo-01/xmlrpc.php?rsd" />
	<meta name="generator" content="WordPress 6.8.1" />
	<link rel="canonical" href="index.html" />
	<link rel='shortlink' href='https://themes.pixelwars.org/interique/demo-01/?p=17837' />
	<link rel="alternate" title="oEmbed (JSON)" type="application/json+oembed"
		href="https://themes.pixelwars.org/interique/demo-01/wp-json/oembed/1.0/embed?url=https%3A%2F%2Fthemes.pixelwars.org%2Finterique%2Fdemo-01%2Fhome-03%2F" />
	<link rel="alternate" title="oEmbed (XML)" type="text/xml+oembed"
		href="https://themes.pixelwars.org/interique/demo-01/wp-json/oembed/1.0/embed?url=https%3A%2F%2Fthemes.pixelwars.org%2Finterique%2Fdemo-01%2Fhome-03%2F&amp;format=xml" />
	<meta name="generator"
		content="Elementor 3.29.0; features: e_font_icon_svg, additional_custom_breakpoints, e_local_google_fonts, e_element_cache; settings: css_print_method-external, google_font-enabled, font_display-swap">
	<style>
		.e-con.e-parent:nth-of-type(n+4):not(.e-lazyloaded):not(.e-no-lazyload),
		.e-con.e-parent:nth-of-type(n+4):not(.e-lazyloaded):not(.e-no-lazyload) * {
			background-image: none !important;
		}

		@media screen and (max-height: 1024px) {

			.e-con.e-parent:nth-of-type(n+3):not(.e-lazyloaded):not(.e-no-lazyload),
			.e-con.e-parent:nth-of-type(n+3):not(.e-lazyloaded):not(.e-no-lazyload) * {
				background-image: none !important;
			}
		}

		@media screen and (max-height: 640px) {

			.e-con.e-parent:nth-of-type(n+2):not(.e-lazyloaded):not(.e-no-lazyload),
			.e-con.e-parent:nth-of-type(n+2):not(.e-lazyloaded):not(.e-no-lazyload) * {
				background-image: none !important;
			}
		}
	</style>
</head>

<body
	class="wp-singular page-template page-template-elementor_header_footer page page-id-17837 wp-theme-interique qodef-qi--no-touch qi-addons-for-elementor-1.8.9 elementor-default elementor-template-full-width elementor-kit-6 elementor-page elementor-page-17837">
	<div id="page" class="hfeed site">
		<header id="masthead" class="site-header" role="banner">
			<div class="header-wrap" data-parallax-video="">
				<div class="header-wrap-inner">
					<div class="site-branding">
						<div class="site-branding-wrap">
							<div class="site-branding-left">
							</div> <!-- .site-branding-left -->

							<div class="site-branding-center">
								<h1 class="site-title">
									<a href="index.html" rel="home">
										<!-- <img src="./assets/logo.png" alt=""> -->
										<!-- <span class="screen-reader-text">
											Motiff Square </span> -->
										<span class="site-title-text">
											<img src="./assets/logo.png" alt="">
										</span>
									</a>
									
								</h1> <!-- .site-title -->
								<p class="site-description">
									Freelance Interior Designer – Personal, creative, and tailored design for homes and
									boutique spaces.
								</p> <!-- .site-description -->
							</div> <!-- .site-branding-center -->
							<div class="site-branding-right">
							</div> <!-- .site-branding-right -->
						</div> <!-- .site-branding-wrap -->
					</div> <!-- .site-branding -->

					<nav id="site-navigation" class="main-navigation site-navigation" role="navigation">
						<div class="menu-wrap">
							<div class="layout-medium">
								<a class="menu-toggle">
									<span class="lines"></span>
								</a> <!-- .menu-toggle -->
								<div class="nav-menu">
									<ul id="menu-mymenu" class="">
										<li id="menu-item-15712"
											class="menu-item menu-item-type-post_type menu-item-object-page menu-item-home current-menu-ancestor current-menu-parent current_page_parent current_page_ancestor menu-item-has-children menu-item-15712">
											<a href="index.html">Home</a>
											<!-- <ul class="sub-menu">
												<li id="menu-item-18584"
													class="menu-item menu-item-type-post_type menu-item-object-page menu-item-home menu-item-18584">
													<a href="index.html">Home
														01</a></li>
												<li id="menu-item-18345"
													class="menu-item menu-item-type-post_type menu-item-object-page menu-item-18345">
													<a href="https://themes.pixelwars.org/interique/demo-01/home-02/">Home
														02</a></li>
												<li id="menu-item-18342"
													class="menu-item menu-item-type-post_type menu-item-object-page current-menu-item page_item page-item-17837 current_page_item menu-item-18342">
													<a href="index.html" aria-current="page">Home 03</a></li>
												<li id="menu-item-18343"
													class="menu-item menu-item-type-post_type menu-item-object-page menu-item-18343">
													<a href="https://themes.pixelwars.org/interique/demo-01/home-04/">Home
														04</a></li>
												<li id="menu-item-18344"
													class="menu-item menu-item-type-post_type menu-item-object-page menu-item-18344">
													<a href="https://themes.pixelwars.org/interique/demo-01/home-05/">Home
														05</a></li>
											</ul> -->
										</li>
										<li id="menu-item-18246"
											class="menu-item menu-item-type-post_type menu-item-object-page menu-item-18246">
											<a href="about.html">About
												Us</a>
										</li>
										<li id="menu-item-18245"
											class="menu-item menu-item-type-post_type menu-item-object-page menu-item-18245">
											<a href="service.html">Services</a>
										</li>
										<li id="menu-item-15714"
											class="menu-item menu-item-type-post_type menu-item-object-page menu-item-15714">
											<a href="portfolio.html">Portfolio</a>
										</li>

										<li id="menu-item-18244"
											class="menu-item menu-item-type-post_type menu-item-object-page menu-item-18244">
											<a href="contact.html">Contact</a>
										</li>
									</ul>
								</div> <a class="search-toggle toggle-link"></a>


								<!-- <div class="social-container widget-area">
									<a class="social-link instagram" target="_blank" href="#"></a>
									<a class="social-link twitter" target="_blank" href="#"></a>
								</div>  -->
							</div> <!-- .layout-medium -->
						</div> <!-- .menu-wrap -->
					</nav> <!-- #site-navigation .main-navigation .site-navigation -->
				</div> <!-- .header-wrap-inner -->
			</div> <!-- .header-wrap -->
		</header> <!-- #masthead .site-header -->

		<div data-elementor-type="wp-page" data-elementor-id="17837" class="elementor elementor-17837">
			<div class="elementor-element elementor-element-1725d48 e-con-full e-flex e-con e-parent" data-id="1725d48"
				data-element_type="container">
				<div class="elementor-element elementor-element-9e51013 elementor-widget-tablet__width-inherit full elementor-widget elementor-widget-prime-slider-general"
					data-id="9e51013" data-element_type="widget" data-widget_type="prime-slider-general.crelly">
					<div class="elementor-widget-container">
						<div class="bdt-prime-slider">
							<div class="bdt-prime-slider-skin-crelly">
								<div class="bdt-position-relative bdt-visible-toggle"
									bdt-slideshow="{&quot;animation&quot;:&quot;push&quot;,&quot;ratio&quot;:&quot;32:14&quot;,&quot;min-height&quot;:650,&quot;autoplay&quot;:true,&quot;autoplay-interval&quot;:&quot;5000&quot;,&quot;pause-on-hover&quot;:true,&quot;draggable&quot;:true,&quot;velocity&quot;:1,&quot;finite&quot;:false}"
									data-settings="{&quot;id&quot;:&quot;#bdt-9e51013&quot;,&quot;animation_status&quot;:&quot;no&quot;}"
									id="bdt-9e51013">
									<ul class="bdt-slideshow-items">

										<li
											class="bdt-slideshow-item bdt-flex bdt-flex-middle bdt-flex-center elementor-repeater-item-fd40857">
											<div
												class="bdt-position-cover bdt-animation-kenburns bdt-transform-origin-center-left">


												<div class="bdt-ps-slide-img"
													style="background-image: url('./wp-content/uploads/sites/2/2025/04/Modern-Sage-Armchair-2-1920x1200.jpg')">
												</div>


											</div>

											<div class="bdt-overlay-default bdt-position-cover"></div>


											<div class="bdt-prime-slider-wrapper">
												<div class="bdt-prime-slider-content">

													<div class="bdt-prime-slider-desc">

														<div class="bdt-sub-title bdt-text-left">
															<h4 data-bdt-slideshow-parallax="x: 300,0,-100; opacity: 1,1,0"
																data-reveal="reveal-active" class="bdt-ps-sub-title">
																Furniture Selection </h4>
														</div>

														<div class="bdt-main-title bdt-text-left"
															data-bdt-slideshow-parallax="x: 500,0,-100; opacity: 1,1,0"
															data-reveal="reveal-active">
															<h1 class="bdt-title-tag">
																Futuristic Designs </h1>
														</div>

													</div>

												</div>
												<div class="bdt-slider-excerpt-content"
													data-bdt-slideshow-parallax="y: 200,0,-100; opacity: 1,1,0">
													<div class="bdt-slider-excerpt" data-reveal="reveal-active">
														<p>Motiff Square redefines luxury
															interiors with futuristic concepts,
															personalized styling, and functional innovation — made for
															the
															spaces of tomorow.
														</p>
													</div>
													<div class="bdt-crelly-btn"
														data-bdt-slideshow-parallax="y: 200,0,-100; opacity: 1,1,0">


														<a class="bdt-slide-btn" data-reveal="reveal-active"
															href="portfolio.html">

															<span class="bdt-prime-slider-button-wrapper">

																<span
																	class="bdt-prime-slider-button-text bdt-flex bdt-flex-middle bdt-flex-inline">Portfolio
																	<span class="bdt-slide-btn-icon"><svg width="20"
																			height="20" viewBox="0 0 20 20"
																			xmlns="http://www.w3.org/2000/svg"
																			data-svg="arrow-right">
																			<polyline fill="none" stroke="#000"
																				points="10 5 15 9.5 10 14"></polyline>
																			<line fill="none" stroke="#000" x1="4"
																				y1="9.5" x2="15" y2="9.5"></line>
																		</svg></span>
																</span>

															</span>

														</a>
													</div>
												</div>
											</div>
										</li>


										<li
											class="bdt-slideshow-item bdt-flex bdt-flex-middle bdt-flex-center elementor-repeater-item-5c23b26">
											<div
												class="bdt-position-cover bdt-animation-kenburns bdt-transform-origin-center-left">


												<div class="bdt-ps-slide-img"
													style="background-image: url('./wp-content/uploads/sites/2/2025/04/stylish-compositon-modern-living-room-interior-with-frotte-armchair-sofa-plants-painting-wooden-commode-side-table-elegant-ho')">
												</div>


											</div>

											<div class="bdt-overlay-default bdt-position-cover"></div>


											<div class="bdt-prime-slider-wrapper">
												<div class="bdt-prime-slider-content">

													<div class="bdt-prime-slider-desc">

														<div class="bdt-sub-title bdt-text-left">
															<h4 data-bdt-slideshow-parallax="x: 300,0,-100; opacity: 1,1,0"
																data-reveal="reveal-active" class="bdt-ps-sub-title">
																Space Planning </h4>
														</div>

														<div class="bdt-main-title bdt-text-left"
															data-bdt-slideshow-parallax="x: 500,0,-100; opacity: 1,1,0"
															data-reveal="reveal-active">
															<h1 class="bdt-title-tag">
																Unique Creations </h1>
														</div>

													</div>

												</div>
												<div class="bdt-slider-excerpt-content"
													data-bdt-slideshow-parallax="y: 200,0,-100; opacity: 1,1,0">
													<div class="bdt-slider-excerpt" data-reveal="reveal-active">
														<p>At Motiff Square Studio, we specialize in designing spaces
															that inspire
															and elevate your everyday life. Let’s create something
															extraordinary together with style and purpose.</p>
													</div>
													<div class="bdt-crelly-btn"
														data-bdt-slideshow-parallax="y: 200,0,-100; opacity: 1,1,0">


														<a class="bdt-slide-btn" data-reveal="reveal-active"
															href="about.html">

															<span class="bdt-prime-slider-button-wrapper">

																<span
																	class="bdt-prime-slider-button-text bdt-flex bdt-flex-middle bdt-flex-inline">About
																	Us <span class="bdt-slide-btn-icon"><svg width="20"
																			height="20" viewBox="0 0 20 20"
																			xmlns="http://www.w3.org/2000/svg"
																			data-svg="arrow-right">
																			<polyline fill="none" stroke="#000"
																				points="10 5 15 9.5 10 14"></polyline>
																			<line fill="none" stroke="#000" x1="4"
																				y1="9.5" x2="15" y2="9.5"></line>
																		</svg></span>
																</span>

															</span>

														</a>
													</div>
												</div>
											</div>
										</li>


										<li
											class="bdt-slideshow-item bdt-flex bdt-flex-middle bdt-flex-center elementor-repeater-item-9f9a850">
											<div
												class="bdt-position-cover bdt-animation-kenburns bdt-transform-origin-center-left">


												<div class="bdt-ps-slide-img"
													style="background-image: url('./wp-content/uploads/sites/2/2025/04/Modern-Translucent-Green-Chair-1920x1200.jpg')">
												</div>


											</div>

											<div class="bdt-overlay-default bdt-position-cover"></div>


											<div class="bdt-prime-slider-wrapper">
												<div class="bdt-prime-slider-content">

													<div class="bdt-prime-slider-desc">

														<div class="bdt-sub-title bdt-text-left">
															<h4 data-bdt-slideshow-parallax="x: 300,0,-100; opacity: 1,1,0"
																data-reveal="reveal-active" class="bdt-ps-sub-title">
																Lighting Solutions </h4>
														</div>

														<div class="bdt-main-title bdt-text-left"
															data-bdt-slideshow-parallax="x: 500,0,-100; opacity: 1,1,0"
															data-reveal="reveal-active">
															<h1 class="bdt-title-tag">
																Elegant Living </h1>
														</div>

													</div>

												</div>
												<div class="bdt-slider-excerpt-content"
													data-bdt-slideshow-parallax="y: 200,0,-100; opacity: 1,1,0">
													<div class="bdt-slider-excerpt" data-reveal="reveal-active">
														<p>Welcome to Motiff Square Interior Design, where creativity
															meets
															functionality. We transform spaces into personalized
															masterpieces that reflect your unique style, vision, and
															lifestyle.</p>
													</div>
													<div class="bdt-crelly-btn"
														data-bdt-slideshow-parallax="y: 200,0,-100; opacity: 1,1,0">


														<a class="bdt-slide-btn" data-reveal="reveal-active"
															href="about.html">

															<span class="bdt-prime-slider-button-wrapper">

																<span
																	class="bdt-prime-slider-button-text bdt-flex bdt-flex-middle bdt-flex-inline">About
																	Us <span class="bdt-slide-btn-icon"><svg width="20"
																			height="20" viewBox="0 0 20 20"
																			xmlns="http://www.w3.org/2000/svg"
																			data-svg="arrow-right">
																			<polyline fill="none" stroke="#000"
																				points="10 5 15 9.5 10 14"></polyline>
																			<line fill="none" stroke="#000" x1="4"
																				y1="9.5" x2="15" y2="9.5"></line>
																		</svg></span>
																</span>

															</span>

														</a>
													</div>
												</div>
											</div>
										</li>


										<li
											class="bdt-slideshow-item bdt-flex bdt-flex-middle bdt-flex-center elementor-repeater-item-36a678d">
											<div
												class="bdt-position-cover bdt-animation-kenburns bdt-transform-origin-center-left">


												<div class="bdt-ps-slide-img"
													style="background-image: url('./wp-content/uploads/sites/2/2025/04/3d-rendering-loft-style-living-room-interior-design.jpg')">
												</div>


											</div>

											<div class="bdt-overlay-default bdt-position-cover"></div>


											<div class="bdt-prime-slider-wrapper">
												<div class="bdt-prime-slider-content">

													<div class="bdt-prime-slider-desc">

														<div class="bdt-sub-title bdt-text-left">
															<h4 data-bdt-slideshow-parallax="x: 300,0,-100; opacity: 1,1,0"
																data-reveal="reveal-active" class="bdt-ps-sub-title">
																Color Harmony </h4>
														</div>

														<div class="bdt-main-title bdt-text-left"
															data-bdt-slideshow-parallax="x: 500,0,-100; opacity: 1,1,0"
															data-reveal="reveal-active">
															<h1 class="bdt-title-tag">
																Functional Beauty </h1>
														</div>

													</div>

												</div>
												<div class="bdt-slider-excerpt-content"
													data-bdt-slideshow-parallax="y: 200,0,-100; opacity: 1,1,0">
													<div class="bdt-slider-excerpt" data-reveal="reveal-active">
														<p>Welcome to Motiff Square, where your vision comes to life
															through
															innovative design solutions. Together, we’ll create
															beautiful, functional spaces that reflect your unique
															personality.</p>
													</div>
													<div class="bdt-crelly-btn"
														data-bdt-slideshow-parallax="y: 200,0,-100; opacity: 1,1,0">


														<a class="bdt-slide-btn" data-reveal="reveal-active"
															href="service.html">

															<span class="bdt-prime-slider-button-wrapper">

																<span
																	class="bdt-prime-slider-button-text bdt-flex bdt-flex-middle bdt-flex-inline">Services
																	<span class="bdt-slide-btn-icon"><svg width="20"
																			height="20" viewBox="0 0 20 20"
																			xmlns="http://www.w3.org/2000/svg"
																			data-svg="arrow-right">
																			<polyline fill="none" stroke="#000"
																				points="10 5 15 9.5 10 14"></polyline>
																			<line fill="none" stroke="#000" x1="4"
																				y1="9.5" x2="15" y2="9.5"></line>
																		</svg></span>
																</span>

															</span>

														</a>
													</div>
												</div>
											</div>
										</li>


									</ul>



									<div
										class="bdt-navigation-arrows bdt-position-bottom-left bdt-margin-large-bottom reveal-muted">
										<a class="bdt-prime-slider-previous" href="#" bdt-slidenav-previous
											bdt-slideshow-item="previous"></a>

										<a class="bdt-prime-slider-next" href="#" bdt-slidenav-next
											bdt-slideshow-item="next"></a>

										<ul class="bdt-ps-counternav bdt-position-bottom bdt-visible@s">
											<li bdt-slideshow-item="0" data-label="01"><a href="#">01</a></li>
											<li bdt-slideshow-item="1" data-label="02"><a href="#">02</a></li>
											<li bdt-slideshow-item="2" data-label="03"><a href="#">03</a></li>
											<li bdt-slideshow-item="3" data-label="04"><a href="#">04</a></li>

											<span>04</span>
										</ul>

									</div>




									<ul
										class="bdt-slideshow-nav bdt-dotnav bdt-dotnav-vertical bdt-position-center-left reveal-muted">
									</ul>



								</div>


								<div
									class="bdt-prime-slider-social-icon reveal-muted bdt-flex bdt-flex-middle bdt-margin-large-bottom bdt-position-bottom-right">

									<h3>Follow Us</h3>

									<a class="bdt-social-animate" href="#" data-bdt-tooltip="title: Twitter; pos: top">
										<svg aria-hidden="true" class="fa-fw e-font-icon-svg e-fab-x-twitter"
											viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
											<path
												d="M389.2 48h70.6L305.6 224.2 487 464H345L233.7 318.6 106.5 464H35.8L200.7 275.5 26.8 48H172.4L272.9 180.9 389.2 48zM364.4 421.8h39.1L151.1 88h-42L364.4 421.8z">
											</path>
										</svg> </a>
									<a class="bdt-social-animate" href="#"
										data-bdt-tooltip="title: Instagram; pos: top">
										<svg aria-hidden="true" class="fa-fw e-font-icon-svg e-fab-instagram"
											viewBox="0 0 448 512" xmlns="http://www.w3.org/2000/svg">
											<path
												d="M224.1 141c-63.6 0-114.9 51.3-114.9 114.9s51.3 114.9 114.9 114.9S339 319.5 339 255.9 287.7 141 224.1 141zm0 189.6c-41.1 0-74.7-33.5-74.7-74.7s33.5-74.7 74.7-74.7 74.7 33.5 74.7 74.7-33.6 74.7-74.7 74.7zm146.4-194.3c0 14.9-12 26.8-26.8 26.8-14.9 0-26.8-12-26.8-26.8s12-26.8 26.8-26.8 26.8 12 26.8 26.8zm76.1 27.2c-1.7-35.9-9.9-67.7-36.2-93.9-26.2-26.2-58-34.4-93.9-36.2-37-2.1-147.9-2.1-184.9 0-35.8 1.7-67.6 9.9-93.9 36.1s-34.4 58-36.2 93.9c-2.1 37-2.1 147.9 0 184.9 1.7 35.9 9.9 67.7 36.2 93.9s58 34.4 93.9 36.2c37 2.1 147.9 2.1 184.9 0 35.9-1.7 67.7-9.9 93.9-36.2 26.2-26.2 34.4-58 36.2-93.9 2.1-37 2.1-147.8 0-184.8zM398.8 388c-7.8 19.6-22.9 34.7-42.6 42.6-29.5 11.7-99.5 9-132.1 9s-102.7 2.6-132.1-9c-19.6-7.8-34.7-22.9-42.6-42.6-11.7-29.5-9-99.5-9-132.1s-2.6-102.7 9-132.1c7.8-19.6 22.9-34.7 42.6-42.6 29.5-11.7 99.5-9 132.1-9s102.7-2.6 132.1 9c19.6 7.8 34.7 22.9 42.6 42.6 11.7 29.5 9 99.5 9 132.1s2.7 102.7-9 132.1z">
											</path>
										</svg> </a>
								</div>

							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="elementor-element elementor-element-185a85d5 e-flex e-con-boxed e-con e-parent"
				data-id="185a85d5" data-element_type="container">
				<div class="e-con-inner">
					<div class="elementor-element elementor-element-52277864 e-con-full e-flex e-con e-child"
						data-id="52277864" data-element_type="container">
						<div class="elementor-element elementor-element-2a38527 elementor-invisible elementor-widget elementor-widget-qi_addons_for_elementor_animated_text"
							data-id="2a38527" data-element_type="widget"
							data-settings="{&quot;_animation&quot;:&quot;fadeIn&quot;}"
							data-widget_type="qi_addons_for_elementor_animated_text.default">
							<div class="elementor-widget-container">
								<div
									class="qodef-shortcode qodef-m qodef-qi-animated-text qodef--animated-by-letter qodef-qi--has-appear qodef--appear-fade">
									<h1 class="qodef-m-title">
										<span class="qodef-e-word-holder">Tailored</span> <span
											class="qodef-e-word-holder">Interior</span> <span
											class="qodef-e-word-holder">Solutions</span>
									</h1>
								</div>
							</div>
						</div>
					</div>
					<div class="elementor-element elementor-element-197a874a e-con-full e-flex e-con e-child"
						data-id="197a874a" data-element_type="container">
						<div class="elementor-element elementor-element-586a2f elementor-widget elementor-widget-qi_addons_for_elementor_animated_text"
							data-id="586a2f" data-element_type="widget"
							data-widget_type="qi_addons_for_elementor_animated_text.default">
							<div class="elementor-widget-container">
								<div
									class="qodef-shortcode qodef-m qodef-qi-animated-text qodef--animated-by-letter qodef-qi--has-appear qodef--appear-fade">
									<h3 class="qodef-m-title">
										<span class="qodef-e-word-holder"></span> <span
											class="qodef-e-word-holder">About</span> <span
											class="qodef-e-word-holder">Us</span>
									</h3>
								</div>
							</div>
						</div>
						<div class="elementor-element elementor-element-341bd562 animated-slow elementor-invisible elementor-widget elementor-widget-heading"
							data-id="341bd562" data-element_type="widget"
							data-settings="{&quot;_animation&quot;:&quot;fadeIn&quot;}"
							data-widget_type="heading.default">
							<div class="elementor-widget-container">
								<p class="elementor-heading-title elementor-size-default">At Motiff Square, we go beyond
									design — we
									create personalized, luxurious spaces that reflect
									your lifestyle and aspirations. With deep industry
									expertise and a detail-driven approach, our work
									transforms visions into elegant, functional realities.</p>
							</div>
						</div>
						<div class="elementor-element elementor-element-9b31a19 elementor-widget elementor-widget-qi_addons_for_elementor_button"
							data-id="9b31a19" data-element_type="widget"
							data-widget_type="qi_addons_for_elementor_button.default">
							<div class="elementor-widget-container">
								<a class="qodef-shortcode qodef-m qodef-qi-button qodef-html--link qodef-layout--textual qodef-icon--right qodef-hover--icon-move-horizontal-short"
									href="about.html" target="_self">
									<span class="qodef-m-text">More About Us</span>
									<span class="qodef-m-icon">
										<span class="qodef-m-icon-inner">
											<svg xmlns="http://www.w3.org/2000/svg" height="21" viewBox="0 0 21 21"
												width="21">
												<g fill="none" fill-rule="evenodd" stroke="currentColor"
													stroke-linecap="round" stroke-linejoin="round"
													transform="translate(6 6)">
													<path d="m8.5 7.5v-7h-7"></path>
													<path d="m8.5.5-8 8"></path>
												</g>
											</svg> </span>
									</span>
								</a>
							</div>
						</div>
					</div>
					<div class="elementor-element elementor-element-21f5eed e-con-full e-flex e-con e-child"
						data-id="21f5eed" data-element_type="container">
						<div class="elementor-element elementor-element-9a11dfe e-con-full e-flex e-con e-child"
							data-id="9a11dfe" data-element_type="container">
							<div class="elementor-element elementor-element-401191f animated-slow elementor-invisible elementor-widget elementor-widget-qi_addons_for_elementor_cards_slider"
								data-id="401191f" data-element_type="widget"
								data-settings="{&quot;_animation&quot;:&quot;fadeInLeft&quot;}"
								data-widget_type="qi_addons_for_elementor_cards_slider.default">
								<div class="elementor-widget-container">
									<div class="qodef-shortcode qodef-m qodef-qi-cards-slider qodef-orientation--one-side qodef-navigation--hover-move qodef-orientation--left"
										data-orientation="left" data-offset="25">
										<div class="qodef-e-inner">
											<div class="qodef-m-card">
												<div class="qodef-m-bundle-item">
													<img fetchpriority="high" decoding="async"
														src="./wp-content/uploads/sites/2/2025/04/stylish-compositon-modern-living-room-interior-with-frotte-armchair-sofa-plants-painting-wooden-commode-side-table-elegant-ho-2"
														alt="" width="1000" height="1000" />
												</div>
											</div>
											<div class="qodef-m-card">
												<div class="qodef-m-bundle-item">
													<img decoding="async"
														src="./wp-content/uploads/sites/2/2025/04/stylish-composition-creative-cozy-living-room-interior-with-grey-sofa-coffee-table-plants-carpet-beautiful-accessories-white-"
														alt="" width="1000" height="1000" />
												</div>
											</div>
											<div class="qodef-m-card">
												<div class="qodef-m-bundle-item">
													<img decoding="async"
														src="./wp-content/uploads/sites/2/2025/04/minimalist-composition-modern-living-room-interior-details-creative-vases-wooden-side-table-home-staging-template-copy-space-"
														alt="" width="1000" height="1000" />
												</div>
											</div>
										</div>
										<div class="qodef-m-fake-card">
											<img decoding="async"
												src="./wp-content/uploads/sites/2/2025/04/minimalist-composition-modern-living-room-interior-details-creative-vases-wooden-side-table-home-staging-template-copy-space-"
												alt="" width="1000" height="1000" />
										</div>
										<div class="qodef-m-navigation">
											<div class="qodef-nav qodef--prev"><svg class="qodef-swiper-arrow-left"
													xmlns="http://www.w3.org/2000/svg" x="0px" y="0px"
													viewBox="0 0 34.2 32.3" xml:space="preserve"
													style="stroke-width: 2;">
													<line x1="0.5" y1="16" x2="33.5" y2="16" />
													<line x1="0.3" y1="16.5" x2="16.2" y2="0.7" />
													<line x1="0" y1="15.4" x2="16.2" y2="31.6" />
												</svg></div>
											<div class="qodef-nav qodef--next"><svg class="qodef-swiper-arrow-right"
													xmlns="http://www.w3.org/2000/svg" x="0px" y="0px"
													viewBox="0 0 34.2 32.3" xml:space="preserve"
													style="stroke-width: 2;">
													<line x1="0" y1="16" x2="33" y2="16" />
													<line x1="17.3" y1="0.7" x2="33.2" y2="16.5" />
													<line x1="17.3" y1="31.6" x2="33.5" y2="15.4" />
												</svg></div>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="elementor-element elementor-element-9185c89 e-con-full e-flex e-con e-child"
							data-id="9185c89" data-element_type="container">
							<div class="elementor-element elementor-element-0f3f3db animated-slow elementor-invisible elementor-widget elementor-widget-qi_addons_for_elementor_tabs_horizontal"
								data-id="0f3f3db" data-element_type="widget"
								data-settings="{&quot;_animation&quot;:&quot;fadeIn&quot;}"
								data-widget_type="qi_addons_for_elementor_tabs_horizontal.default">
								<div class="elementor-widget-container">
									<div
										class="qodef-shortcode qodef-m qodef-qi-tabs-horizontal qodef-qi-clear qodef-layout--simple qodef-title-hover--underline-draw qodef-title-underline-from-left">
										<ul class="qodef-tabs-horizontal-navigation">
											<li>
												<h3 class="qodef-tab-title" role="tab">
													<a href="#qodef-tab-de6d4f0">
														<span class="q-text">
															History </span>
													</a>
												</h3>
											</li>
											<li>
												<h3 class="qodef-tab-title" role="tab">
													<a href="#qodef-tab-0927390">
														<span class="q-text">
															Mission </span>
													</a>
												</h3>
											</li>
											<li>
												<h3 class="qodef-tab-title" role="tab">
													<a href="#qodef-tab-b0d90cc">
														<span class="q-text">
															Vision </span>
													</a>
												</h3>
											</li>
										</ul>
										<div class="qodef-tabs-horizontal-content" id="qodef-tab-de6d4f0">
											<p>Motiff Square began as a bold vision to redefine the interior design
												experience.
												Founded by Mr. Avinash Mane, with over 15 years of industry expertise,
												we
												started as a sister concern of Sharayu Interiors & Turnkey Contractors.
											</p>
											<p>What began with small-scale fit-outs has now grown into a full-service
												luxury
												interior design firm — trusted by premium clients and top real estate
												developers
												across Mumbai.</p>
											<!-- <p>From first ideas to final touches, I’m with you every step—making the
												process enjoyable, creative, and stress-free.</p> -->
										</div>
										<div class="qodef-tabs-horizontal-content" id="qodef-tab-0927390">
											<p>My mission is to design spaces that feel truly yours—comfortable,
												beautiful, and practical. I believe great design is about listening,
												understanding, and bringing your vision to life with care and
												creativity.</p>
											<p>Every project is a collaboration, and I’m committed to making the
												experience positive, transparent, and inspiring for you.</p>
										</div>
										<div class="qodef-tabs-horizontal-content" id="qodef-tab-b0d90cc">
											<p>I envision a world where every home and small business can be a
												reflection of its owner’s spirit. My goal is to make custom, creative
												design accessible and enjoyable for everyone—one project at a time.</p>
											<p>Let’s create spaces that inspire, comfort, and support your everyday
												life.</p>
										</div>
									</div>
								</div>
							</div>
							<div class="elementor-element elementor-element-346fe75 animated-slow elementor-invisible elementor-widget elementor-widget-qi_addons_for_elementor_progress_bar_horizontal"
								data-id="346fe75" data-element_type="widget"
								data-settings="{&quot;_animation&quot;:&quot;fadeInUp&quot;,&quot;_animation_delay&quot;:&quot;200&quot;}"
								data-widget_type="qi_addons_for_elementor_progress_bar_horizontal.default">
								<div class="elementor-widget-container">
									<div class="qodef-shortcode qodef-m qodef-qi-progress-bar-horizontal qodef-percentage--fixed-above"
										data-active-line-color="#000000" data-active-line-width="1"
										data-inactive-line-color="#000000" data-inactive-line-width="4" data-number="75"
										data-percentage-type="fixed-above" data-gradient-fill="no"
										data-rand-id="375466223" data-gradient-start="#D9E7FA"
										data-gradient-end="#FCC4AF" data-text-color="#000000">
										<div class="qodef-m-inner">
											<div class="qodef-m-content">
												<h5 class="qodef-m-title">
													Renovation </h5>
												<div class="qodef-m-value">
													<div class="qodef-m-value-inner"></div>
												</div>
											</div>
											<div class="qodef-m-canvas"></div>
										</div>
									</div>
								</div>
							</div>
							<div class="elementor-element elementor-element-d102b9d animated-slow elementor-invisible elementor-widget elementor-widget-qi_addons_for_elementor_progress_bar_horizontal"
								data-id="d102b9d" data-element_type="widget"
								data-settings="{&quot;_animation&quot;:&quot;fadeInUp&quot;,&quot;_animation_delay&quot;:&quot;200&quot;}"
								data-widget_type="qi_addons_for_elementor_progress_bar_horizontal.default">
								<div class="elementor-widget-container">
									<div class="qodef-shortcode qodef-m qodef-qi-progress-bar-horizontal qodef-percentage--fixed-above"
										data-active-line-color="#000000" data-active-line-width="1"
										data-inactive-line-color="#000000" data-inactive-line-width="4" data-number="95"
										data-percentage-type="fixed-above" data-gradient-fill="no"
										data-rand-id="319669283" data-gradient-start="#D9E7FA"
										data-gradient-end="#FCC4AF" data-text-color="#000000">
										<div class="qodef-m-inner">
											<div class="qodef-m-content">
												<h5 class="qodef-m-title">
													Architecture </h5>
												<div class="qodef-m-value">
													<div class="qodef-m-value-inner"></div>
												</div>
											</div>
											<div class="qodef-m-canvas"></div>
										</div>
									</div>
								</div>
							</div>
							<div class="elementor-element elementor-element-def4d87 animated-slow elementor-invisible elementor-widget elementor-widget-qi_addons_for_elementor_progress_bar_horizontal"
								data-id="def4d87" data-element_type="widget"
								data-settings="{&quot;_animation&quot;:&quot;fadeInUp&quot;,&quot;_animation_delay&quot;:&quot;200&quot;}"
								data-widget_type="qi_addons_for_elementor_progress_bar_horizontal.default">
								<div class="elementor-widget-container">
									<div class="qodef-shortcode qodef-m qodef-qi-progress-bar-horizontal qodef-percentage--fixed-above"
										data-active-line-color="#000000" data-active-line-width="1"
										data-inactive-line-color="#000000" data-inactive-line-width="4" data-number="85"
										data-percentage-type="fixed-above" data-gradient-fill="no"
										data-rand-id="1323806681" data-gradient-start="#D9E7FA"
										data-gradient-end="#FCC4AF" data-text-color="#000000">
										<div class="qodef-m-inner">
											<div class="qodef-m-content">
												<h5 class="qodef-m-title">
													Functional Spaces </h5>
												<div class="qodef-m-value">
													<div class="qodef-m-value-inner"></div>
												</div>
											</div>
											<div class="qodef-m-canvas"></div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="elementor-element elementor-element-f387de2 e-con-full e-flex e-con e-parent" data-id="f387de2"
				data-element_type="container"
				data-settings="{&quot;background_background&quot;:&quot;video&quot;,&quot;background_video_link&quot;:&quot;https:\/\/themes.pixelwars.org\/interique\/demo-01\/wp-content\/uploads\/sites\/2\/2025\/05\/freepik__dolly-shot-a-stylish-living-room-featuring-a-cozy-__17637.mp4&quot;,&quot;background_play_on_mobile&quot;:&quot;yes&quot;}">
				<div class="elementor-background-video-container">
					<video class="elementor-background-video-hosted" autoplay muted playsinline loop></video>
				</div>
			</div>
			<div class="elementor-element elementor-element-2ca4a17d e-flex e-con-boxed e-con e-parent"
				data-id="2ca4a17d" data-element_type="container">
				<div class="e-con-inner">
					<div class="elementor-element elementor-element-420abc6e e-con-full e-flex e-con e-child"
						data-id="420abc6e" data-element_type="container">
						<div class="elementor-element elementor-element-11572ebf elementor-invisible elementor-widget elementor-widget-qi_addons_for_elementor_animated_text"
							data-id="11572ebf" data-element_type="widget"
							data-settings="{&quot;_animation&quot;:&quot;fadeIn&quot;}"
							data-widget_type="qi_addons_for_elementor_animated_text.default">
							<div class="elementor-widget-container">
								<div
									class="qodef-shortcode qodef-m qodef-qi-animated-text qodef--animated-by-letter qodef-qi--has-appear qodef--appear-fade">
									<h1 class="qodef-m-title">
										<span class="qodef-e-word-holder">Tailored</span> <span
											class="qodef-e-word-holder">Interior</span> <span
											class="qodef-e-word-holder">Solutions</span>
									</h1>
								</div>
							</div>
						</div>
					</div>
					<div class="elementor-element elementor-element-76669938 e-con-full e-flex e-con e-child"
						data-id="76669938" data-element_type="container">
						<div class="elementor-element elementor-element-7f845ffa elementor-widget elementor-widget-qi_addons_for_elementor_animated_text"
							data-id="7f845ffa" data-element_type="widget"
							data-widget_type="qi_addons_for_elementor_animated_text.default">
							<div class="elementor-widget-container">
								<div
									class="qodef-shortcode qodef-m qodef-qi-animated-text qodef--animated-by-letter qodef-qi--has-appear qodef--appear-fade">
									<h3 class="qodef-m-title">
										<span class="qodef-e-word-holder">Our</span> <span
											class="qodef-e-word-holder">Services</span>
									</h3>
								</div>
							</div>
						</div>
						<div class="elementor-element elementor-element-79db3a3f animated-slow elementor-invisible elementor-widget elementor-widget-heading"
							data-id="79db3a3f" data-element_type="widget"
							data-settings="{&quot;_animation&quot;:&quot;fadeIn&quot;}"
							data-widget_type="heading.default">
							<div class="elementor-widget-container">
								<p class="elementor-heading-title elementor-size-default">From luxurious residences to
									high-performance
									commercial spaces, our turnkey solutions are
									crafted with creativity, precision, and purpose. At
									Motiff Square, every design is a balance of beauty,
									function, and lifestyle.</p>
							</div>
						</div>
						<div class="elementor-element elementor-element-13b94473 elementor-widget elementor-widget-qi_addons_for_elementor_button"
							data-id="13b94473" data-element_type="widget"
							data-widget_type="qi_addons_for_elementor_button.default">
							<div class="elementor-widget-container">
								<a class="qodef-shortcode qodef-m qodef-qi-button qodef-html--link qodef-layout--textual qodef-icon--right qodef-hover--icon-move-horizontal-short"
									href="service.html" target="_self">
									<span class="qodef-m-text">All Services</span>
									<span class="qodef-m-icon">
										<span class="qodef-m-icon-inner">
											<svg xmlns="http://www.w3.org/2000/svg" height="21" viewBox="0 0 21 21"
												width="21">
												<g fill="none" fill-rule="evenodd" stroke="currentColor"
													stroke-linecap="round" stroke-linejoin="round"
													transform="translate(6 6)">
													<path d="m8.5 7.5v-7h-7"></path>
													<path d="m8.5.5-8 8"></path>
												</g>
											</svg> </span>
									</span>
								</a>
							</div>
						</div>
					</div>
					<div class="elementor-element elementor-element-085559a e-con-full e-flex e-con e-child"
						data-id="085559a" data-element_type="container">
						<div class="elementor-element elementor-element-30c6a06 e-flex e-con-boxed e-con e-child"
							data-id="30c6a06" data-element_type="container">
							<div class="e-con-inner">
								<div class="elementor-element elementor-element-7f101c8 e-flex e-con-boxed e-con e-child"
									data-id="7f101c8" data-element_type="container">
									<div class="e-con-inner">
										<div class="elementor-element elementor-element-9810eba elementor-view-default elementor-widget elementor-widget-icon"
											data-id="9810eba" data-element_type="widget"
											data-widget_type="icon.default">
											<div class="elementor-widget-container">
												<div class="elementor-icon-wrapper">
													<div class="elementor-icon">
														<svg xmlns="http://www.w3.org/2000/svg" id="Bahan"
															viewBox="0 0 64 64">
															<path
																d="m40.65 43.349v-7.386a2.457 2.457 0 0 0 -2.45-2.455h-3.52a1 1 0 0 0 0 2h3.52a.449.449 0 0 1 .454.455v7.386a3.179 3.179 0 0 0 -3.128 3.121h-23.016a3.179 3.179 0 0 0 -3.128-3.121v-7.386a.45.45 0 0 1 .454-.455h21.831a1 1 0 0 0 0-2h-21.831a2.457 2.457 0 0 0 -2.454 2.455v7.386a3.186 3.186 0 0 0 -3.133 3.176v10.69a1 1 0 0 0 1 1h9.88a1 1 0 0 0 0-2h-2.613v-7.745h23v7.745h-17.373a1 1 0 0 0 0 2h24.633a1 1 0 0 0 1-1v-10.69a3.182 3.182 0 0 0 -3.126-3.176zm-34.401 12.866v-9.69a1.2 1.2 0 0 1 1.188-1.181h1.9a1.183 1.183 0 0 1 1.182 1.181v9.69zm31.268-9.69a1.183 1.183 0 0 1 1.183-1.181h1.9a1.183 1.183 0 0 1 1.181 1.181v9.69h-4.264z">
															</path>
															<path
																d="m5.578 26.831h25.784a1 1 0 0 0 1-1v-19.046a1 1 0 0 0 -1-1h-25.784a1 1 0 0 0 -1 1v19.046a1 1 0 0 0 1 1zm1-2v-4.212l6.182-4.982 6.6 7.848a1 1 0 0 0 .766.357 1.007 1.007 0 0 0 .774-1.642l-2.308-2.742 1.825-1.566c.984 1.214 3.5 4.348 4.51 5.608a1 1 0 1 0 1.56-1.25l-2.473-3.08 4.138-3.719 2.214 2.581v6.8zm23.784-17.046v7.173l-1.362-1.588a1.007 1.007 0 0 0 -1.428-.092l-4.817 4.329c-.795-.734-1.539-2.841-2.859-1.908l-2.6 2.227-3.637-4.326a1 1 0 0 0 -1.392-.134l-5.689 4.584v-10.265z">
															</path>
															<path
																d="m19 14.21a2.948 2.948 0 0 0 0-5.9 2.948 2.948 0 0 0 0 5.9zm0-3.9a.948.948 0 0 1 0 1.9.948.948 0 0 1 0-1.896z">
															</path>
															<path
																d="m47.94 37.83a1.008 1.008 0 0 0 .38-1.93 1.008 1.008 0 1 0 -.38 1.93z">
															</path>
															<path
																d="m59.714 26.76a.985.985 0 0 0 -.041-.575l-4.3-12.268a1.072 1.072 0 0 0 -.945-.666h-5.575a.961.961 0 0 0 -.662.263 1.334 1.334 0 0 0 -.271.376l-4.73 12.323a1.018 1.018 0 0 0 .934 1.361h2.815v6.7a1 1 0 0 0 2 0v-6.7h1.5v24.7h-1.847a2.937 2.937 0 0 0 0 5.873h5.691a2.937 2.937 0 0 0 0-5.873h-1.847v-24.7h6.315a1 1 0 0 0 .963-.814zm-4.523 28.476a.909.909 0 0 1 -.908.908h-5.691a.938.938 0 0 1 0-1.873h2.856 2.835a.923.923 0 0 1 .908.965zm-9.613-29.662 3.962-10.324h4.177l3.622 10.324z">
															</path>
														</svg>
													</div>
												</div>
											</div>
										</div>
										<div class="elementor-element elementor-element-1c49f1f elementor-widget elementor-widget-heading"
											data-id="1c49f1f" data-element_type="widget"
											data-widget_type="heading.default">
											<div class="elementor-widget-container">
												<h1 class="elementor-heading-title elementor-size-default"><a
														href="service.html">Interior Design & Turnkey Execution</a></h1>
											</div>
										</div>
										<div class="elementor-element elementor-element-16cebf2 animated-slow elementor-invisible elementor-widget elementor-widget-heading"
											data-id="16cebf2" data-element_type="widget"
											data-settings="{&quot;_animation&quot;:&quot;fadeIn&quot;}"
											data-widget_type="heading.default">
											<div class="elementor-widget-container">
												<p class="elementor-heading-title elementor-size-default">Complete
													design-to-delivery solutions that
													transform raw spaces into fully realized
													environments, ready for living or working.</p>
											</div>
										</div>
									</div>
								</div>
								<div class="elementor-element elementor-element-94802b3 e-flex e-con-boxed e-con e-child"
									data-id="94802b3" data-element_type="container">
									<div class="e-con-inner">
										<div class="elementor-element elementor-element-2e5fd15 elementor-view-default elementor-widget elementor-widget-icon"
											data-id="2e5fd15" data-element_type="widget"
											data-widget_type="icon.default">
											<div class="elementor-widget-container">
												<div class="elementor-icon-wrapper">
													<div class="elementor-icon">
														<svg xmlns="http://www.w3.org/2000/svg" id="Layer_1"
															height="512" viewBox="0 0 100.25 100.25" width="512">
															<g>
																<path
																	d="m45 8.75c0-.829-.671-1.5-1.5-1.5h-30c-.829 0-1.5.671-1.5 1.5v63.188c0 8.994 7.317 16.312 16.311 16.312h.354c9.007 0 16.335-7.327 16.335-16.334zm-30 19.5h27v28h-27zm27-18v15h-27v-15zm-13.334 75h-.354c-7.341 0-13.312-5.972-13.312-13.312v-12.688h27v12.666c0 7.353-5.982 13.334-13.334 13.334z">
																</path>
																<path
																	d="m50.364 69.624c.292.288.672.432 1.053.432.388 0 .774-.149 1.068-.446l30.253-30.672c.575-.584.575-1.522 0-2.106l-20.444-20.729c-.564-.572-1.572-.571-2.137 0l-9.808 9.945c-.582.59-.575 1.54.015 2.121.588.581 1.539.575 2.122-.015l8.74-8.862 18.338 18.591-29.215 29.619c-.582.59-.575 1.54.015 2.122z">
																</path>
																<path
																	d="m90.5 57.25h-21c-.828 0-1.5.672-1.5 1.5s.672 1.5 1.5 1.5h19.5v27h-44.5c-.829 0-1.5.672-1.5 1.5s.671 1.5 1.5 1.5h46c.828 0 1.5-.672 1.5-1.5v-30c0-.828-.672-1.5-1.5-1.5z">
																</path>
																<path
																	d="m28.5 67.25c-3.033 0-5.5 2.468-5.5 5.5s2.467 5.5 5.5 5.5 5.5-2.468 5.5-5.5-2.467-5.5-5.5-5.5zm0 8c-1.378 0-2.5-1.121-2.5-2.5s1.122-2.5 2.5-2.5 2.5 1.121 2.5 2.5-1.122 2.5-2.5 2.5z">
																</path>
															</g>
														</svg>
													</div>
												</div>
											</div>
										</div>
										<div class="elementor-element elementor-element-570879d elementor-widget elementor-widget-heading"
											data-id="570879d" data-element_type="widget"
											data-widget_type="heading.default">
											<div class="elementor-widget-container">
												<h1 class="elementor-heading-title elementor-size-default"><a
														href="service.html">Architectural Consultation</a>
													<br><br>
												</h1>
											</div>
										</div>
										<div class="elementor-element elementor-element-06de4ef animated-slow elementor-invisible elementor-widget elementor-widget-heading"
											data-id="06de4ef" data-element_type="widget"
											data-settings="{&quot;_animation&quot;:&quot;fadeIn&quot;}"
											data-widget_type="heading.default">
											<div class="elementor-widget-container">
												<p class="elementor-heading-title elementor-size-default">Expert
													structural planning and layout ideation to
													ensure every inch of your space is optimized
													for form and function.

												</p>
											</div>
										</div>
									</div>
								</div>
								<div class="elementor-element elementor-element-9502218 e-flex e-con-boxed e-con e-child"
									data-id="9502218" data-element_type="container">
									<div class="e-con-inner">
										<div class="elementor-element elementor-element-2a2c697 elementor-view-default elementor-widget elementor-widget-icon"
											data-id="2a2c697" data-element_type="widget"
											data-widget_type="icon.default">
											<div class="elementor-widget-container">
												<div class="elementor-icon-wrapper">
													<div class="elementor-icon">
														<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
															<g id="_x30_6_Living_Room">
																<g>
																	<path
																		d="m470.626 327.072v-28.063c0-13.731-11.17-24.9-24.9-24.9h-129.558c-5 0-9.528 2.013-12.86 5.248-3.321-3.235-7.833-5.248-12.817-5.248h-129.557c-13.731 0-24.9 11.17-24.9 24.9v28.067c-13.202 1.303-23.585 12.338-23.585 25.873v91.003c0 3.107 2.52 5.627 5.627 5.627h22.089l4.922 23.992c1.912 9.305 10.192 16.057 19.687 16.057s17.775-6.752 19.687-16.057l4.922-23.992h227.877l4.922 23.992c1.912 9.305 10.192 16.057 19.687 16.057 9.496 0 17.775-6.752 19.687-16.057l4.922-23.992h22.107c3.107 0 5.627-2.52 5.627-5.627v-91.003c-.001-13.188-9.717-24.499-23.586-25.877zm-154.458-41.71h129.557c7.525 0 13.646 6.122 13.646 13.646v29.3c-9.854 3.424-17.486 13.118-17.486 24.64v39.257h-132.959v-99.601c0-3.992 3.249-7.242 7.242-7.242zm-151.391 152.963c0-1.413 0-35.635 0-34.865h277.109c0 .798 0 37.035 0 34.865zm-17.489-139.316c0-7.525 6.122-13.646 13.646-13.646h129.557c3.96 0 7.18 3.249 7.18 7.243v99.601h-132.894v-39.257c0-11.72-7.636-21.141-17.489-24.655zm-23.586 53.94c0-8.202 6.675-14.877 14.881-14.877 7.388 0 14.939 5.913 14.939 14.877 0 3.136 0 86.554 0 85.376h-29.82zm49.732 118.358c-.839 4.096-4.48 7.067-8.66 7.067s-7.821-2.971-8.66-7.067l-4.455-21.706h26.23zm277.094 0c-.839 4.096-4.48 7.067-8.66 7.067s-7.821-2.971-8.66-7.067l-4.455-21.706h26.23zm32.429-32.982h-29.817c0-.79 0-41.982 0-40.492v-44.884c0-6.957 4.737-12.906 11.624-14.493 8.947-2.242 18.193 4.756 18.193 14.493z">
																	</path>
																	<path
																		d="m23.416 442.762c-3.107 0-5.627 2.52-5.627 5.627s2.52 5.627 5.627 5.627h57.157c3.107 0 5.627-2.52 5.627-5.627s-2.52-5.627-5.627-5.627h-22.915v-209.982c16.158-2.703 28.542-16.686 28.542-33.6 0-16.95-12.384-30.962-28.542-33.672v-84.997c0-25.853 21.032-46.885 46.885-46.885h14.236c25.023 0 45.469 19.719 46.76 44.426h-40.235c-3.107 0-5.627 2.52-5.627 5.627v2.912c0 26.336 21.424 47.76 47.76 47.76h5.774c26.336 0 47.76-21.424 47.76-47.76v-2.912c0-3.107-2.52-5.627-5.627-5.627h-38.55c-1.302-30.914-26.785-55.681-58.014-55.681h-14.236c-32.059 0-58.139 26.08-58.139 58.139v84.997c-16.198 2.705-28.615 16.719-28.615 33.673 0 16.918 12.417 30.902 28.615 33.601v209.981zm149.794-319.664h-5.774c-19.218 0-35.011-14.925-36.407-33.792h78.588c-1.395 18.867-17.189 33.792-36.407 33.792zm-144.167 76.082c0-12.675 10.313-22.988 22.988-22.988 12.635 0 22.915 10.313 22.915 22.988 0 12.635-10.28 22.915-22.915 22.915-12.675 0-22.988-10.28-22.988-22.915z">
																	</path>
																</g>
															</g>
															<g id="Layer_1"></g>
														</svg>
													</div>
												</div>
											</div>
										</div>
										<div class="elementor-element elementor-element-a62f323 elementor-widget elementor-widget-heading"
											data-id="a62f323" data-element_type="widget"
											data-widget_type="heading.default">
											<div class="elementor-widget-container">
												<h1 class="elementor-heading-title elementor-size-default"><a
														href="service.html">Bespoke Furniture</a>

													<br><br>
												</h1>
											</div>
										</div>
										<div class="elementor-element elementor-element-6ac40bc animated-slow elementor-invisible elementor-widget elementor-widget-heading"
											data-id="6ac40bc" data-element_type="widget"
											data-settings="{&quot;_animation&quot;:&quot;fadeIn&quot;}"
											data-widget_type="heading.default">
											<div class="elementor-widget-container">
												<p class="elementor-heading-title elementor-size-default">Tailor-made
													furniture that fits your aesthetic
													and space — designed for elegance,
													comfort, and durability.</p>

											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="elementor-element elementor-element-52fe893 e-flex e-con-boxed e-con e-child"
							data-id="52fe893" data-element_type="container">
							<div class="e-con-inner">
								<div class="elementor-element elementor-element-d91131c e-flex e-con-boxed e-con e-child"
									data-id="d91131c" data-element_type="container">
									<div class="e-con-inner">
										<div class="elementor-element elementor-element-3bb02d6 elementor-view-default elementor-widget elementor-widget-icon"
											data-id="3bb02d6" data-element_type="widget"
											data-widget_type="icon.default">
											<div class="elementor-widget-container">
												<div class="elementor-icon-wrapper">
													<div class="elementor-icon">
														<svg xmlns="http://www.w3.org/2000/svg" id="Layer_1"
															height="512" viewBox="0 0 512 512" width="512"
															data-name="Layer 1">
															<g id="Layer_1-2" data-name="Layer_1">
																<g id="_256581984" data-name=" 256581984">
																	<g id="_515979528" data-name=" 515979528">
																		<path
																			d="m468.26 470.37h-44a18.76 18.76 0 0 1 -18.73-18.74v-115.19a18.75 18.75 0 0 1 18.73-18.73h44a18.76 18.76 0 0 1 18.74 18.73v115.19a18.77 18.77 0 0 1 -18.74 18.74zm-44-140.66a6.74 6.74 0 0 0 -6.73 6.73v115.19a6.74 6.74 0 0 0 6.73 6.74h44a6.74 6.74 0 0 0 6.74-6.74v-115.19a6.74 6.74 0 0 0 -6.74-6.73z">
																		</path>
																	</g>
																	<g id="_515979696" data-name=" 515979696">
																		<path
																			d="m237.12 470.37h-44a18.76 18.76 0 0 1 -18.74-18.74v-115.19a18.75 18.75 0 0 1 18.74-18.73h44a18.75 18.75 0 0 1 18.74 18.73v115.19a18.76 18.76 0 0 1 -18.74 18.74zm-44-140.66a6.74 6.74 0 0 0 -6.74 6.73v115.19a6.74 6.74 0 0 0 6.74 6.74h44a6.74 6.74 0 0 0 6.74-6.74v-115.19a6.74 6.74 0 0 0 -6.74-6.73z">
																		</path>
																	</g>
																	<g id="_515979504" data-name=" 515979504">
																		<path
																			d="m411.52 459.19h-161.66a6 6 0 0 1 -6-6v-56.66a28.44 28.44 0 0 1 28.41-28.41h116.84a28.44 28.44 0 0 1 28.41 28.41v56.66a6 6 0 0 1 -6 6zm-155.66-12h149.66v-50.66a16.43 16.43 0 0 0 -16.41-16.41h-116.84a16.43 16.43 0 0 0 -16.41 16.41z">
																		</path>
																	</g>
																	<g id="_515979648" data-name=" 515979648">
																		<path
																			d="m446.26 329.71a6 6 0 0 1 -6-6v-75.32c0-10.76-10.35-19.51-23.07-19.51h-173c-12.72 0-23.07 8.75-23.07 19.51v75.32a6 6 0 0 1 -12 0v-75.32c0-17.38 15.73-31.51 35.07-31.51h173c19.34 0 35.07 14.13 35.07 31.51v75.32a6 6 0 0 1 -6 6z">
																		</path>
																	</g>
																	<g id="_515979552" data-name=" 515979552">
																		<path
																			d="m330.69 345.67a6 6 0 0 1 -4.47-2l-36.87-41.17a6 6 0 0 1 0-8l36.87-41.17a6 6 0 0 1 8.94 0l36.84 41.17a6 6 0 0 1 0 8l-36.87 41.17a6 6 0 0 1 -4.44 2zm-28.82-47.17 28.82 32.17 28.81-32.17-28.81-32.18z">
																		</path>
																	</g>
																	<g id="_515979624" data-name=" 515979624">
																		<path
																			d="m274.32 270.27h-28.32a6 6 0 0 1 0-12h28.36a6 6 0 0 1 0 12z">
																		</path>
																	</g>
																	<g id="_515979432" data-name=" 515979432">
																		<path
																			d="m415.42 270.27h-28.36a6 6 0 1 1 0-12h28.36a6 6 0 1 1 0 12z">
																		</path>
																	</g>
																	<g id="_515978808" data-name=" 515978808">
																		<path
																			d="m359.4 411.55h-57.4a6 6 0 0 1 0-12h57.4a6 6 0 0 1 0 12z">
																		</path>
																	</g>
																	<g id="_515978280" data-name=" 515978280">
																		<path
																			d="m148 470.37h-74.63a6 6 0 0 1 -6-6v-36.09a6 6 0 0 1 6-6h74.63a6 6 0 0 1 6 6v36.09a6 6 0 0 1 -6 6zm-68.6-12h62.6v-24.09h-62.63z">
																		</path>
																	</g>
																	<g id="_515978376" data-name=" 515978376">
																		<path
																			d="m129.32 434.28h-37.32a6 6 0 0 1 -6-6v-259.28a6 6 0 0 1 6-6h37.3a6 6 0 0 1 6 6v259.28a6 6 0 0 1 -5.98 6zm-31.3-12h25.3v-247.28h-25.32z">
																		</path>
																	</g>
																	<g id="_515978760" data-name=" 515978760">
																		<path
																			d="m190.34 175h-159.34a6 6 0 0 1 -5.64-8l44.21-121.42a6 6 0 0 1 5.64-3.95h70.92a6 6 0 0 1 5.64 3.95l44.23 121.42a6 6 0 0 1 -5.63 8.05zm-150.77-12h142.2l-39.84-109.37h-62.52z">
																		</path>
																	</g>
																	<g id="_515978736" data-name=" 515978736">
																		<path
																			d="m139.3 175h-57.3a6 6 0 0 1 -6-6.78l16-121.37a6 6 0 0 1 6-5.22h25.48a6 6 0 0 1 6 5.22l15.89 121.4a6 6 0 0 1 -6.07 6.75zm-50.43-12h43.59l-14.31-109.37h-15z">
																		</path>
																	</g>
																	<g id="_515978328" data-name=" 515978328">
																		<path
																			d="m481 175h-225.31a6 6 0 0 1 -6-6v-121.37a6 6 0 0 1 6-6h225.31a6 6 0 0 1 6 6v121.37a6 6 0 0 1 -6 6zm-219.31-12h213.31v-109.37h-213.31z">
																		</path>
																	</g>
																	<g id="_256583472" data-name=" 256583472">
																		<path
																			d="m392 175a6 6 0 0 1 -5.58-3.8l-24.95-63.2-32.83 27.62a6 6 0 0 1 -8.72-1.07l-23.48-32.35-36.15 43.3a6 6 0 0 1 -9.21-7.7l41.08-49.17a6 6 0 0 1 9.46.33l24.23 33.37 34.28-28.84a6 6 0 0 1 9.44 2.39l28 71a6 6 0 0 1 -3.38 7.78 5.88 5.88 0 0 1 -2.19.34z">
																		</path>
																	</g>
																	<g id="_256582464" data-name=" 256582464">
																		<path
																			d="m377.29 137.77a6 6 0 0 1 -3.77-10.68c29.21-23.51 48.57-14.78 64.13-7.77 13.55 6.1 23.34 10.52 39.24-4.44a6 6 0 1 1 8.22 8.73c-21.74 20.46-38 13.12-52.4 6.65s-28.19-12.71-51.66 6.18a6 6 0 0 1 -3.76 1.33z">
																		</path>
																	</g>
																	<g id="_256582272" data-name=" 256582272">
																		<path
																			d="m441.13 107.89a21.76 21.76 0 1 1 21.75-21.75 21.77 21.77 0 0 1 -21.75 21.75zm0-31.51a9.76 9.76 0 1 0 9.75 9.76 9.77 9.77 0 0 0 -9.75-9.76z">
																		</path>
																	</g>
																</g>
															</g>
														</svg>
													</div>
												</div>
											</div>
										</div>
										<div class="elementor-element elementor-element-ee0f281 elementor-widget elementor-widget-heading"
											data-id="ee0f281" data-element_type="widget"
											data-widget_type="heading.default">
											<div class="elementor-widget-container">
												<h1 class="elementor-heading-title elementor-size-default"><a
														href="service.html">Interior Styling</a></h1>
											</div>
										</div>
										<div class="elementor-element elementor-element-fd6f785 animated-slow elementor-invisible elementor-widget elementor-widget-heading"
											data-id="fd6f785" data-element_type="widget"
											data-settings="{&quot;_animation&quot;:&quot;fadeIn&quot;}"
											data-widget_type="heading.default">
											<div class="elementor-widget-container">
												<p class="elementor-heading-title elementor-size-default">Layering
													spaces with art, accessories,
													textures, and finishes to bring warmth,
													personality, and harmony.

												</p>
											</div>
										</div>
									</div>
								</div>
								<div class="elementor-element elementor-element-9000550 e-flex e-con-boxed e-con e-child"
									data-id="9000550" data-element_type="container">
									<div class="e-con-inner">
										<div class="elementor-element elementor-element-c453bb5 elementor-view-default elementor-widget elementor-widget-icon"
											data-id="c453bb5" data-element_type="widget"
											data-widget_type="icon.default">
											<div class="elementor-widget-container">
												<div class="elementor-icon-wrapper">
													<div class="elementor-icon">
														<svg xmlns="http://www.w3.org/2000/svg" id="Layer_25"
															viewBox="0 0 64 64" data-name="Layer 25">
															<path
																d="m61.498 13.9935c-.0013-.0903-.0317-.1737-.0779-.2496-.0106-.0174-.0209-.0315-.0335-.0475-.0543-.0695-.1205-.1304-.2089-.1642l-29-11c-.0714-.0259-.1442-.0316-.2155-.0258-.0267.0021-.0504.0117-.0765.018-.0467.0113-.0902.0262-.1317.0499-.0121.007-.0267.0065-.0383.0145-.0121.0084-.0173.0229-.0286.0323-.0377.0306-.0682.067-.0961.1085-.014.0212-.0291.0395-.0398.0626-.0052.011-.0146.0185-.0191.03-.0175.0462-.0195.0933-.0229.1401-.001.0131-.0093.0244-.0093.0377v31.6766l-28.704 12.8673c-.1807.0801-.2959.2588-.2959.4561s.1152.376.2959.4561l29 13c.0645.0293.1348.0439.2041.0439s.1396-.0146.2041-.0439l29-13c.1807-.0801.2959-.2588.2959-.4561v-34c0-.0024-.002-.0041-.002-.0065zm-7.998 30.0963-12.4703-5.5898h10.9703c.2764 0 .5-.2236.5-.5s-.2236-.5-.5-.5h-12.5v-24.1162l14 5.3018zm-9.0292-2.9517-5.6245 2.5214-12.4709-5.5903 5.6246-2.5214zm2.8131 12.4627c-.0267-.019-.0487-.043-.0798-.0569l-7.1361-3.199 5.6241-2.5215 7.2401 3.2455zm-6.8224 3.0583-14.0861-6.3144 5.6245-2.5215 14.0861 6.3146zm-29.3933-5.5904 7.2397-3.2454 12.4713 5.5905-7.2405 3.2452zm7.2403-9.3832 12.47 5.5901-5.6245 2.5215-12.4703-5.5901zm6.8454-3.0687 5.6241 2.5211-5.6237 2.5214-5.6245-2.5214 5.624-2.5212zm13.6927 11.1806-12.4709-5.5906 5.6237-2.5214 12.4713 5.5905zm-27.3844-5.0428 5.6243 2.5212-7.2397 3.2454-5.624-2.5212 7.2395-3.2454zm20.5378 15.6975-7.2398-3.2455 7.2405-3.2452 7.2393 3.2452-7.2401 3.2455zm22.1537-9.9307-14.0859-6.3143 5.6245-2.5215 14.0861 6.3144-5.6246 2.5214zm.3463-5.9834v-26.1982c0-.208-.1289-.3936-.3232-.4678l-15-5.6797c-.1523-.0576-.3252-.0361-.4609.0566-.1348.0928-.2158.2471-.2158.4111v24.7059l-6-2.6895v-30.9515l5.9341 2.2507 22.0659 8.37v32.882l-6-2.6895z">
															</path>
															<path
																d="m3 26.4834c.2764 0 .5-.2236.5-.5v-1c0-.2764-.2236-.5-.5-.5s-.5.2236-.5.5v1c0 .2764.2236.5.5.5z">
															</path>
															<path
																d="m3 32.4834c.2764 0 .5-.2236.5-.5v-1c0-.2764-.2236-.5-.5-.5s-.5.2236-.5.5v1c0 .2764.2236.5.5.5z">
															</path>
															<path
																d="m3 17.4834c.2764 0 .5-.2236.5-.5v-1c0-.2764-.2236-.5-.5-.5s-.5.2236-.5.5v1c0 .2764.2236.5.5.5z">
															</path>
															<path
																d="m3 20.4834c.2764 0 .5-.2236.5-.5v-1c0-.2764-.2236-.5-.5-.5s-.5.2236-.5.5v1c0 .2764.2236.5.5.5z">
															</path>
															<path
																d="m3 23.4834c.2764 0 .5-.2236.5-.5v-1c0-.2764-.2236-.5-.5-.5s-.5.2236-.5.5v1c0 .2764.2236.5.5.5z">
															</path>
															<path
																d="m3 29.4834c.2764 0 .5-.2236.5-.5v-1c0-.2764-.2236-.5-.5-.5s-.5.2236-.5.5v1c0 .2764.2236.5.5.5z">
															</path>
															<path
																d="m3 44.4834c.2764 0 .5-.2236.5-.5v-1c0-.2764-.2236-.5-.5-.5s-.5.2236-.5.5v1c0 .2764.2236.5.5.5z">
															</path>
															<path
																d="m3 35.4834c.2764 0 .5-.2236.5-.5v-1c0-.2764-.2236-.5-.5-.5s-.5.2236-.5.5v1c0 .2764.2236.5.5.5z">
															</path>
															<path
																d="m3 38.4834c.2764 0 .5-.2236.5-.5v-1c0-.2764-.2236-.5-.5-.5s-.5.2236-.5.5v1c0 .2764.2236.5.5.5z">
															</path>
															<path
																d="m3 41.4834c.2764 0 .5-.2236.5-.5v-1c0-.2764-.2236-.5-.5-.5s-.5.2236-.5.5v1c0 .2764.2236.5.5.5z">
															</path>
															<path
																d="m8.625 12.3662c.0586 0 .1191-.0107.1768-.0322l.9355-.3545c.2588-.0977.3887-.3867.291-.6445-.0986-.2588-.3877-.3857-.6445-.291l-.9355.3545c-.2588.0977-.3887.3867-.291.6445.0762.2002.2656.3232.4678.3232z">
															</path>
															<path
																d="m22.6504 7.0469c.0596 0 .1191-.0107.1777-.0322l.9346-.3555c.2578-.0986.3877-.3877.29-.6455-.0986-.2588-.3887-.3838-.6455-.29l-.9346.3555c-.2578.0986-.3877.3877-.29.6455.0762.1992.2666.3223.4678.3223z">
															</path>
															<path
																d="m25.4551 5.9824c.0586 0 .1191-.0107.1777-.0322l.9346-.3545c.2578-.0986.3877-.3867.29-.6455-.0986-.2578-.3867-.3857-.6455-.29l-.9346.3545c-.2578.0986-.3877.3867-.29.6455.0762.1992.2666.3223.4678.3223z">
															</path>
															<path
																d="m19.8447 8.1104c.0586 0 .1191-.0107.1768-.0322l.9355-.3545c.2588-.0977.3887-.3867.291-.6445-.0986-.2588-.3887-.3867-.6445-.291l-.9355.3545c-.2588.0977-.3887.3867-.291.6445.0762.2002.2656.3232.4678.3232z">
															</path>
															<path
																d="m28.2598 4.9189c.0596 0 .1191-.0107.1777-.0322l.9355-.3555c.2578-.0986.3877-.3877.29-.6455-.0986-.2578-.3887-.3848-.6455-.29l-.9355.3555c-.2578.0986-.3877.3877-.29.6455.0762.1992.2666.3223.4678.3223z">
															</path>
															<path
																d="m14.2354 10.2383c.0586 0 .1191-.0107.1777-.0322l.9346-.3545c.2578-.0986.3877-.3867.29-.6455-.0986-.2578-.3867-.3857-.6455-.29l-.9346.3545c-.2578.0986-.3877.3867-.29.6455.0762.1992.2666.3223.4678.3223z">
															</path>
															<path
																d="m11.4297 11.3027c.0596 0 .1191-.0107.1777-.0322l.9355-.3555c.2578-.0986.3877-.3877.29-.6455-.0986-.2578-.3896-.3848-.6455-.29l-.9355.3555c-.2578.0986-.3877.3877-.29.6455.0762.1992.2666.3223.4678.3223z">
															</path>
															<path
																d="m17.04 9.1748c.0596 0 .1191-.0107.1777-.0322l.9346-.3555c.2578-.0986.3877-.3877.29-.6455-.0986-.2578-.3887-.3838-.6455-.29l-.9346.3555c-.2578.0986-.3877.3877-.29.6455.0762.1992.2666.3223.4678.3223z">
															</path>
															<path
																d="m5.8203 13.4307c.0596 0 .1191-.0107.1777-.0322l.9346-.3555c.2578-.0986.3877-.3877.29-.6455-.0986-.2588-.3896-.3838-.6455-.29l-.9346.3555c-.2578.0986-.3877.3877-.29.6455.0762.1992.2666.3223.4678.3223z">
															</path>
															<path
																d="m3.0146 14.4941c.0586 0 .1191-.0107.1768-.0322l.9355-.3545c.2588-.0977.3887-.3867.291-.6445-.0986-.2588-.3877-.3867-.6445-.291l-.9355.3545c-.2588.0977-.3887.3867-.291.6445.0762.2002.2656.3232.4678.3232z">
															</path>
															<path
																d="m8.5 17.7197v21.5898c0 .1699.0859.3271.2285.4199.082.0527.1768.0801.2715.0801.0693 0 .1396-.0146.2051-.0439l7-3.1396c.1797-.0801.2949-.2588.2949-.4561v-21.0996c0-.1641-.0811-.3184-.2158-.4111s-.3096-.1143-.4609-.0566l-7 2.6494c-.1943.0732-.3232.2598-.3232.4678zm1 .3457 6-2.2715v20.0527l-6 2.6904v-20.4717z">
															</path>
															<path
																d="m19 35.3301c.0693 0 .1396-.0146.2051-.0439l7-3.1396c.1797-.0801.2949-.2588.2949-.4561v-20.4102c0-.1641-.0811-.3184-.2158-.4111-.1348-.0938-.3086-.1152-.4609-.0566l-7 2.6494c-.1943.0732-.3232.2598-.3232.4678v20.9004c0 .1699.0859.3271.2285.4199.082.0527.1768.0801.2715.0801zm.5-21.0547 6-2.2715v19.3633l-6 2.6904z">
															</path>
															<path
																d="m10.7451 21.5c.168 0 .333-.085.4268-.2402l1.5312-2.5166c.1436-.2354.0693-.543-.167-.6865-.2344-.1436-.543-.0713-.6865.167l-1.5312 2.5166c-.1436.2354-.0693.543.167.6865.0811.0498.1709.0732.2598.0732z">
															</path>
															<path
																d="m14.3633 18.7412c-.2383-.1436-.5449-.0654-.6855.1719l-3.1768 5.3135c-.1426.2373-.0645.5439.1719.6855.0811.0488.1689.0713.2568.0713.1699 0 .3359-.0869.4287-.2432l3.1768-5.3135c.1426-.2373.0645-.5439-.1719-.6855z">
															</path>
															<path
																d="m20.8193 17.7646c.168 0 .333-.085.4268-.2402l1.5312-2.5166c.1436-.2354.0693-.543-.167-.6865-.2344-.1445-.5439-.0703-.6865.167l-1.5312 2.5166c-.1436.2354-.0693.543.167.6865.0811.0498.1709.0732.2598.0732z">
															</path>
															<path
																d="m24.4375 15.0059c-.2393-.1436-.5449-.0654-.6855.1719l-3.1768 5.3135c-.1426.2373-.0645.5439.1719.6855.0811.0488.1689.0713.2568.0713.1699 0 .3359-.0869.4287-.2432l3.1768-5.3135c.1426-.2373.0645-.5439-.1719-.6855z">
															</path>
														</svg>
													</div>
												</div>
											</div>
										</div>
										<div class="elementor-element elementor-element-654e0c6 elementor-widget elementor-widget-heading"
											data-id="654e0c6" data-element_type="widget"
											data-widget_type="heading.default">
											<div class="elementor-widget-container">
												<h1 class="elementor-heading-title elementor-size-default"><a
														href="service.html">Landscape Design</a></h1>
											</div>
										</div>
										<div class="elementor-element elementor-element-14ef676 animated-slow elementor-invisible elementor-widget elementor-widget-heading"
											data-id="14ef676" data-element_type="widget"
											data-settings="{&quot;_animation&quot;:&quot;fadeIn&quot;}"
											data-widget_type="heading.default">
											<div class="elementor-widget-container">
												<p class="elementor-heading-title elementor-size-default">From cozy
													balconies to grand lawns, we
													design outdoor spaces that blend nature,
													relaxation, and style.
												</p>
											</div>
										</div>
									</div>
								</div>
								<div class="elementor-element elementor-element-119440c e-flex e-con-boxed e-con e-child"
									data-id="119440c" data-element_type="container">
									<div class="e-con-inner">
										<div class="elementor-element elementor-element-2746ba4 elementor-view-default elementor-widget elementor-widget-icon"
											data-id="2746ba4" data-element_type="widget"
											data-widget_type="icon.default">
											<div class="elementor-widget-container">
												<div class="elementor-icon-wrapper">
													<div class="elementor-icon">
														<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 96 96"
															width="512" height="512">
															<g>
																<circle cx="75" cy="43" r="1"></circle>
																<circle cx="37" cy="43" r="1"></circle>
																<circle cx="47" cy="41" r="1"></circle>
																<circle cx="56" cy="41" r="1"></circle>
																<circle cx="65" cy="41" r="1"></circle>
																<circle cx="51.5" cy="35" r="1"></circle>
																<circle cx="60.5" cy="35" r="1"></circle>
																<circle cx="63" cy="11" r="1"></circle>
																<path
																	d="M31,43V57a1,1,0,0,0,1,1H42a1,1,0,0,0,1-1H69a1,1,0,0,0,1,1H80a1,1,0,0,0,1-1V43a6.0046,6.0046,0,0,0-5-5.91A9.0122,9.0122,0,0,0,67,28H45a9.0124,9.0124,0,0,0-9,9.09A6.0046,6.0046,0,0,0,31,43ZM43,55V48H69v7ZM79,43V56H71V43A4,4,0,0,1,79,43ZM45,30H67a7.0109,7.0109,0,0,1,7,7.09A6.0046,6.0046,0,0,0,69,43v3H43V43a6.0046,6.0046,0,0,0-5-5.91A7.0107,7.0107,0,0,1,45,30ZM33,43a4,4,0,0,1,8,0c0,2.6059,0,10.2863,0,13H33Z">
																</path>
																<path
																	d="M88,37H84a1,1,0,0,0,0,2h4a2.0027,2.0027,0,0,1,2,2V51a5.1638,5.1638,0,0,0-6,5V66a5.1642,5.1642,0,0,0,6,5V81a2.0027,2.0027,0,0,1-2,2H15V39h4a1,1,0,0,0,0-2H8a4.0042,4.0042,0,0,0-4,4V86a4.0042,4.0042,0,0,0,4,4H88a4.0039,4.0039,0,0,0,4-4V41A4.0039,4.0039,0,0,0,88,37Zm1,32a3.0033,3.0033,0,0,1-3-3V56a3.0033,3.0033,0,0,1,3-3h1V69ZM6,41a2.0023,2.0023,0,0,1,2-2h5V83H8a2.0023,2.0023,0,0,1-2-2ZM88,88H8a2.0023,2.0023,0,0,1-2-2V84.4437A3.9551,3.9551,0,0,0,8,85H88a3.9545,3.9545,0,0,0,2-.5562V86A2.0027,2.0027,0,0,1,88,88Z">
																</path>
																<path
																	d="M15.5,33H22V49.0577c-7.2869.894-6.207,7.9386,1,7.9423,7.2072-.004,8.2865-7.0487,1-7.9423V33h6.5a1,1,0,0,0,.9487-1.3164l-4-12A1,1,0,0,0,26.5,19h-7a1,1,0,0,0-.9487.6836l-4,12A1.0071,1.0071,0,0,0,15.5,33ZM27,53c0,.9443-1.71,2-4,2-4.4644-.0917-5.6265-3.1543-1-3.9231V53a1,1,0,0,0,2,0V51.0769C25.7666,51.3274,27,52.2,27,53ZM20.2207,21h5.5586l3.3335,10H16.8872Z">
																</path>
																<path
																	d="M9.4453,66a1,1,0,0,0,1-1V57a1,1,0,0,0-2,0v8A1,1,0,0,0,9.4453,66Z">
																</path>
																<path
																	d="M44,24H68a1,1,0,0,0,1-1V7a1,1,0,0,0-1-1H44a1,1,0,0,0-1,1V23A1,1,0,0,0,44,24ZM67,8V22H45V15.8881l4.8174-3.517L52.3566,15.54l-2.5441,2.4065a1,1,0,0,0,1.375,1.4531l5.8057-5.4922,5.6884,5.4854a1,1,0,1,0,1.3887-1.44l-6.376-6.1484a.9985.9985,0,0,0-1.3818-.0068l-2.4984,2.3632L50.78,10.375a.9992.9992,0,0,0-1.37-.1826L45,13.412V8Z">
																</path>
																<path
																	d="M58,65a1,1,0,0,0-1-1H22.5a1,1,0,0,0,0,2H57A1,1,0,0,0,58,65Z">
																</path>
																<path
																	d="M72,72a1,1,0,0,0-1-1H36.5a1,1,0,0,0,0,2H71A1,1,0,0,0,72,72Z">
																</path>
																<path
																	d="M79,65a1,1,0,0,0-1-1H66a1,1,0,0,0,0,2H78A1,1,0,0,0,79,65Z">
																</path>
																<path d="M39,78a1,1,0,0,0,0,2H51a1,1,0,0,0,0-2Z"></path>
																<path d="M19,71a1,1,0,0,0,0,2H30a1,1,0,0,0,0-2Z"></path>
																<path d="M29,37H27a1,1,0,0,0,0,2h2A1,1,0,0,0,29,37Z">
																</path>
																<path d="M74,17h6a1,1,0,0,0,0-2H74A1,1,0,0,0,74,17Z">
																</path>
																<path
																	d="M74,20a1,1,0,0,0,1,1h8a1,1,0,0,0,0-2H75A1,1,0,0,0,74,20Z">
																</path>
																<path d="M22,10h6a1,1,0,0,0,0-2H22A1,1,0,0,0,22,10Z">
																</path>
																<path
																	d="M22,13a1,1,0,0,0,1,1h8a1,1,0,0,0,0-2H23A1,1,0,0,0,22,13Z">
																</path>
															</g>
														</svg>
													</div>
												</div>
											</div>
										</div>
										<div class="elementor-element elementor-element-d3d743c elementor-widget elementor-widget-heading"
											data-id="d3d743c" data-element_type="widget"
											data-widget_type="heading.default">
											<div class="elementor-widget-container">
												<h1 class="elementor-heading-title elementor-size-default"><a
														href="service.html">Facade Design</a></h1>
											</div>
										</div>
										<div class="elementor-element elementor-element-c5c52a6 animated-slow elementor-invisible elementor-widget elementor-widget-heading"
											data-id="c5c52a6" data-element_type="widget"
											data-settings="{&quot;_animation&quot;:&quot;fadeIn&quot;}"
											data-widget_type="heading.default">
											<div class="elementor-widget-container">
												<p class="elementor-heading-title elementor-size-default">Crafting
													modern and elegant exterior
													identities that enhance curb appeal and
													architectural presence.</p>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="elementor-element elementor-element-1ac1cf1a e-flex e-con-boxed e-con e-parent"
				data-id="1ac1cf1a" data-element_type="container"
				data-settings="{&quot;background_background&quot;:&quot;classic&quot;}">
				<div class="e-con-inner">
					<div class="elementor-element elementor-element-11bb294e elementor-widget elementor-widget-qi_addons_for_elementor_image_gallery"
						data-id="11bb294e" data-element_type="widget"
						data-widget_type="qi_addons_for_elementor_image_gallery.default">
						<div class="elementor-widget-container">
							<div
								class="qodef-shortcode qodef-m qodef-qi-image-gallery qodef-qi-fslightbox-popup qodef-popup-gallery qodef-image--hover-move qodef-qi-grid qodef-layout--qi-columns qodef-col-num--3 qodef-responsive--predefined">
								<div class="qodef-grid-inner">
									<div class="qodef-e qodef-image-wrapper qodef-grid-item">
										<div class="qodef-e-inner">
											<a class="qodef-popup-item" itemprop="image"
												href="./wp-content/uploads/sites/2/2025/04/bright-living-room-decorating-with-rattan-furniture.jpg"
												data-type="image" data-fslightbox="gallery-169">
												<img loading="lazy" decoding="async" width="650" height="650"
													src="./wp-content/uploads/sites/2/2025/04/bright-living-room-decorating-with-rattan-furniture-650x650.jpg"
													class="attachment-qi_addons_for_elementor_image_size_square size-qi_addons_for_elementor_image_size_square"
													alt=""
													srcset="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/bright-living-room-decorating-with-rattan-furniture-650x650.jpg 650w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/bright-living-room-decorating-with-rattan-furniture-150x150.jpg 150w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/bright-living-room-decorating-with-rattan-furniture-550x550.jpg 550w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/bright-living-room-decorating-with-rattan-furniture-300x300.jpg 300w"
													sizes="(max-width: 650px) 100vw, 650px" /> </a>
										</div>
									</div>
									<div class="qodef-e qodef-image-wrapper qodef-grid-item">
										<div class="qodef-e-inner">
											<a class="qodef-popup-item" itemprop="image"
												href="./wp-content/uploads/sites/2/2025/04/festive-christmas-modern-kitchen-with-big-tree.jpg"
												data-type="image" data-fslightbox="gallery-169">
												<img loading="lazy" decoding="async" width="650" height="650"
													src="./wp-content/uploads/sites/2/2025/04/festive-christmas-modern-kitchen-with-big-tree-650x650.jpg"
													class="attachment-qi_addons_for_elementor_image_size_square size-qi_addons_for_elementor_image_size_square"
													alt=""
													srcset="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/festive-christmas-modern-kitchen-with-big-tree-650x650.jpg 650w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/festive-christmas-modern-kitchen-with-big-tree-150x150.jpg 150w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/festive-christmas-modern-kitchen-with-big-tree-550x550.jpg 550w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/festive-christmas-modern-kitchen-with-big-tree-300x300.jpg 300w"
													sizes="(max-width: 650px) 100vw, 650px" /> </a>
										</div>
									</div>
									<div class="qodef-e qodef-image-wrapper qodef-grid-item">
										<div class="qodef-e-inner">
											<a class="qodef-popup-item" itemprop="image"
												href="./wp-content/uploads/sites/2/2025/04/minimalist-composition-modern-living-room-interior-details-creative-vases-wooden-side-table-home-staging-template-copy-space-3"
												data-type="image" data-fslightbox="gallery-169">
												<img loading="lazy" decoding="async" width="650" height="650"
													src="./wp-content/uploads/sites/2/2025/04/minimalist-composition-modern-living-room-interior-details-creative-vases-wooden-side-table-home-staging-template-copy-space-4"
													class="attachment-qi_addons_for_elementor_image_size_square size-qi_addons_for_elementor_image_size_square"
													alt=""
													srcset="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/minimalist-composition-modern-living-room-interior-details-creative-vases-wooden-side-table-home-staging-template-copy-space-xa-650x650.jpg 650w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/minimalist-composition-modern-living-room-interior-details-creative-vases-wooden-side-table-home-staging-template-copy-space-xa-300x300.jpg 300w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/minimalist-composition-modern-living-room-interior-details-creative-vases-wooden-side-table-home-staging-template-copy-space-xa-1024x1024.jpg 1024w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/minimalist-composition-modern-living-room-interior-details-creative-vases-wooden-side-table-home-staging-template-copy-space-xa-150x150.jpg 150w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/minimalist-composition-modern-living-room-interior-details-creative-vases-wooden-side-table-home-staging-template-copy-space-xa-768x768.jpg 768w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/minimalist-composition-modern-living-room-interior-details-creative-vases-wooden-side-table-home-staging-template-copy-space-xa-550x550.jpg 550w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/minimalist-composition-modern-living-room-interior-details-creative-vases-wooden-side-table-home-staging-template-copy-space-xa-1060x1060.jpg 1060w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/minimalist-composition-modern-living-room-interior-details-creative-vases-wooden-side-table-home-staging-template-copy-space-xa-1536x1536.jpg 1536w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/minimalist-composition-modern-living-room-interior-details-creative-vases-wooden-side-table-home-staging-template-copy-space-xa-500x500.jpg 500w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/minimalist-composition-modern-living-room-interior-details-creative-vases-wooden-side-table-home-staging-template-copy-space-xa-1080x1080.jpg 1080w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/minimalist-composition-modern-living-room-interior-details-creative-vases-wooden-side-table-home-staging-template-copy-space-xa-1300x1300.jpg 1300w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/minimalist-composition-modern-living-room-interior-details-creative-vases-wooden-side-table-home-staging-template-copy-space-xa.jpg 1920w"
													sizes="(max-width: 650px) 100vw, 650px" /> </a>
										</div>
									</div>
									<div class="qodef-e qodef-image-wrapper qodef-grid-item">
										<div class="qodef-e-inner">
											<a class="qodef-popup-item" itemprop="image"
												href="./wp-content/uploads/sites/2/2025/04/stylish-boho-vintage-dining-room-interior-with-poster-mock-up-round-table-with-rattan-chair-vase-with-dried-flowers-brown-wal"
												data-type="image" data-fslightbox="gallery-169">
												<img loading="lazy" decoding="async" width="650" height="650"
													src="./wp-content/uploads/sites/2/2025/04/stylish-boho-vintage-dining-room-interior-with-poster-mock-up-round-table-with-rattan-chair-vase-with-dried-flowers-brown-wal-2"
													class="attachment-qi_addons_for_elementor_image_size_square size-qi_addons_for_elementor_image_size_square"
													alt=""
													srcset="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-boho-vintage-dining-room-interior-with-poster-mock-up-round-table-with-rattan-chair-vase-with-dried-flowers-brown-wall-rattan-lamp-mock-up-poster-template-650x650.jpg 650w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-boho-vintage-dining-room-interior-with-poster-mock-up-round-table-with-rattan-chair-vase-with-dried-flowers-brown-wall-rattan-lamp-mock-up-poster-template-150x150.jpg 150w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-boho-vintage-dining-room-interior-with-poster-mock-up-round-table-with-rattan-chair-vase-with-dried-flowers-brown-wall-rattan-lamp-mock-up-poster-template-550x550.jpg 550w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-boho-vintage-dining-room-interior-with-poster-mock-up-round-table-with-rattan-chair-vase-with-dried-flowers-brown-wall-rattan-lamp-mock-up-poster-template-300x300.jpg 300w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-boho-vintage-dining-room-interior-with-poster-mock-up-round-table-with-rattan-chair-vase-with-dried-flowers-brown-wall-rattan-lamp-mock-up-poster-template-1300x1300.jpg 1300w"
													sizes="(max-width: 650px) 100vw, 650px" /> </a>
										</div>
									</div>
									<div class="qodef-e qodef-image-wrapper qodef-grid-item">
										<div class="qodef-e-inner">
											<a class="qodef-popup-item" itemprop="image"
												href="./wp-content/uploads/sites/2/2025/04/stylish-boho-vintage-dining-room-interior-with-rattan-commode-round-mirror-plants-personal-accessories-brown-wall-template-sc"
												data-type="image" data-fslightbox="gallery-169">
												<img loading="lazy" decoding="async" width="650" height="650"
													src="./wp-content/uploads/sites/2/2025/04/stylish-boho-vintage-dining-room-interior-with-rattan-commode-round-mirror-plants-personal-accessories-brown-wall-template-65"
													class="attachment-qi_addons_for_elementor_image_size_square size-qi_addons_for_elementor_image_size_square"
													alt=""
													srcset="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-boho-vintage-dining-room-interior-with-rattan-commode-round-mirror-plants-personal-accessories-brown-wall-template-650x650.jpg 650w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-boho-vintage-dining-room-interior-with-rattan-commode-round-mirror-plants-personal-accessories-brown-wall-template-150x150.jpg 150w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-boho-vintage-dining-room-interior-with-rattan-commode-round-mirror-plants-personal-accessories-brown-wall-template-550x550.jpg 550w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-boho-vintage-dining-room-interior-with-rattan-commode-round-mirror-plants-personal-accessories-brown-wall-template-300x300.jpg 300w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-boho-vintage-dining-room-interior-with-rattan-commode-round-mirror-plants-personal-accessories-brown-wall-template-1300x1300.jpg 1300w"
													sizes="(max-width: 650px) 100vw, 650px" /> </a>
										</div>
									</div>
									<div class="qodef-e qodef-image-wrapper qodef-grid-item">
										<div class="qodef-e-inner">
											<a class="qodef-popup-item" itemprop="image"
												href="./wp-content/uploads/sites/2/2025/04/stylish-composition-cozy-living-room-interior-with-copy-space-lot-plants-wooden-shelves-rattan-sofa-accessories-beige-wall-ca"
												data-type="image" data-fslightbox="gallery-169">
												<img loading="lazy" decoding="async" width="650" height="650"
													src="./wp-content/uploads/sites/2/2025/04/stylish-composition-cozy-living-room-interior-with-copy-space-lot-plants-wooden-shelves-rattan-sofa-accessories-beige-wall-ca-2"
													class="attachment-qi_addons_for_elementor_image_size_square size-qi_addons_for_elementor_image_size_square"
													alt=""
													srcset="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-composition-cozy-living-room-interior-with-copy-space-lot-plants-wooden-shelves-rattan-sofa-accessories-beige-wall-carpet-floor-plants-love-concept-template-650x650.jpg 650w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-composition-cozy-living-room-interior-with-copy-space-lot-plants-wooden-shelves-rattan-sofa-accessories-beige-wall-carpet-floor-plants-love-concept-template-150x150.jpg 150w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-composition-cozy-living-room-interior-with-copy-space-lot-plants-wooden-shelves-rattan-sofa-accessories-beige-wall-carpet-floor-plants-love-concept-template-550x550.jpg 550w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-composition-cozy-living-room-interior-with-copy-space-lot-plants-wooden-shelves-rattan-sofa-accessories-beige-wall-carpet-floor-plants-love-concept-template-300x300.jpg 300w"
													sizes="(max-width: 650px) 100vw, 650px" /> </a>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<!-- <div class="elementor-element elementor-element-7c4ddd51 e-flex e-con-boxed e-con e-parent"
				data-id="7c4ddd51" data-element_type="container">
				<div class="e-con-inner">
					<div class="elementor-element elementor-element-4c6d4bec e-con-full e-flex e-con e-child"
						data-id="4c6d4bec" data-element_type="container">
						<div class="elementor-element elementor-element-18b38097 elementor-invisible elementor-widget elementor-widget-qi_addons_for_elementor_animated_text"
							data-id="18b38097" data-element_type="widget"
							data-settings="{&quot;_animation&quot;:&quot;fadeIn&quot;}"
							data-widget_type="qi_addons_for_elementor_animated_text.default">
							<div class="elementor-widget-container">
								<div
									class="qodef-shortcode qodef-m qodef-qi-animated-text qodef--animated-by-letter qodef-qi--has-appear qodef--appear-fade">
									<h1 class="qodef-m-title">
										<span class="qodef-e-word-holder">Studio</span> <span
											class="qodef-e-word-holder">News</span>
									</h1>
								</div>
							</div>
						</div>
					</div>
					<div class="elementor-element elementor-element-643a8652 e-con-full e-flex e-con e-child"
						data-id="643a8652" data-element_type="container">
						<div class="elementor-element elementor-element-7c2659f0 animated-slow elementor-invisible elementor-widget elementor-widget-heading"
							data-id="7c2659f0" data-element_type="widget"
							data-settings="{&quot;_animation&quot;:&quot;fadeIn&quot;}"
							data-widget_type="heading.default">
							<div class="elementor-widget-container">
								<p class="elementor-heading-title elementor-size-default">Get inspired by my latest projects, design tips, and stories from real clients. Stay connected for fresh ideas and updates!</p>
							</div>
						</div>
						<div class="elementor-element elementor-element-29993e3 elementor-widget elementor-widget-qi_addons_for_elementor_button"
							data-id="29993e3" data-element_type="widget"
							data-widget_type="qi_addons_for_elementor_button.default">
							<div class="elementor-widget-container">
								<a class="qodef-shortcode qodef-m qodef-qi-button qodef-html--link qodef-layout--textual qodef-icon--right qodef-hover--icon-move-horizontal-short"
									href="https://themes.pixelwars.org/interique/demo-01/news/" target="_self">
									<span class="qodef-m-text">View Fresh News!</span>
									<span class="qodef-m-icon">
										<span class="qodef-m-icon-inner">
											<svg xmlns="http://www.w3.org/2000/svg" height="21" viewBox="0 0 21 21"
												width="21">
												<g fill="none" fill-rule="evenodd" stroke="currentColor"
													stroke-linecap="round" stroke-linejoin="round"
													transform="translate(6 6)">
													<path d="m8.5 7.5v-7h-7"></path>
													<path d="m8.5.5-8 8"></path>
												</g>
											</svg> </span>
									</span>
								</a>
							</div>
						</div>
					</div>
					<div class="elementor-element elementor-element-cf11a82 e-con-full e-flex e-con e-child"
						data-id="cf11a82" data-element_type="container">
						<div class="elementor-element elementor-element-8aee633 elementor-widget elementor-widget-qi_addons_for_elementor_blog_list"
							data-id="8aee633" data-element_type="widget"
							data-widget_type="qi_addons_for_elementor_blog_list.default">
							<div class="elementor-widget-container">
								<div
									class="qodef-shortcode qodef-m qodef-addons-blog-list qodef-info-no-icons qodef-title--hover-underline qodef-image--hover-zoom qodef-qi-grid qodef-layout--qi-columns qodef-col-num--4 qodef-item-layout--minimal qodef-responsive--custom qodef-col-num--1680--4 qodef-col-num--1440--4 qodef-col-num--1366--4 qodef-col-num--1024--2 qodef-col-num--768--2 qodef-col-num--680--2 qodef-col-num--480--1">
									<div class="qodef-grid-inner">
										<article
											class="qodef-e qodef-blog-item qodef-grid-item qodef-item--portrait post-5632 post type-post status-publish format-standard has-post-thumbnail hentry category-news is-cat-link-borders-light is-cat-link-rounded">
											<div class="qodef-e-inner">
												<div class="qodef-e-content">
													<div class="qodef-e-info qodef-info--top">
														<div itemprop="dateCreated"
															class="qodef-e-info-item qodef-e-info-date entry-date published updated">
															<a itemprop="url"
																href="https://themes.pixelwars.org/interique/demo-01/2025/05/">
																May 22, 2025 </a>
														</div>
														<div class="qodef-e-info-item qodef-e-info-category">
															<a href="https://themes.pixelwars.org/interique/demo-01/category/news/"
																rel="category tag">News</a>
														</div>
													</div>
													<div class="qodef-e-text">
														<h3 itemprop="name" class="qodef-e-title entry-title">
															<a itemprop="url" class="qodef-e-title-link"
																href="https://themes.pixelwars.org/interique/demo-01/functional-beauty/">
																Functional Beauty </a>
														</h3>
														<p itemprop="description" class="qodef-e-excerpt">
															In 2025, interior design is being reimagined with a sharper
															focus on Functionality and Beauty working hand </p>
													</div>
												</div>
											</div>
										</article>
										<article
											class="qodef-e qodef-blog-item qodef-grid-item qodef-item--portrait post-5652 post type-post status-publish format-standard has-post-thumbnail hentry category-insights is-cat-link-borders-light is-cat-link-rounded">
											<div class="qodef-e-inner">
												<div class="qodef-e-content">
													<div class="qodef-e-info qodef-info--top">
														<div itemprop="dateCreated"
															class="qodef-e-info-item qodef-e-info-date entry-date published updated">
															<a itemprop="url"
																href="https://themes.pixelwars.org/interique/demo-01/2025/05/">
																May 23, 2025 </a>
														</div>
														<div class="qodef-e-info-item qodef-e-info-category">
															<a href="https://themes.pixelwars.org/interique/demo-01/category/insights/"
																rel="category tag">Insights</a>
														</div>
													</div>
													<div class="qodef-e-text">
														<h3 itemprop="name" class="qodef-e-title entry-title">
															<a itemprop="url" class="qodef-e-title-link"
																href="https://themes.pixelwars.org/interique/demo-01/cozy-minimalism/">
																Cozy Minimalism </a>
														</h3>
														<p itemprop="description" class="qodef-e-excerpt">
															A new chapter in interior design is unfolding in 2025 with
															the rise of Cozy Minimalism—a style that blen </p>
													</div>
												</div>
											</div>
										</article>
										<article
											class="qodef-e qodef-blog-item qodef-grid-item qodef-item--portrait post-5654 post type-post status-publish format-standard has-post-thumbnail hentry category-news is-cat-link-borders-light is-cat-link-rounded">
											<div class="qodef-e-inner">
												<div class="qodef-e-content">
													<div class="qodef-e-info qodef-info--top">
														<div itemprop="dateCreated"
															class="qodef-e-info-item qodef-e-info-date entry-date published updated">
															<a itemprop="url"
																href="https://themes.pixelwars.org/interique/demo-01/2025/05/">
																May 24, 2025 </a>
														</div>
														<div class="qodef-e-info-item qodef-e-info-category">
															<a href="https://themes.pixelwars.org/interique/demo-01/category/news/"
																rel="category tag">News</a>
														</div>
													</div>
													<div class="qodef-e-text">
														<h3 itemprop="name" class="qodef-e-title entry-title">
															<a itemprop="url" class="qodef-e-title-link"
																href="https://themes.pixelwars.org/interique/demo-01/space-harmony/">
																Space Harmony </a>
														</h3>
														<p itemprop="description" class="qodef-e-excerpt">
															n 2025, the focus of interior design has shifted toward a
															more thoughtful, holistic approach—enter Space </p>
													</div>
												</div>
											</div>
										</article>
										<article
											class="qodef-e qodef-blog-item qodef-grid-item qodef-item--portrait post-5656 post type-post status-publish format-standard has-post-thumbnail hentry category-news is-cat-link-borders-light is-cat-link-rounded">
											<div class="qodef-e-inner">
												<div class="qodef-e-content">
													<div class="qodef-e-info qodef-info--top">
														<div itemprop="dateCreated"
															class="qodef-e-info-item qodef-e-info-date entry-date published updated">
															<a itemprop="url"
																href="https://themes.pixelwars.org/interique/demo-01/2025/05/">
																May 25, 2025 </a>
														</div>
														<div class="qodef-e-info-item qodef-e-info-category">
															<a href="https://themes.pixelwars.org/interique/demo-01/category/news/"
																rel="category tag">News</a>
														</div>
													</div>
													<div class="qodef-e-text">
														<h3 itemprop="name" class="qodef-e-title entry-title">
															<a itemprop="url" class="qodef-e-title-link"
																href="https://themes.pixelwars.org/interique/demo-01/earthy-elegance/">
																Earthy Elegance </a>
														</h3>
														<p itemprop="description" class="qodef-e-excerpt">
															A rising design movement known as Earthy Elegance is
															transforming interior spaces in 2025. Combining natur </p>
													</div>
												</div>
											</div>
										</article>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div> -->
			<div class="elementor-element elementor-element-5726a643 e-flex e-con-boxed e-con e-parent"
				data-id="5726a643" data-element_type="container"
				data-settings="{&quot;background_background&quot;:&quot;classic&quot;}">
				<div class="e-con-inner">
					<div class="elementor-element elementor-element-5eed100a e-con-full e-flex e-con e-child"
						data-id="5eed100a" data-element_type="container">
						<div class="elementor-element elementor-element-8a9c72f elementor-invisible elementor-widget elementor-widget-qi_addons_for_elementor_animated_text"
							data-id="8a9c72f" data-element_type="widget"
							data-settings="{&quot;_animation&quot;:&quot;fadeIn&quot;}"
							data-widget_type="qi_addons_for_elementor_animated_text.default">
							<div class="elementor-widget-container">
								<div
									class="qodef-shortcode qodef-m qodef-qi-animated-text qodef--animated-by-letter qodef-qi--has-appear qodef--appear-fade">
									<h1 class="qodef-m-title">
										<span class="qodef-e-word-holder">Our</span> <span
											class="qodef-e-word-holder">Design</span> <span
											class="qodef-e-word-holder">Process</span>
									</h1>
								</div>
							</div>
						</div>
					</div>
					<div class="elementor-element elementor-element-2ba1e3f3 e-con-full e-flex e-con e-child"
						data-id="2ba1e3f3" data-element_type="container">
						<div class="elementor-element elementor-element-541b5b7 elementor-widget elementor-widget-qi_addons_for_elementor_animated_text"
							data-id="541b5b7" data-element_type="widget"
							data-widget_type="qi_addons_for_elementor_animated_text.default">
							<div class="elementor-widget-container">
								<div
									class="qodef-shortcode qodef-m qodef-qi-animated-text qodef--animated-by-letter qodef-qi--has-appear qodef--appear-fade">
									<h3 class="qodef-m-title">
										<span class="qodef-e-word-holder">Process</span>
									</h3>
								</div>
							</div>
						</div>
						<div class="elementor-element elementor-element-29681bdb animated-slow elementor-invisible elementor-widget elementor-widget-heading"
							data-id="29681bdb" data-element_type="widget"
							data-settings="{&quot;_animation&quot;:&quot;fadeIn&quot;}"
							data-widget_type="heading.default">
							<div class="elementor-widget-container">
								<p class="elementor-heading-title elementor-size-default">Discover how our thoughtful
									process transforms ideas into personalized, functional, and beautifully styled
									spaces.</p>
							</div>
						</div>
					</div>
					<div class="elementor-element elementor-element-6a19d9f e-con-full e-flex e-con e-child"
						data-id="6a19d9f" data-element_type="container">
						<div class="elementor-element elementor-element-fcf8206 e-con-full e-flex e-con e-child"
							data-id="fcf8206" data-element_type="container">
							<div class="elementor-element elementor-element-6cfc18e elementor-widget elementor-widget-qi_addons_for_elementor_process"
								data-id="6cfc18e" data-element_type="widget"
								data-widget_type="qi_addons_for_elementor_process.default">
								<div class="elementor-widget-container">
									<div
										class="qodef-shortcode qodef-m qodef-qi-process qodef-qi--has-appear qodef-qi-grid qodef-layout--qi-columns qodef-col-num--4 qodef-item-layout--horizontal qodef-responsive--custom qodef-col-num--1680--4 qodef-col-num--1440--4 qodef-col-num--1366--4 qodef-col-num--1024--2 qodef-col-num--768--2 qodef-col-num--680--2 qodef-col-num--480--1">
										<div class="qodef-grid-inner">
											<div
												class="qodef-e qodef-process-item qodef-grid-item elementor-repeater-item-e90ae08">
												<div class="qodef-e-inner">
													<div class="qodef-e-content">
														<div class="qodef-e-icon-holder">
															<div class="qodef-e-icon">
																<span class="qodef-e-item-icon-text">
																	<svg xmlns="http://www.w3.org/2000/svg" id="outline"
																		viewBox="0 0 340 340" width="512" height="512">
																		<path
																			d="M272.071,49.747a3.481,3.481,0,1,0,6.962-.091c-.236-18.042-6.032-29.89-6.279-30.385a3.482,3.482,0,0,0-5.739-.74c-9.268,10.61-15.293,21.4-18.01,32.167a81.989,81.989,0,0,0-19.7-19.919,3.483,3.483,0,0,0-5.339,2.24c-4.579,23.6-2.06,41.861,7.49,54.289a36.833,36.833,0,0,0,4.741,5.093,3.462,3.462,0,0,0-3.322,3.818l4.046,39.924a13.68,13.68,0,0,0,1.69,5.324H187.733c3.6-6.282,8.291-17.636,8.291-34.119a54.918,54.918,0,0,0-9.065-30.926,13.293,13.293,0,0,1-.828-13.356,26.312,26.312,0,0,0,3.049-11.775,3.482,3.482,0,0,0-3.482-3.482H154.3a3.482,3.482,0,0,0-3.482,3.482,26.312,26.312,0,0,0,3.049,11.775,13.293,13.293,0,0,1-.828,13.356,54.918,54.918,0,0,0-9.065,30.926c0,16.483,4.695,27.837,8.291,34.119H111a3.481,3.481,0,0,0-3.482,3.482v7.187a15.748,15.748,0,0,0,15.73,15.729h13.128l2.914,16.373a12.519,12.519,0,0,0,24.651,0l2.914-16.373H273.313l2.915,16.373a12.519,12.519,0,0,0,24.651,0l2.914-16.373h13.129a15.747,15.747,0,0,0,15.729-15.729v-7.187a3.481,3.481,0,0,0-3.482-3.482H284.944a13.673,13.673,0,0,0,1.689-5.324l4.046-39.924a3.482,3.482,0,0,0-3.464-3.833h-1.936c17.571-16.72,12.7-45.244,12.447-46.626a3.482,3.482,0,0,0-6.235-1.439,62.694,62.694,0,0,1-16.848,15.371A59.4,59.4,0,0,0,264.8,67.53a3.482,3.482,0,0,0,4.922,4.927,52.4,52.4,0,0,1,8.687-6.909A75.551,75.551,0,0,0,291.514,55c.168,10.614-2.073,28.975-18.142,37.385H262.623a70.708,70.708,0,0,0-8.136-32.7c.975-10.56,5.692-21.322,14.041-32.056A76.01,76.01,0,0,1,272.071,49.747Zm-121.132,57.6a47.985,47.985,0,0,1,7.882-27.043,20.267,20.267,0,0,0,1.272-20.363,22.744,22.744,0,0,1-1.9-5.17h23.622a22.744,22.744,0,0,1-1.9,5.17,20.267,20.267,0,0,0,1.272,20.363,47.985,47.985,0,0,1,7.882,27.043c0,19.2-7.081,30.642-9.612,34.119h-18.91C158,138.016,150.939,126.691,150.939,107.348Zm6.146,75.67a5.555,5.555,0,0,1-10.939,0l-2.7-15.153h16.334Zm136.938,0a5.556,5.556,0,0,1-10.94,0l-2.7-15.153H296.72Zm31.664-30.882a8.775,8.775,0,0,1-8.765,8.765H123.248a8.775,8.775,0,0,1-8.766-8.765V148.43H325.687Zm-75.175-10.669a6.684,6.684,0,0,1-6.667-6.026l-3.657-36.092h43.175l-3.658,36.092a6.682,6.682,0,0,1-6.667,6.026Zm-1.065-49.081c-3.31-1.014-25.942-9.573-19.621-52.369,8.333,7.166,25.68,25.319,25.836,52.369Z">
																		</path>
																		<path
																			d="M23.078,295.961H36.207l2.914,16.374a12.519,12.519,0,0,0,24.651,0l2.914-16.374H173.145l2.914,16.374a12.519,12.519,0,0,0,24.651,0l2.914-16.374h13.128a15.748,15.748,0,0,0,15.73-15.729v-7.187A3.482,3.482,0,0,0,229,269.563H215.61V252.318c0-.062-.015-.12-.018-.182s.018-.119.018-.181V234.8a3.482,3.482,0,0,0-3.482-3.482H149.492a3.482,3.482,0,0,0-3.482,3.482v14.04h-28.8a3.482,3.482,0,0,0-3.482,3.482v10.363a3.482,3.482,0,1,0,6.964,0V255.8h87.953v13.764H91.236v-87.5a3.482,3.482,0,0,0-3.482-3.482H70.368a3.482,3.482,0,0,0-3.482,3.482v33.775H53.152V196.547a3.482,3.482,0,0,0-3.482-3.482h-20.7a3.482,3.482,0,0,0-3.482,3.482v73.016H10.831a3.482,3.482,0,0,0-3.482,3.482v7.187A15.747,15.747,0,0,0,23.078,295.961Zm129.9-57.683h55.672v10.2H152.974ZM56.917,311.115a5.556,5.556,0,0,1-10.94,0l-2.7-15.154H59.613Zm136.937,0a5.555,5.555,0,0,1-10.939,0l-2.7-15.154h16.333ZM73.85,185.54H84.272v84.023H73.85Zm-41.4,14.489H46.188v59.579a3.482,3.482,0,0,0,6.964,0V222.8H66.886v46.767H32.454Zm-18.141,76.5H225.518v3.705A8.775,8.775,0,0,1,216.752,289H23.078a8.775,8.775,0,0,1-8.765-8.765Z">
																		</path>
																	</svg> </span>
																<div class="qodef-e-number">
																	1. </div>
															</div>
															<div class="qodef-e-line">
																<div class="qodef-e-line-inner"></div>
															</div>
														</div>
														<h3 class="qodef-e-title">
															Discovery & Consultation</h3>
														<p class="qodef-e-text">
															We begin with an in-depth discussion to
															understand your needs, vision, budget,
															and goals — setting the foundation for a
															collaborative journey.</p>
													</div>
												</div>
											</div>
											<div
												class="qodef-e qodef-process-item qodef-grid-item elementor-repeater-item-eedc822">
												<div class="qodef-e-inner">
													<div class="qodef-e-content">
														<div class="qodef-e-icon-holder">
															<div class="qodef-e-icon">
																<span class="qodef-e-item-icon-text">
																	<svg xmlns="http://www.w3.org/2000/svg" id="Layer_1"
																		height="512" viewBox="0 0 512 512" width="512"
																		data-name="Layer 1">
																		<g id="Layer_1-2" data-name="Layer_1">
																			<g id="_256581984" data-name=" 256581984">
																				<g id="_515979528"
																					data-name=" 515979528">
																					<path
																						d="m468.26 470.37h-44a18.76 18.76 0 0 1 -18.73-18.74v-115.19a18.75 18.75 0 0 1 18.73-18.73h44a18.76 18.76 0 0 1 18.74 18.73v115.19a18.77 18.77 0 0 1 -18.74 18.74zm-44-140.66a6.74 6.74 0 0 0 -6.73 6.73v115.19a6.74 6.74 0 0 0 6.73 6.74h44a6.74 6.74 0 0 0 6.74-6.74v-115.19a6.74 6.74 0 0 0 -6.74-6.73z">
																					</path>
																				</g>
																				<g id="_515979696"
																					data-name=" 515979696">
																					<path
																						d="m237.12 470.37h-44a18.76 18.76 0 0 1 -18.74-18.74v-115.19a18.75 18.75 0 0 1 18.74-18.73h44a18.75 18.75 0 0 1 18.74 18.73v115.19a18.76 18.76 0 0 1 -18.74 18.74zm-44-140.66a6.74 6.74 0 0 0 -6.74 6.73v115.19a6.74 6.74 0 0 0 6.74 6.74h44a6.74 6.74 0 0 0 6.74-6.74v-115.19a6.74 6.74 0 0 0 -6.74-6.73z">
																					</path>
																				</g>
																				<g id="_515979504"
																					data-name=" 515979504">
																					<path
																						d="m411.52 459.19h-161.66a6 6 0 0 1 -6-6v-56.66a28.44 28.44 0 0 1 28.41-28.41h116.84a28.44 28.44 0 0 1 28.41 28.41v56.66a6 6 0 0 1 -6 6zm-155.66-12h149.66v-50.66a16.43 16.43 0 0 0 -16.41-16.41h-116.84a16.43 16.43 0 0 0 -16.41 16.41z">
																					</path>
																				</g>
																				<g id="_515979648"
																					data-name=" 515979648">
																					<path
																						d="m446.26 329.71a6 6 0 0 1 -6-6v-75.32c0-10.76-10.35-19.51-23.07-19.51h-173c-12.72 0-23.07 8.75-23.07 19.51v75.32a6 6 0 0 1 -12 0v-75.32c0-17.38 15.73-31.51 35.07-31.51h173c19.34 0 35.07 14.13 35.07 31.51v75.32a6 6 0 0 1 -6 6z">
																					</path>
																				</g>
																				<g id="_515979552"
																					data-name=" 515979552">
																					<path
																						d="m330.69 345.67a6 6 0 0 1 -4.47-2l-36.87-41.17a6 6 0 0 1 0-8l36.87-41.17a6 6 0 0 1 8.94 0l36.84 41.17a6 6 0 0 1 0 8l-36.87 41.17a6 6 0 0 1 -4.44 2zm-28.82-47.17 28.82 32.17 28.81-32.17-28.81-32.18z">
																					</path>
																				</g>
																				<g id="_515979624"
																					data-name=" 515979624">
																					<path
																						d="m274.32 270.27h-28.32a6 6 0 0 1 0-12h28.36a6 6 0 0 1 0 12z">
																					</path>
																				</g>
																				<g id="_515979432"
																					data-name=" 515979432">
																					<path
																						d="m415.42 270.27h-28.36a6 6 0 1 1 0-12h28.36a6 6 0 1 1 0 12z">
																					</path>
																				</g>
																				<g id="_515978808"
																					data-name=" 515978808">
																					<path
																						d="m359.4 411.55h-57.4a6 6 0 0 1 0-12h57.4a6 6 0 0 1 0 12z">
																					</path>
																				</g>
																				<g id="_515978280"
																					data-name=" 515978280">
																					<path
																						d="m148 470.37h-74.63a6 6 0 0 1 -6-6v-36.09a6 6 0 0 1 6-6h74.63a6 6 0 0 1 6 6v36.09a6 6 0 0 1 -6 6zm-68.6-12h62.6v-24.09h-62.63z">
																					</path>
																				</g>
																				<g id="_515978376"
																					data-name=" 515978376">
																					<path
																						d="m129.32 434.28h-37.32a6 6 0 0 1 -6-6v-259.28a6 6 0 0 1 6-6h37.3a6 6 0 0 1 6 6v259.28a6 6 0 0 1 -5.98 6zm-31.3-12h25.3v-247.28h-25.32z">
																					</path>
																				</g>
																				<g id="_515978760"
																					data-name=" 515978760">
																					<path
																						d="m190.34 175h-159.34a6 6 0 0 1 -5.64-8l44.21-121.42a6 6 0 0 1 5.64-3.95h70.92a6 6 0 0 1 5.64 3.95l44.23 121.42a6 6 0 0 1 -5.63 8.05zm-150.77-12h142.2l-39.84-109.37h-62.52z">
																					</path>
																				</g>
																				<g id="_515978736"
																					data-name=" 515978736">
																					<path
																						d="m139.3 175h-57.3a6 6 0 0 1 -6-6.78l16-121.37a6 6 0 0 1 6-5.22h25.48a6 6 0 0 1 6 5.22l15.89 121.4a6 6 0 0 1 -6.07 6.75zm-50.43-12h43.59l-14.31-109.37h-15z">
																					</path>
																				</g>
																				<g id="_515978328"
																					data-name=" 515978328">
																					<path
																						d="m481 175h-225.31a6 6 0 0 1 -6-6v-121.37a6 6 0 0 1 6-6h225.31a6 6 0 0 1 6 6v121.37a6 6 0 0 1 -6 6zm-219.31-12h213.31v-109.37h-213.31z">
																					</path>
																				</g>
																				<g id="_256583472"
																					data-name=" 256583472">
																					<path
																						d="m392 175a6 6 0 0 1 -5.58-3.8l-24.95-63.2-32.83 27.62a6 6 0 0 1 -8.72-1.07l-23.48-32.35-36.15 43.3a6 6 0 0 1 -9.21-7.7l41.08-49.17a6 6 0 0 1 9.46.33l24.23 33.37 34.28-28.84a6 6 0 0 1 9.44 2.39l28 71a6 6 0 0 1 -3.38 7.78 5.88 5.88 0 0 1 -2.19.34z">
																					</path>
																				</g>
																				<g id="_256582464"
																					data-name=" 256582464">
																					<path
																						d="m377.29 137.77a6 6 0 0 1 -3.77-10.68c29.21-23.51 48.57-14.78 64.13-7.77 13.55 6.1 23.34 10.52 39.24-4.44a6 6 0 1 1 8.22 8.73c-21.74 20.46-38 13.12-52.4 6.65s-28.19-12.71-51.66 6.18a6 6 0 0 1 -3.76 1.33z">
																					</path>
																				</g>
																				<g id="_256582272"
																					data-name=" 256582272">
																					<path
																						d="m441.13 107.89a21.76 21.76 0 1 1 21.75-21.75 21.77 21.77 0 0 1 -21.75 21.75zm0-31.51a9.76 9.76 0 1 0 9.75 9.76 9.77 9.77 0 0 0 -9.75-9.76z">
																					</path>
																				</g>
																			</g>
																		</g>
																	</svg> </span>
																<div class="qodef-e-number">
																	2. </div>
															</div>
															<div class="qodef-e-line">
																<div class="qodef-e-line-inner"></div>
															</div>
														</div>
														<h3 class="qodef-e-title">
															Concept & Visualization</h3>
														<p class="qodef-e-text">
															Our designers translate your brief into
															mood boards, layouts, and 3D
															visualizations, helping you see your
															space before it s built.</p>
													</div>
												</div>
											</div>
											<div
												class="qodef-e qodef-process-item qodef-grid-item elementor-repeater-item-ba4f0f7">
												<div class="qodef-e-inner">
													<div class="qodef-e-content">
														<div class="qodef-e-icon-holder">
															<div class="qodef-e-icon">
																<span class="qodef-e-item-icon-text">
																	<svg xmlns="http://www.w3.org/2000/svg"
																		viewBox="0 0 512 512">
																		<g id="_x32_8_Chair">
																			<g>
																				<path
																					d="m390.708 249.597c29.899-75.478 2.758-185.094-30.891-217.4-9.566-15.597-20.36-25.181-30.944-25.181-38.589 0-54.642 129.641-58.642 169.374-1.583 15.719-14.208 28.134-30.017 29.519-89.917 7.881-131.796 23.044-131.796 47.721 0 11.292 5.573 21.062 15.118 29.272 5.263 12.556 17.804 22.478 34.531 29.953-6.614 36.396-28.703 179.77-29.739 186.503-.377 2.453 1.144 4.796 3.533 5.452 2.377.653 4.892-.576 5.821-2.889 2.537-6.309 56.368-140.348 69.35-175.32 11.343 1.85 23.114 3.069 34.872 3.676 15.045 39.857 65.52 165.54 67.974 171.644.929 2.315 3.446 3.542 5.821 2.889 2.389-.656 3.909-2.999 3.533-5.452-.925-6.007-18.606-120.768-27.108-171.312 8.376-1.424 16.129-3.371 23.271-5.795 25.468 42.057 63.702 104.129 66.04 107.926 1.304 2.115 3.986 2.91 6.227 1.862 2.246-1.051 3.347-3.619 2.56-5.971-1.428-4.273-25.02-74.737-40.994-121.241 21.355-15.765 34.025-36.402 41.48-55.23zm-272.526 4.033c0-10.167 15.962-28.625 122.88-37.995 20.482-1.795 36.835-17.888 38.885-38.267 9.368-93.05 29.945-160.588 48.925-160.588 6.76 0 15.031 7.775 22.991 21.024.009.015.009.032.018.046 41.287 68.613 53.651 228.697-57.531 260.785-42.527 12.296-130.266 5.344-163.563-22.182-.258-.215-.559-.339-.847-.493-7.441-6.427-11.758-13.881-11.758-22.33zm26.332 205.003c8.307-53.407 18.533-118.15 22.786-142.046 9.2 3.363 19.329 6.104 29.994 8.246-8.531 22.754-32.72 83.641-52.78 133.8zm158.532 0c-18.538-46.352-40.596-101.854-50.572-127.974 9.763.205 17.827.105 29.987-1.272 4.976 29.629 13.482 83.58 20.585 129.246zm64.292-70.036c-13.886-22.635-29.898-48.814-42.645-69.843 5.811-2.469 11.182-5.251 16.147-8.291 8.009 23.366 17.946 52.74 26.498 78.134zm-107.648-67.617c-42.847 0-80.935-7.933-104.406-20.51 37.64 13.707 101.039 19.641 142.931 7.251 38.454-11.429 65.295-37.125 79.944-74.299 10.601-26.728 16.369-63.967 10.791-108.761 7.361 38.86 7.578 83.738-7.318 121.341-19.706 49.753-60.736 74.978-121.942 74.978z">
																				</path>
																				<path
																					d="m342.999 216.274c.93-2.532-.367-5.337-2.899-6.265-2.522-.932-5.335.369-6.265 2.899-13.235 36.036-40.744 52.82-86.58 52.82-37.249 0-62.046-6.534-73.512-10.429-2.555-.863-5.325.503-6.193 3.051-.863 2.553.501 5.325 3.056 6.193 12.033 4.088 37.998 10.949 76.649 10.949 50.485 0 80.907-18.818 95.744-59.218z">
																				</path>
																			</g>
																		</g>
																		<g id="Layer_1"></g>
																	</svg> </span>
																<div class="qodef-e-number">
																	3. </div>
															</div>
															<div class="qodef-e-line">
																<div class="qodef-e-line-inner"></div>
															</div>
														</div>
														<h3 class="qodef-e-title">
															Planning & Execution </h3>
														<p class="qodef-e-text">
															Once approved, we finalize drawings,
															materials, and timelines. Our expert
															team ensures seamless execution with
															precision and craftsmanship.</p>
													</div>
												</div>
											</div>
											<div
												class="qodef-e qodef-process-item qodef-grid-item elementor-repeater-item-a7a238a">
												<div class="qodef-e-inner">
													<div class="qodef-e-content">
														<div class="qodef-e-icon-holder">
															<div class="qodef-e-icon">
																<span class="qodef-e-item-icon-text">
																	<svg xmlns="http://www.w3.org/2000/svg" id="Layer_1"
																		height="512" viewBox="0 0 480 480" width="512">
																		<path
																			d="m220 157v-116c0-12.131-9.869-22-22-22h-176c-12.131 0-22 9.869-22 22v116c0 12.131 9.869 22 22 22h176c12.131 0 22-9.869 22-22zm-204 0v-12.073l43.873-29.51c2.609-1.755 6.021-1.816 8.691-.154l26.642 16.577c8.585 5.341 19.672 4.708 27.594-1.573l40.953-32.481c2.497-1.98 5.926-2.291 8.736-.791 3.896 2.079 8.744.604 10.823-3.293 2.079-3.898.604-8.745-3.294-10.824-8.432-4.497-18.719-3.566-26.208 2.372l-40.953 32.481c-2.64 2.095-6.336 2.305-9.198.524l-26.642-16.577c-8.013-4.985-18.247-4.802-26.074.463l-34.943 23.503v-84.644c0-3.309 2.691-6 6-6h176c3.309 0 6 2.691 6 6v116c0 3.309-2.691 6-6 6h-176c-3.309 0-6-2.691-6-6zm94-101c-8.271 0-15 6.729-15 15s6.729 15 15 15 15-6.729 15-15-6.729-15-15-15zm-1 15c0-1.322 2-1.322 2 0s-2 1.322-2 0zm371 284.87v69.261c0 19.779-16.091 35.87-35.869 35.87h-40.131c-4.418 0-8-3.582-8-8v-51h-171c-4.418 0-8-3.582-8-8s3.582-8 8-8h179c4.724 0 8.541 4.118 7.931 9 .108.862.069-2.344.069 50h32.131c10.956 0 19.869-8.914 19.869-19.87v-69.261c0-10.956-8.913-19.87-19.869-19.87h-12.262c-10.956 0-19.869 8.914-19.869 19.87v7.13c0 4.418-3.582 8-8 8s-8-3.582-8-8v-7.13c0-18.471 14.035-33.722 32-35.658v-52.375c0-14.798-12.039-26.837-26.837-26.837h-100.163v90c0 4.418-3.582 8-8 8s-8-3.582-8-8v-90h-102.163c-14.798 0-26.837 12.039-26.837 26.837v19.163c0 4.418-3.582 8-8 8s-8-3.582-8-8v-19.163c0-23.62 19.217-42.837 42.837-42.837h218.326c23.62 0 42.837 19.217 42.837 42.837v52.163c20.182 0 36 16.311 36 35.87zm-109 97.13c0 4.418-3.582 8-8 8-7.233 0-209.769 0-221.131 0-19.778 0-35.869-16.091-35.869-35.87v-69.26c0-19.779 16.091-35.87 35.869-35.87h12.262c19.778 0 35.869 16.091 35.869 35.87v45.13c0 4.418-3.582 8-8 8s-8-3.582-8-8v-45.13c0-10.956-8.913-19.87-19.869-19.87h-12.262c-10.956 0-19.869 8.914-19.869 19.87v69.261c0 10.956 8.913 19.87 19.869 19.87h221.131c4.418-.001 8 3.581 8 7.999z">
																		</path>
																	</svg> </span>
																<div class="qodef-e-number">
																	4. </div>
															</div>
															<div class="qodef-e-line">
																<div class="qodef-e-line-inner"></div>
															</div>
														</div>
														<h3 class="qodef-e-title">
															Final Touch & Handover </h3>
														<p class="qodef-e-text">
															After quality checks and detailing,
															your space is handed over — styled,
															polished, and ready to experience,
															with ongoing post-handover support.</p>
													</div>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="elementor-element elementor-element-6795591 e-con-full e-flex e-con e-parent" data-id="6795591"
				data-element_type="container">
				<div class="elementor-element elementor-element-643f98d elementor-widget elementor-widget-qi_addons_for_elementor_image_slider"
					data-id="643f98d" data-element_type="widget"
					data-widget_type="qi_addons_for_elementor_image_slider.default">
					<div class="elementor-widget-container">
						<div class="qodef-shortcode qodef-m qodef-qi-image-slider qodef-image--hover-zoom-out qodef-col-num--2 qodef-qi-grid qodef-qi-swiper-container"
							data-options="{&quot;direction&quot;:&quot;horizontal&quot;,&quot;slidesPerView&quot;:&quot;2&quot;,&quot;spaceBetween&quot;:0,&quot;spaceBetweenTablet&quot;:0,&quot;spaceBetweenMobile&quot;:12,&quot;effect&quot;:&quot;&quot;,&quot;loop&quot;:true,&quot;autoplay&quot;:true,&quot;centeredSlides&quot;:false,&quot;speed&quot;:&quot;&quot;,&quot;speedAnimation&quot;:&quot;&quot;,&quot;outsideNavigation&quot;:&quot;no&quot;,&quot;outsidePagination&quot;:&quot;no&quot;,&quot;unique&quot;:261,&quot;partialValue&quot;:0,&quot;disablePartialValue&quot;:&quot;&quot;,&quot;parallax&quot;:&quot;no&quot;,&quot;customStages&quot;:true,&quot;slidesPerView1680&quot;:&quot;3&quot;,&quot;slidesPerView1440&quot;:&quot;3&quot;,&quot;slidesPerView1366&quot;:&quot;3&quot;,&quot;slidesPerView1024&quot;:&quot;3&quot;,&quot;slidesPerView768&quot;:&quot;3&quot;,&quot;slidesPerView680&quot;:&quot;3&quot;,&quot;slidesPerView480&quot;:&quot;3&quot;}">
							<div class="swiper-wrapper">
								<div class="qodef-e swiper-slide">
									<div class="qodef-e-inner">
										<div class="qodef-e-inner-holder">
											<img loading="lazy" decoding="async" width="2400" height="1200"
												src="./wp-content/uploads/sites/2/2025/04/Modern-Sculptural-Chair.jpg"
												class="attachment-full size-full" alt=""
												srcset="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/Modern-Sculptural-Chair.jpeg 2400w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/Modern-Sculptural-Chair-300x150.jpeg 300w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/Modern-Sculptural-Chair-1024x512.jpeg 1024w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/Modern-Sculptural-Chair-768x384.jpeg 768w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/Modern-Sculptural-Chair-1100x550.jpeg 1100w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/Modern-Sculptural-Chair-1060x530.jpeg 1060w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/Modern-Sculptural-Chair-1920x960.jpeg 1920w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/Modern-Sculptural-Chair-1536x768.jpeg 1536w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/Modern-Sculptural-Chair-2048x1024.jpeg 2048w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/Modern-Sculptural-Chair-550x275.jpeg 550w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/Modern-Sculptural-Chair-1000x500.jpeg 1000w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/Modern-Sculptural-Chair-1300x650.jpeg 1300w"
												sizes="(max-width: 2400px) 100vw, 2400px" />
										</div>
									</div>
								</div>
								<div class="qodef-e swiper-slide">
									<div class="qodef-e-inner">
										<div class="qodef-e-inner-holder">
											<img loading="lazy" decoding="async" width="1706" height="2560"
												src="./wp-content/uploads/sites/2/2025/04/stylish-boho-vintage-dining-room-interior-with-poster-mock-up-round-table-with-rattan-chair-vase-with-dried-flowers-brown-wal"
												class="attachment-full size-full" alt=""
												srcset="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-boho-vintage-dining-room-interior-with-poster-mock-up-round-table-with-rattan-chair-vase-with-dried-flowers-brown-wall-rattan-lamp-mock-up-poster-template-scaled.jpg 1706w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-boho-vintage-dining-room-interior-with-poster-mock-up-round-table-with-rattan-chair-vase-with-dried-flowers-brown-wall-rattan-lamp-mock-up-poster-template-200x300.jpg 200w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-boho-vintage-dining-room-interior-with-poster-mock-up-round-table-with-rattan-chair-vase-with-dried-flowers-brown-wall-rattan-lamp-mock-up-poster-template-682x1024.jpg 682w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-boho-vintage-dining-room-interior-with-poster-mock-up-round-table-with-rattan-chair-vase-with-dried-flowers-brown-wall-rattan-lamp-mock-up-poster-template-768x1152.jpg 768w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-boho-vintage-dining-room-interior-with-poster-mock-up-round-table-with-rattan-chair-vase-with-dried-flowers-brown-wall-rattan-lamp-mock-up-poster-template-367x550.jpg 367w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-boho-vintage-dining-room-interior-with-poster-mock-up-round-table-with-rattan-chair-vase-with-dried-flowers-brown-wall-rattan-lamp-mock-up-poster-template-1060x1591.jpg 1060w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-boho-vintage-dining-room-interior-with-poster-mock-up-round-table-with-rattan-chair-vase-with-dried-flowers-brown-wall-rattan-lamp-mock-up-poster-template-1024x1536.jpg 1024w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-boho-vintage-dining-room-interior-with-poster-mock-up-round-table-with-rattan-chair-vase-with-dried-flowers-brown-wall-rattan-lamp-mock-up-poster-template-1365x2048.jpg 1365w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-boho-vintage-dining-room-interior-with-poster-mock-up-round-table-with-rattan-chair-vase-with-dried-flowers-brown-wall-rattan-lamp-mock-up-poster-template-550x825.jpg 550w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-boho-vintage-dining-room-interior-with-poster-mock-up-round-table-with-rattan-chair-vase-with-dried-flowers-brown-wall-rattan-lamp-mock-up-poster-template-333x500.jpg 333w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-boho-vintage-dining-room-interior-with-poster-mock-up-round-table-with-rattan-chair-vase-with-dried-flowers-brown-wall-rattan-lamp-mock-up-poster-template-720x1080.jpg 720w"
												sizes="(max-width: 1706px) 100vw, 1706px" />
										</div>
									</div>
								</div>
								<div class="qodef-e swiper-slide">
									<div class="qodef-e-inner">
										<div class="qodef-e-inner-holder">
											<img loading="lazy" decoding="async" width="2400" height="1200"
												src="./wp-content/uploads/sites/2/2025/04/Modern-Interior-with-Plush-White-Chair.jpg"
												class="attachment-full size-full" alt=""
												srcset="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/Modern-Interior-with-Plush-White-Chair.jpeg 2400w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/Modern-Interior-with-Plush-White-Chair-300x150.jpeg 300w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/Modern-Interior-with-Plush-White-Chair-1024x512.jpeg 1024w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/Modern-Interior-with-Plush-White-Chair-768x384.jpeg 768w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/Modern-Interior-with-Plush-White-Chair-1100x550.jpeg 1100w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/Modern-Interior-with-Plush-White-Chair-1060x530.jpeg 1060w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/Modern-Interior-with-Plush-White-Chair-1920x960.jpeg 1920w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/Modern-Interior-with-Plush-White-Chair-1536x768.jpeg 1536w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/Modern-Interior-with-Plush-White-Chair-2048x1024.jpeg 2048w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/Modern-Interior-with-Plush-White-Chair-550x275.jpeg 550w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/Modern-Interior-with-Plush-White-Chair-1000x500.jpeg 1000w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/Modern-Interior-with-Plush-White-Chair-1300x650.jpeg 1300w"
												sizes="(max-width: 2400px) 100vw, 2400px" />
										</div>
									</div>
								</div>
								<div class="qodef-e swiper-slide">
									<div class="qodef-e-inner">
										<div class="qodef-e-inner-holder">
											<img loading="lazy" decoding="async" width="1920" height="1200"
												src="./wp-content/uploads/sites/2/2025/04/stylish-living-room-interior-with-design-furniture-elegant-accessories-bamboo-ceiling-3d-rende.jpg"
												class="attachment-full size-full" alt=""
												srcset="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-living-room-interior-with-design-furniture-elegant-accessories-bamboo-ceiling-3d-rende.jpg 1920w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-living-room-interior-with-design-furniture-elegant-accessories-bamboo-ceiling-3d-rende-300x188.jpg 300w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-living-room-interior-with-design-furniture-elegant-accessories-bamboo-ceiling-3d-rende-1024x640.jpg 1024w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-living-room-interior-with-design-furniture-elegant-accessories-bamboo-ceiling-3d-rende-768x480.jpg 768w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-living-room-interior-with-design-furniture-elegant-accessories-bamboo-ceiling-3d-rende-880x550.jpg 880w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-living-room-interior-with-design-furniture-elegant-accessories-bamboo-ceiling-3d-rende-1060x663.jpg 1060w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-living-room-interior-with-design-furniture-elegant-accessories-bamboo-ceiling-3d-rende-1536x960.jpg 1536w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-living-room-interior-with-design-furniture-elegant-accessories-bamboo-ceiling-3d-rende-550x344.jpg 550w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-living-room-interior-with-design-furniture-elegant-accessories-bamboo-ceiling-3d-rende-800x500.jpg 800w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-living-room-interior-with-design-furniture-elegant-accessories-bamboo-ceiling-3d-rende-1728x1080.jpg 1728w"
												sizes="(max-width: 1920px) 100vw, 1920px" />
										</div>
									</div>
								</div>
								<div class="qodef-e swiper-slide">
									<div class="qodef-e-inner">
										<div class="qodef-e-inner-holder">
											<img loading="lazy" decoding="async" width="1920" height="1280"
												src="./wp-content/uploads/sites/2/2025/04/stylish-dining-room-with-round-table-rattan-chair-wooden-commode-poster-kitchen-accessories-beige-wall-with-mock-up-poster-ho"
												class="attachment-full size-full" alt=""
												srcset="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-dining-room-with-round-table-rattan-chair-wooden-commode-poster-kitchen-accessories-beige-wall-with-mock-up-poster-home-decor-template.jpg 1920w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-dining-room-with-round-table-rattan-chair-wooden-commode-poster-kitchen-accessories-beige-wall-with-mock-up-poster-home-decor-template-300x200.jpg 300w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-dining-room-with-round-table-rattan-chair-wooden-commode-poster-kitchen-accessories-beige-wall-with-mock-up-poster-home-decor-template-1024x683.jpg 1024w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-dining-room-with-round-table-rattan-chair-wooden-commode-poster-kitchen-accessories-beige-wall-with-mock-up-poster-home-decor-template-768x512.jpg 768w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-dining-room-with-round-table-rattan-chair-wooden-commode-poster-kitchen-accessories-beige-wall-with-mock-up-poster-home-decor-template-825x550.jpg 825w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-dining-room-with-round-table-rattan-chair-wooden-commode-poster-kitchen-accessories-beige-wall-with-mock-up-poster-home-decor-template-1060x707.jpg 1060w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-dining-room-with-round-table-rattan-chair-wooden-commode-poster-kitchen-accessories-beige-wall-with-mock-up-poster-home-decor-template-1536x1024.jpg 1536w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-dining-room-with-round-table-rattan-chair-wooden-commode-poster-kitchen-accessories-beige-wall-with-mock-up-poster-home-decor-template-550x367.jpg 550w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-dining-room-with-round-table-rattan-chair-wooden-commode-poster-kitchen-accessories-beige-wall-with-mock-up-poster-home-decor-template-750x500.jpg 750w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-dining-room-with-round-table-rattan-chair-wooden-commode-poster-kitchen-accessories-beige-wall-with-mock-up-poster-home-decor-template-1620x1080.jpg 1620w"
												sizes="(max-width: 1920px) 100vw, 1920px" />
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="elementor-element elementor-element-e0b51c5 e-flex e-con-boxed e-con e-parent" data-id="e0b51c5"
				data-element_type="container" data-settings="{&quot;background_background&quot;:&quot;classic&quot;}">
				<div class="e-con-inner">
					<div class="elementor-element elementor-element-45a0070 e-con-full e-flex e-con e-child"
						data-id="45a0070" data-element_type="container">
						<div class="elementor-element elementor-element-aa66b41 elementor-invisible elementor-widget elementor-widget-qi_addons_for_elementor_animated_text"
							data-id="aa66b41" data-element_type="widget"
							data-settings="{&quot;_animation&quot;:&quot;fadeIn&quot;}"
							data-widget_type="qi_addons_for_elementor_animated_text.default">
							<div class="elementor-widget-container">
								<div
									class="qodef-shortcode qodef-m qodef-qi-animated-text qodef--animated-by-letter qodef-qi--has-appear qodef--appear-fade">
									<h1 class="qodef-m-title">
										<span class="qodef-e-word-holder">Client</span> <span
											class="qodef-e-word-holder">Testimonials</span>
									</h1>
								</div>
							</div>
						</div>
					</div>
					<div class="elementor-element elementor-element-59c63f6 e-con-full e-flex e-con e-child"
						data-id="59c63f6" data-element_type="container">
						<div class="elementor-element elementor-element-f183de0 elementor-widget elementor-widget-qi_addons_for_elementor_animated_text"
							data-id="f183de0" data-element_type="widget"
							data-widget_type="qi_addons_for_elementor_animated_text.default">
							<div class="elementor-widget-container">
								<div
									class="qodef-shortcode qodef-m qodef-qi-animated-text qodef--animated-by-letter qodef-qi--has-appear qodef--appear-fade">
									<h3 class="qodef-m-title">
										<span class="qodef-e-word-holder">Testimonials</span>
									</h3>
								</div>
							</div>
						</div>
						<div class="elementor-element elementor-element-ac62840 animated-slow elementor-invisible elementor-widget elementor-widget-heading"
							data-id="ac62840" data-element_type="widget"
							data-settings="{&quot;_animation&quot;:&quot;fadeIn&quot;}"
							data-widget_type="heading.default">
							<div class="elementor-widget-container">
								<p class="elementor-heading-title elementor-size-default">Hear from homeowners and small
									business owners who trusted us to bring their vision to life. Your story could be
									next!</p>
							</div>
						</div>
					</div>
					<div class="elementor-element elementor-element-7bdc34f e-con-full e-flex e-con e-child"
						data-id="7bdc34f" data-element_type="container">
						<div class="elementor-element elementor-element-6b01d3e e-con-full e-flex e-con e-child"
							data-id="6b01d3e" data-element_type="container">
							<div class="elementor-element elementor-element-c99959b elementor-widget elementor-widget-qi_addons_for_elementor_testimonials_slider"
								data-id="c99959b" data-element_type="widget"
								data-widget_type="qi_addons_for_elementor_testimonials_slider.default">
								<div class="elementor-widget-container">
									<div class="qodef-shortcode qodef-m qodef-qi-testimonials-slider qodef-qi-grid qodef-qi-swiper-container qodef-item-layout--standard qodef-navigation--outside qodef-pagination--outside qodef-hide-navigation--1024 qodef-navigation--hover-move"
										data-options="{&quot;direction&quot;:&quot;horizontal&quot;,&quot;slidesPerView&quot;:&quot;3&quot;,&quot;spaceBetween&quot;:80,&quot;spaceBetweenTablet&quot;:60,&quot;spaceBetweenMobile&quot;:0,&quot;effect&quot;:&quot;&quot;,&quot;loop&quot;:true,&quot;autoplay&quot;:false,&quot;centeredSlides&quot;:false,&quot;speed&quot;:&quot;&quot;,&quot;speedAnimation&quot;:&quot;500&quot;,&quot;outsideNavigation&quot;:&quot;yes&quot;,&quot;outsidePagination&quot;:&quot;yes&quot;,&quot;unique&quot;:&quot;1&quot;,&quot;partialValue&quot;:0,&quot;disablePartialValue&quot;:&quot;&quot;,&quot;parallax&quot;:&quot;no&quot;}">
										<div class="swiper-wrapper">
											<div class="qodef-e swiper-slide elementor-repeater-item-b61893c">
												<div class="qodef-e-inner">
													<div class="qodef-e-content">
														<div class="qodef-e-quote">
															<svg class="qodef-e-quote-icon"
																xmlns="http://www.w3.org/2000/svg" x="0px" y="0px"
																viewBox="0 0 190.5 148" xml:space="preserve">
																<g>
																	<path
																		d="M37.7,146.3L2.1,124.6C19.3,100,28.2,74.1,28.8,46.7V2.3H90v38.8c0,19.3-5,38.8-15.1,58.4C64.9,119,52.5,134.6,37.7,146.3z M133.7,146.3l-35.6-21.7c17.2-24.5,26.2-50.5,26.8-77.9V2.3h61.2v38.8c0,19.3-5,38.8-15.1,58.4C160.9,119,148.5,134.6,133.7,146.3z" />
																</g>
															</svg>
														</div>
														<h3 itemprop="description" class="qodef-e-text">As a boutique
															owner, I wanted a unique look that reflected my brand. The
															design exceeded my expectations and my customers always
															compliment the beautiful space!</h3>
														<div class="qodef-e-bottom-info">
															<div class="qodef-e-media-image">
																<img loading="lazy" decoding="async" width="150"
																	height="150"
																	src="./wp-content/uploads/sites/2/2023/10/analise-benevides-QWf3AIF9C90-unsplash-150x150.jpg"
																	class="attachment-thumbnail size-thumbnail" alt=""
																	srcset="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2023/10/analise-benevides-QWf3AIF9C90-unsplash-150x150.jpg 150w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2023/10/analise-benevides-QWf3AIF9C90-unsplash-300x300.jpg 300w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2023/10/analise-benevides-QWf3AIF9C90-unsplash-551x550.jpg 551w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2023/10/analise-benevides-QWf3AIF9C90-unsplash-550x550.jpg 550w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2023/10/analise-benevides-QWf3AIF9C90-unsplash-501x500.jpg 501w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2023/10/analise-benevides-QWf3AIF9C90-unsplash.jpg 633w"
																	sizes="(max-width: 150px) 100vw, 150px" />
															</div>
															<div class="qodef-e-author">
																<h5 class="qodef-e-author-name">James Clark</h5>
																<span class="qodef-e-author-job">Boutique Owner -
																	Atlas</span>
															</div>
														</div>
													</div>
												</div>
											</div>
											<div class="qodef-e swiper-slide elementor-repeater-item-fcd673c">
												<div class="qodef-e-inner">
													<div class="qodef-e-content">
														<div class="qodef-e-quote">
															<svg class="qodef-e-quote-icon"
																xmlns="http://www.w3.org/2000/svg" x="0px" y="0px"
																viewBox="0 0 190.5 148" xml:space="preserve">
																<g>
																	<path
																		d="M37.7,146.3L2.1,124.6C19.3,100,28.2,74.1,28.8,46.7V2.3H90v38.8c0,19.3-5,38.8-15.1,58.4C64.9,119,52.5,134.6,37.7,146.3z M133.7,146.3l-35.6-21.7c17.2-24.5,26.2-50.5,26.8-77.9V2.3h61.2v38.8c0,19.3-5,38.8-15.1,58.4C160.9,119,148.5,134.6,133.7,146.3z" />
																</g>
															</svg>
														</div>
														<h3 itemprop="description" class="qodef-e-text">Motiff Square transformed
															our living room into a cozy, inviting space that truly feels
															like home. Her attention to detail and warm personality made
															the entire process enjoyable and stress-free</h3>
														<div class="qodef-e-bottom-info">
															<div class="qodef-e-media-image">
																<img loading="lazy" decoding="async" width="150"
																	height="150"
																	src="./wp-content/uploads/sites/2/2023/10/team_4-150x150.jpg"
																	class="attachment-thumbnail size-thumbnail" alt=""
																	srcset="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2023/10/team_4-150x150.jpg 150w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2023/10/team_4-300x300.jpg 300w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2023/10/team_4.jpg 400w"
																	sizes="(max-width: 150px) 100vw, 150px" />
															</div>
															<div class="qodef-e-author">
																<h5 class="qodef-e-author-name">Jennifer Dust</h5>
																<span class="qodef-e-author-job">Olivia Martin -
																	Homeowner</span>
															</div>
														</div>
													</div>
												</div>
											</div>
											<div class="qodef-e swiper-slide elementor-repeater-item-13e9193">
												<div class="qodef-e-inner">
													<div class="qodef-e-content">
														<div class="qodef-e-quote">
															<svg class="qodef-e-quote-icon"
																xmlns="http://www.w3.org/2000/svg" x="0px" y="0px"
																viewBox="0 0 190.5 148" xml:space="preserve">
																<g>
																	<path
																		d="M37.7,146.3L2.1,124.6C19.3,100,28.2,74.1,28.8,46.7V2.3H90v38.8c0,19.3-5,38.8-15.1,58.4C64.9,119,52.5,134.6,37.7,146.3z M133.7,146.3l-35.6-21.7c17.2-24.5,26.2-50.5,26.8-77.9V2.3h61.2v38.8c0,19.3-5,38.8-15.1,58.4C160.9,119,148.5,134.6,133.7,146.3z" />
																</g>
															</svg>
														</div>
														<h3 itemprop="description" class="qodef-e-text">Our café needed
															a refresh, and the new design brought so much warmth and
															character. The process was smooth and collaborative from
															start to finish.</h3>
														<div class="qodef-e-bottom-info">
															<div class="qodef-e-media-image">
																<img loading="lazy" decoding="async" width="150"
																	height="150"
																	src="./wp-content/uploads/sites/2/2023/10/shamim-nakhaei-u6oXAJWx4sE-unsplash-150x150.jpg"
																	class="attachment-thumbnail size-thumbnail" alt=""
																	srcset="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2023/10/shamim-nakhaei-u6oXAJWx4sE-unsplash-150x150.jpg 150w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2023/10/shamim-nakhaei-u6oXAJWx4sE-unsplash-300x300.jpg 300w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2023/10/shamim-nakhaei-u6oXAJWx4sE-unsplash-551x550.jpg 551w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2023/10/shamim-nakhaei-u6oXAJWx4sE-unsplash-550x550.jpg 550w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2023/10/shamim-nakhaei-u6oXAJWx4sE-unsplash-501x500.jpg 501w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2023/10/shamim-nakhaei-u6oXAJWx4sE-unsplash.jpg 609w"
																	sizes="(max-width: 150px) 100vw, 150px" />
															</div>
															<div class="qodef-e-author">
																<h5 class="qodef-e-author-name">Adam Hills</h5>
																<span class="qodef-e-author-job">Daniel Kim - Cafe
																	Owner</span>
															</div>
														</div>
													</div>
												</div>
											</div>
											<div class="qodef-e swiper-slide elementor-repeater-item-3b7b5ef">
												<div class="qodef-e-inner">
													<div class="qodef-e-content">
														<div class="qodef-e-quote">
															<svg class="qodef-e-quote-icon"
																xmlns="http://www.w3.org/2000/svg" x="0px" y="0px"
																viewBox="0 0 190.5 148" xml:space="preserve">
																<g>
																	<path
																		d="M37.7,146.3L2.1,124.6C19.3,100,28.2,74.1,28.8,46.7V2.3H90v38.8c0,19.3-5,38.8-15.1,58.4C64.9,119,52.5,134.6,37.7,146.3z M133.7,146.3l-35.6-21.7c17.2-24.5,26.2-50.5,26.8-77.9V2.3h61.2v38.8c0,19.3-5,38.8-15.1,58.4C160.9,119,148.5,134.6,133.7,146.3z" />
																</g>
															</svg>
														</div>
														<h3 itemprop="description" class="qodef-e-text">It’s amazing how
															much easier it has been to meet new people and create
															instant connections. I have the exact same personality, the
															only thing that has changed is my mindset and a few
															behaviors.</h3>
														<div class="qodef-e-bottom-info">
															<div class="qodef-e-media-image">
																<img loading="lazy" decoding="async" width="150"
																	height="150"
																	src="./wp-content/uploads/sites/2/2023/10/bbh-singapore-gQXcI2ceVJ0-unsplash-150x150.jpg"
																	class="attachment-thumbnail size-thumbnail" alt=""
																	srcset="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2023/10/bbh-singapore-gQXcI2ceVJ0-unsplash-150x150.jpg 150w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2023/10/bbh-singapore-gQXcI2ceVJ0-unsplash-300x300.jpg 300w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2023/10/bbh-singapore-gQXcI2ceVJ0-unsplash-768x768.jpg 768w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2023/10/bbh-singapore-gQXcI2ceVJ0-unsplash-550x550.jpg 550w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2023/10/bbh-singapore-gQXcI2ceVJ0-unsplash-500x500.jpg 500w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2023/10/bbh-singapore-gQXcI2ceVJ0-unsplash-650x650.jpg 650w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2023/10/bbh-singapore-gQXcI2ceVJ0-unsplash.jpg 821w"
																	sizes="(max-width: 150px) 100vw, 150px" />
															</div>
															<div class="qodef-e-author">
																<h5 class="qodef-e-author-name">Jennifer Dust</h5>
																<span class="qodef-e-author-job">HR - Archy CO.</span>
															</div>
														</div>
													</div>
												</div>
											</div>
										</div>
									</div>
									<div class="swiper-button-prev swiper-button-outside swiper-button-prev-1"><svg
											class="qodef-swiper-arrow-left" xmlns="http://www.w3.org/2000/svg" x="0px"
											y="0px" viewBox="0 0 34.2 32.3" xml:space="preserve"
											style="stroke-width: 2;">
											<line x1="0.5" y1="16" x2="33.5" y2="16" />
											<line x1="0.3" y1="16.5" x2="16.2" y2="0.7" />
											<line x1="0" y1="15.4" x2="16.2" y2="31.6" />
										</svg></div>
									<div class="swiper-button-next swiper-button-outside swiper-button-next-1"><svg
											class="qodef-swiper-arrow-right" xmlns="http://www.w3.org/2000/svg" x="0px"
											y="0px" viewBox="0 0 34.2 32.3" xml:space="preserve"
											style="stroke-width: 2;">
											<line x1="0" y1="16" x2="33" y2="16" />
											<line x1="17.3" y1="0.7" x2="33.2" y2="16.5" />
											<line x1="17.3" y1="31.6" x2="33.5" y2="15.4" />
										</svg></div>
									<div class="swiper-pagination qodef-swiper-pagination-outside swiper-pagination-1">
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="elementor-element elementor-element-60d9578e e-flex e-con-boxed e-con e-parent"
				data-id="60d9578e" data-element_type="container"
				data-settings="{&quot;background_background&quot;:&quot;classic&quot;}">
				<div class="e-con-inner">
					<div class="elementor-element elementor-element-557d7ab e-con-full e-flex e-con e-child"
						data-id="557d7ab" data-element_type="container">
						<div class="elementor-element elementor-element-79efc388 elementor-invisible elementor-widget elementor-widget-qi_addons_for_elementor_animated_text"
							data-id="79efc388" data-element_type="widget"
							data-settings="{&quot;_animation&quot;:&quot;fadeIn&quot;}"
							data-widget_type="qi_addons_for_elementor_animated_text.default">
							<div class="elementor-widget-container">
								<div
									class="qodef-shortcode qodef-m qodef-qi-animated-text qodef--animated-by-letter qodef-qi--has-appear qodef--appear-fade">
									<h1 class="qodef-m-title">
										<span class="qodef-e-word-holder">Meet</span> <span
											class="qodef-e-word-holder">Our</span> <span
											class="qodef-e-word-holder">Team</span>
									</h1>
								</div>
							</div>
						</div>
					</div>
					<div class="elementor-element elementor-element-33765da4 e-con-full e-flex e-con e-child"
						data-id="33765da4" data-element_type="container">
						<div class="elementor-element elementor-element-35db96ea elementor-widget elementor-widget-qi_addons_for_elementor_animated_text"
							data-id="35db96ea" data-element_type="widget"
							data-widget_type="qi_addons_for_elementor_animated_text.default">
							<div class="elementor-widget-container">
								<div
									class="qodef-shortcode qodef-m qodef-qi-animated-text qodef--animated-by-letter qodef-qi--has-appear qodef--appear-fade">
									<h3 class="qodef-m-title">
										<span class="qodef-e-word-holder">Team</span>
									</h3>
								</div>
							</div>
						</div>
						<div class="elementor-element elementor-element-36f1fd0 animated-slow elementor-invisible elementor-widget elementor-widget-heading"
							data-id="36f1fd0" data-element_type="widget"
							data-settings="{&quot;_animation&quot;:&quot;fadeIn&quot;}"
							data-widget_type="heading.default">
							<div class="elementor-widget-container">
								<p class="elementor-heading-title elementor-size-default">Every project is personal. As
									a interior designer, We work closely with you from start to finish—your vision, our
									expertise, one beautiful result.</p>
							</div>
						</div>
					</div>
					<div class="elementor-element elementor-element-4bd9b0be e-con-full e-flex e-con e-child"
						data-id="4bd9b0be" data-element_type="container">
						<div class="elementor-element elementor-element-61b8c784 e-con-full e-flex e-con e-child"
							data-id="61b8c784" data-element_type="container">
							<div class="elementor-element elementor-element-23611e8a elementor-widget elementor-widget-qi_addons_for_elementor_banner"
								data-id="23611e8a" data-element_type="widget"
								data-widget_type="qi_addons_for_elementor_banner.default">
								<div class="elementor-widget-container">
									<div
										class="qodef-shortcode qodef-m qodef-qi-banner qodef-layout--standard qodef-vertical--bottom qodef-horizontal--left qodef-image--hover-zoom">
										<div class="qodef-m-image">
											<img loading="lazy" decoding="async" width="800" height="900"
												src="./wp-content/uploads/sites/2/2025/04/t-02.jpg"
												class="attachment-qi_addons_for_elementor_image_size_huge-square size-qi_addons_for_elementor_image_size_huge-square"
												alt=""
												srcset="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-02.jpg 800w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-02-267x300.jpg 267w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-02-768x864.jpg 768w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-02-489x550.jpg 489w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-02-550x619.jpg 550w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-02-444x500.jpg 444w"
												sizes="(max-width: 800px) 100vw, 800px" />
										</div>
										<div class="qodef-m-content">
											<div class="qodef-m-content-inner">
												<h5 class="qodef-m-subtitle">
													Furniture Designer </h5>
												<h3 class="qodef-m-title">
													Oliver James </h3>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="elementor-element elementor-element-605bb77c e-con-full e-flex e-con e-child"
							data-id="605bb77c" data-element_type="container">
							<div class="elementor-element elementor-element-515764a8 elementor-widget elementor-widget-qi_addons_for_elementor_banner"
								data-id="515764a8" data-element_type="widget"
								data-widget_type="qi_addons_for_elementor_banner.default">
								<div class="elementor-widget-container">
									<div
										class="qodef-shortcode qodef-m qodef-qi-banner qodef-layout--standard qodef-vertical--bottom qodef-horizontal--left qodef-image--hover-zoom">
										<div class="qodef-m-image">
											<img loading="lazy" decoding="async" width="800" height="900"
												src="./wp-content/uploads/sites/2/2025/04/t-21.jpg"
												class="attachment-qi_addons_for_elementor_image_size_huge-square size-qi_addons_for_elementor_image_size_huge-square"
												alt=""
												srcset="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-21.jpg 800w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-21-267x300.jpg 267w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-21-768x864.jpg 768w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-21-489x550.jpg 489w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-21-550x619.jpg 550w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-21-444x500.jpg 444w"
												sizes="(max-width: 800px) 100vw, 800px" />
										</div>
										<div class="qodef-m-content">
											<div class="qodef-m-content-inner">
												<h5 class="qodef-m-subtitle">
													Creative Director </h5>
												<h3 class="qodef-m-title">
													Anna Hallberg </h3>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="elementor-element elementor-element-32c22e3c e-con-full e-flex e-con e-child"
							data-id="32c22e3c" data-element_type="container">
							<div class="elementor-element elementor-element-7f908a1b elementor-widget elementor-widget-qi_addons_for_elementor_banner"
								data-id="7f908a1b" data-element_type="widget"
								data-widget_type="qi_addons_for_elementor_banner.default">
								<div class="elementor-widget-container">
									<div
										class="qodef-shortcode qodef-m qodef-qi-banner qodef-layout--standard qodef-vertical--bottom qodef-horizontal--left qodef-image--hover-zoom">
										<div class="qodef-m-image">
											<img loading="lazy" decoding="async" width="800" height="900"
												src="./wp-content/uploads/sites/2/2025/04/t-14.jpg"
												class="attachment-qi_addons_for_elementor_image_size_huge-square size-qi_addons_for_elementor_image_size_huge-square"
												alt=""
												srcset="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-14.jpg 800w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-14-267x300.jpg 267w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-14-768x864.jpg 768w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-14-489x550.jpg 489w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-14-550x619.jpg 550w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-14-444x500.jpg 444w"
												sizes="(max-width: 800px) 100vw, 800px" />
										</div>
										<div class="qodef-m-content">
											<div class="qodef-m-content-inner">
												<h5 class="qodef-m-subtitle">
													Senior Stylist </h5>
												<h3 class="qodef-m-title">
													Henry Patel </h3>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="elementor-element elementor-element-72241dd0 e-con-full e-flex e-con e-child"
							data-id="72241dd0" data-element_type="container">
							<div class="elementor-element elementor-element-62645a79 elementor-widget elementor-widget-qi_addons_for_elementor_banner"
								data-id="62645a79" data-element_type="widget"
								data-widget_type="qi_addons_for_elementor_banner.default">
								<div class="elementor-widget-container">
									<div
										class="qodef-shortcode qodef-m qodef-qi-banner qodef-layout--standard qodef-vertical--bottom qodef-horizontal--left qodef-image--hover-zoom">
										<div class="qodef-m-image">
											<img loading="lazy" decoding="async" width="800" height="900"
												src="./wp-content/uploads/sites/2/2025/04/t-08.jpg"
												class="attachment-qi_addons_for_elementor_image_size_huge-square size-qi_addons_for_elementor_image_size_huge-square"
												alt=""
												srcset="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-08.jpg 800w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-08-267x300.jpg 267w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-08-768x864.jpg 768w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-08-489x550.jpg 489w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-08-550x619.jpg 550w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-08-444x500.jpg 444w"
												sizes="(max-width: 800px) 100vw, 800px" />
										</div>
										<div class="qodef-m-content">
											<div class="qodef-m-content-inner">
												<h5 class="qodef-m-subtitle">
													Creative Director </h5>
												<h3 class="qodef-m-title">
													Sophia Bennett </h3>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<section
				class="elementor-section elementor-top-section elementor-element elementor-element-e19a625 elementor-section-boxed elementor-section-height-default elementor-section-height-default"
				data-id="e19a625" data-element_type="section"
				data-settings="{&quot;background_background&quot;:&quot;classic&quot;}">
				<div class="elementor-background-overlay"></div>
				<div class="elementor-container elementor-column-gap-default">
					<div class="elementor-column elementor-col-100 elementor-top-column elementor-element elementor-element-770ddcd"
						data-id="770ddcd" data-element_type="column">
						<div class="elementor-widget-wrap elementor-element-populated">
							<section
								class="elementor-section elementor-inner-section elementor-element elementor-element-e942d6f elementor-section-boxed elementor-section-height-default elementor-section-height-default"
								data-id="e942d6f" data-element_type="section">
								<div class="elementor-container elementor-column-gap-default">
									<div class="elementor-column elementor-col-50 elementor-inner-column elementor-element elementor-element-70e005d"
										data-id="70e005d" data-element_type="column">
										<div class="elementor-widget-wrap elementor-element-populated">
											<div class="elementor-element elementor-element-31246d3 elementor-widget__width-initial elementor-widget elementor-widget-qi_addons_for_elementor_animated_text"
												data-id="31246d3" data-element_type="widget"
												data-widget_type="qi_addons_for_elementor_animated_text.default">
												<div class="elementor-widget-container">
													<div
														class="qodef-shortcode qodef-m qodef-qi-animated-text qodef--animated-by-letter qodef-qi--has-appear qodef--appear-fade">
														<h3 class="qodef-m-title">
															<span class="qodef-e-word-holder">—</span> <span
																class="qodef-e-word-holder">Collaboration</span>
														</h3>
													</div>
												</div>
											</div>
											<div class="elementor-element elementor-element-5d51dc1 elementor-widget elementor-widget-spacer"
												data-id="5d51dc1" data-element_type="widget"
												data-widget_type="spacer.default">
												<div class="elementor-widget-container">
													<div class="elementor-spacer">
														<div class="elementor-spacer-inner"></div>
													</div>
												</div>
											</div>
										</div>
									</div>
									<div class="elementor-column elementor-col-50 elementor-inner-column elementor-element elementor-element-5bc6764"
										data-id="5bc6764" data-element_type="column">
										<div class="elementor-widget-wrap elementor-element-populated">
											<div class="elementor-element elementor-element-17c6d09 elementor-invisible elementor-widget elementor-widget-qi_addons_for_elementor_animated_text"
												data-id="17c6d09" data-element_type="widget"
												data-settings="{&quot;_animation&quot;:&quot;fadeIn&quot;}"
												data-widget_type="qi_addons_for_elementor_animated_text.default">
												<div class="elementor-widget-container">
													<div
														class="qodef-shortcode qodef-m qodef-qi-animated-text qodef--animated-by-letter qodef-qi--has-appear qodef--appear-fade">
														<h1 class="qodef-m-title">
															<span class="qodef-e-word-holder">Let’s</span> <span
																class="qodef-e-word-holder">Create</span> <span
																class="qodef-e-word-holder">Something</span> <span
																class="qodef-e-word-holder">Beautiful</span> <span
																class="qodef-e-word-holder">Together</span>
														</h1>
													</div>
												</div>
											</div>
										</div>
									</div>
								</div>
							</section>
							<section
								class="elementor-section elementor-inner-section elementor-element elementor-element-cb7c6d6 elementor-section-full_width elementor-section-height-default elementor-section-height-default"
								data-id="cb7c6d6" data-element_type="section">
								<div class="elementor-container elementor-column-gap-default">
									<div class="elementor-column elementor-col-50 elementor-inner-column elementor-element elementor-element-e586641"
										data-id="e586641" data-element_type="column">
										<div class="elementor-widget-wrap elementor-element-populated">
											<div class="elementor-element elementor-element-c79f510 -text-faded elementor-invisible elementor-widget elementor-widget-heading"
												data-id="c79f510" data-element_type="widget"
												data-settings="{&quot;_animation&quot;:&quot;fadeInUp&quot;,&quot;_animation_delay&quot;:400}"
												data-widget_type="heading.default">
												<div class="elementor-widget-container">
													<h3 class="elementor-heading-title elementor-size-default">Ready to
														transform your space? We’d love to hear from you! <u>Get in
															touch</u> today to schedule a consultation and start
														bringing your vision to life.</h3>
												</div>
											</div>
										</div>
									</div>
									<div class="elementor-column elementor-col-50 elementor-inner-column elementor-element elementor-element-9a37161"
										data-id="9a37161" data-element_type="column">
										<div class="elementor-widget-wrap elementor-element-populated">
											<div class="elementor-element elementor-element-da565d3 elementor-invisible elementor-widget elementor-widget-qi_addons_for_elementor_button"
												data-id="da565d3" data-element_type="widget"
												data-settings="{&quot;_animation&quot;:&quot;fadeInUp&quot;,&quot;_animation_delay&quot;:800}"
												data-widget_type="qi_addons_for_elementor_button.default">
												<div class="elementor-widget-container">
													<a class="qodef-shortcode qodef-m qodef-qi-button qodef-html--link qodef-layout--outlined qodef-type--standard qodef-icon--right qodef-hover--icon-move-horizontal-short"
														href="contact.html" target="_self">
														<span class="qodef-m-text">Get In Touch</span>
														<span class="qodef-m-icon">
															<span class="qodef-m-icon-inner">
																<svg xmlns="http://www.w3.org/2000/svg" height="21"
																	viewBox="0 0 21 21" width="21">
																	<g fill="none" fill-rule="evenodd"
																		stroke="currentColor" stroke-linecap="round"
																		stroke-linejoin="round"
																		transform="translate(6 6)">
																		<path d="m8.5 7.5v-7h-7"></path>
																		<path d="m8.5.5-8 8"></path>
																	</g>
																</svg> </span>
														</span>
													</a>
												</div>
											</div>
										</div>
									</div>
								</div>
							</section>
						</div>
					</div>
				</div>
			</section>
		</div>


		<footer id="colophon" class="site-footer" role="contentinfo">
			<div class="site-info">

				<p>© 2025 Motiff Square</p>
			</div> <!-- .site-info -->
		</footer> <!-- #colophon .site-footer -->
	</div>
	<script type="speculationrules">
{"prefetch":[{"source":"document","where":{"and":[{"href_matches":"\/interique\/demo-01\/*"},{"not":{"href_matches":["\/interique\/demo-01\/wp-*.php","\/interique\/demo-01\/wp-admin\/*","\/interique\/demo-01\/wp-content\/uploads\/sites\/2\/*","\/interique\/demo-01\/wp-content\/*","\/interique\/demo-01\/wp-content\/plugins\/*","\/interique\/demo-01\/wp-content\/themes\/interique\/*","\/interique\/demo-01\/*\\?(.+)"]}},{"not":{"selector_matches":"a[rel~=\"nofollow\"]"}},{"not":{"selector_matches":".no-prefetch, .no-prefetch a"}}]},"eagerness":"conservative"}]}
</script>
	<script>
		const lazyloadRunObserver = () => {
			const lazyloadBackgrounds = document.querySelectorAll(`.e-con.e-parent:not(.e-lazyloaded)`);
			const lazyloadBackgroundObserver = new IntersectionObserver((entries) => {
				entries.forEach((entry) => {
					if (entry.isIntersecting) {
						let lazyloadBackground = entry.target;
						if (lazyloadBackground) {
							lazyloadBackground.classList.add('e-lazyloaded');
						}
						lazyloadBackgroundObserver.unobserve(entry.target);
					}
				});
			}, { rootMargin: '200px 0px 200px 0px' });
			lazyloadBackgrounds.forEach((lazyloadBackground) => {
				lazyloadBackgroundObserver.observe(lazyloadBackground);
			});
		};
		const events = [
			'DOMContentLoaded',
			'elementor/lazyload/observe',
		];
		events.forEach((event) => {
			document.addEventListener(event, lazyloadRunObserver);
		});
	</script>
	<script type="text/javascript" src="./wp-includes/js/jquery/ui/core.minb37e.js?ver=1.13.3"
		id="jquery-ui-core-js"></script>
	<script type="text/javascript" id="qi-addons-for-elementor-script-js-extra">
		/* <![CDATA[ */
		var qodefQiAddonsGlobal = { "vars": { "adminBarHeight": 0, "iconArrowLeft": "<svg  xmlns=\"http:\/\/www.w3.org\/2000\/svg\" x=\"0px\" y=\"0px\" viewBox=\"0 0 34.2 32.3\" xml:space=\"preserve\" style=\"stroke-width: 2;\"><line x1=\"0.5\" y1=\"16\" x2=\"33.5\" y2=\"16\"\/><line x1=\"0.3\" y1=\"16.5\" x2=\"16.2\" y2=\"0.7\"\/><line x1=\"0\" y1=\"15.4\" x2=\"16.2\" y2=\"31.6\"\/><\/svg>", "iconArrowRight": "<svg  xmlns=\"http:\/\/www.w3.org\/2000\/svg\" x=\"0px\" y=\"0px\" viewBox=\"0 0 34.2 32.3\" xml:space=\"preserve\" style=\"stroke-width: 2;\"><line x1=\"0\" y1=\"16\" x2=\"33\" y2=\"16\"\/><line x1=\"17.3\" y1=\"0.7\" x2=\"33.2\" y2=\"16.5\"\/><line x1=\"17.3\" y1=\"31.6\" x2=\"33.5\" y2=\"15.4\"\/><\/svg>", "iconClose": "<svg  xmlns=\"http:\/\/www.w3.org\/2000\/svg\" x=\"0px\" y=\"0px\" viewBox=\"0 0 9.1 9.1\" xml:space=\"preserve\"><g><path d=\"M8.5,0L9,0.6L5.1,4.5L9,8.5L8.5,9L4.5,5.1L0.6,9L0,8.5L4,4.5L0,0.6L0.6,0L4.5,4L8.5,0z\"\/><\/g><\/svg>" } };
		/* ]]> */
	</script>
	<script type="text/javascript"
		src="./wp-content/plugins/qi-addons-for-elementor/assets/js/main.minf5a9.js?ver=1.8.9"
		id="qi-addons-for-elementor-script-js"></script>
	<script type="text/javascript" src="./wp-content/themes/interique/js/jquery.fitvids.js" id="fitvids-js"></script>
	<script type="text/javascript" src="./wp-content/themes/interique/js/jarallax.min.js" id="jarallax-js"></script>
	<script type="text/javascript" src="./wp-content/themes/interique/js/jarallax-video.min.js"
		id="jarallax-video-js"></script>
	<script type="text/javascript" src="./wp-content/themes/interique/js/fluidbox/jquery.fluidbox.min.js"
		id="fluidbox-js"></script>
	<script type="text/javascript" src="./wp-content/themes/interique/js/jquery-validation/jquery.validate.js"
		id="jqueryvalidation-js"></script>
	<script type="text/javascript" id="jqueryvalidation-js-after">
		/* <![CDATA[ */
		(function ($) {
			"use strict";
			$.extend($.validator.messages, {
				required: "This field is required.",
				remote: "Please fix this field.",
				email: "Please enter a valid email address.",
				url: "Please enter a valid URL.",
				date: "Please enter a valid date.",
				dateISO: "Please enter a valid date ( ISO ).",
				number: "Please enter a valid number.",
				digits: "Please enter only digits.",
				equalTo: "Please enter the same value again.",
				maxlength: $.validator.format("Please enter no more than {0} characters."),
				minlength: $.validator.format("Please enter at least {0} characters."),
				rangelength: $.validator.format("Please enter a value between {0} and {1} characters long."),
				range: $.validator.format("Please enter a value between {0} and {1}."),
				max: $.validator.format("Please enter a value less than or equal to {0}."),
				min: $.validator.format("Please enter a value greater than or equal to {0}."),
				step: $.validator.format("Please enter a multiple of {0}.")
			});
		})(jQuery);
		/* ]]> */
	</script>
	<script type="text/javascript"
		src="./wp-content/plugins/qi-addons-for-elementor/inc/masonry/assets/js/plugins/isotope.pkgd.min7c45.js?ver=3.0.6"
		id="isotope-js"></script>
	<script type="text/javascript"
		src="./wp-content/themes/interique/js/jquery.magnific-popup/jquery.magnific-popup.min.js"
		id="magnific-popup-js"></script>
	<script type="text/javascript" src="./wp-content/themes/interique/js/owl-carousel/owl.carousel.min.js"
		id="owl-carousel-js"></script>
	<script type="text/javascript" src="./wp-includes/js/imagesloaded.minbb93.js?ver=5.0.0"
		id="imagesloaded-js"></script>
	<script type="text/javascript" src="./wp-content/themes/interique/js/jquery.collagePlus.min.js"
		id="collagePlus-js"></script>
	<script type="text/javascript" src="./wp-content/themes/interique/js/jquery.fittext.js" id="fittext-js"></script>
	<script type="text/javascript" src="./wp-content/themes/interique/js/resize-sensor.js"
		id="resize-sensor-js"></script>
	<script type="text/javascript" src="./wp-content/themes/interique/js/jquery.sticky-sidebar.min.js"
		id="sticky-sidebar-js"></script>
	<script type="text/javascript" src="./wp-content/themes/interique/js/main.js" id="interique-main-js"></script>
	<script type="text/javascript" src="./wp-content/plugins/pixelwars-core/themes/global/js/shortcodes.js"
		id="pixelwars-core-shortcodes-js"></script>
	<script type="text/javascript" src="./wp-content/plugins/elementor/assets/js/webpack.runtime.min0d09.js?ver=3.29.0"
		id="elementor-webpack-runtime-js"></script>
	<script type="text/javascript" src="./wp-content/plugins/elementor/assets/js/frontend-modules.min0d09.js?ver=3.29.0"
		id="elementor-frontend-modules-js"></script>
	<script type="text/javascript" id="elementor-frontend-js-before">
		/* <![CDATA[ */
		var elementorFrontendConfig = { "environmentMode": { "edit": false, "wpPreview": false, "isScriptDebug": false }, "i18n": { "shareOnFacebook": "Share on Facebook", "shareOnTwitter": "Share on Twitter", "pinIt": "Pin it", "download": "Download", "downloadImage": "Download image", "fullscreen": "Fullscreen", "zoom": "Zoom", "share": "Share", "playVideo": "Play Video", "previous": "Previous", "next": "Next", "close": "Close", "a11yCarouselPrevSlideMessage": "Previous slide", "a11yCarouselNextSlideMessage": "Next slide", "a11yCarouselFirstSlideMessage": "This is the first slide", "a11yCarouselLastSlideMessage": "This is the last slide", "a11yCarouselPaginationBulletMessage": "Go to slide" }, "is_rtl": false, "breakpoints": { "xs": 0, "sm": 480, "md": 768, "lg": 1025, "xl": 1440, "xxl": 1600 }, "responsive": { "breakpoints": { "mobile": { "label": "Mobile Portrait", "value": 767, "default_value": 767, "direction": "max", "is_enabled": true }, "mobile_extra": { "label": "Mobile Landscape", "value": 880, "default_value": 880, "direction": "max", "is_enabled": false }, "tablet": { "label": "Tablet Portrait", "value": 1024, "default_value": 1024, "direction": "max", "is_enabled": true }, "tablet_extra": { "label": "Tablet Landscape", "value": 1200, "default_value": 1200, "direction": "max", "is_enabled": false }, "laptop": { "label": "Laptop", "value": 1366, "default_value": 1366, "direction": "max", "is_enabled": false }, "widescreen": { "label": "Widescreen", "value": 2400, "default_value": 2400, "direction": "min", "is_enabled": false } }, "hasCustomBreakpoints": false }, "version": "3.29.0", "is_static": false, "experimentalFeatures": { "e_font_icon_svg": true, "additional_custom_breakpoints": true, "container": true, "e_local_google_fonts": true, "nested-elements": true, "e_element_cache": true, "home_screen": true, "launchpad-checklist": true, "cloud-library": true, "e_opt_in_v4_page": true }, "urls": { "assets": "https:\/\/themes.pixelwars.org\/interique\/demo-01\/wp-content\/plugins\/elementor\/assets\/", "ajaxurl": "https:\/\/themes.pixelwars.org\/interique\/demo-01\/wp-admin\/admin-ajax.php", "uploadUrl": "https:\/\/themes.pixelwars.org\/interique\/demo-01\/wp-content\/uploads\/sites\/2" }, "nonces": { "floatingButtonsClickTracking": "5ab3af599e" }, "swiperClass": "swiper", "settings": { "page": [], "editorPreferences": [] }, "kit": { "active_breakpoints": ["viewport_mobile", "viewport_tablet"], "global_image_lightbox": "yes", "lightbox_enable_counter": "yes", "lightbox_enable_fullscreen": "yes", "lightbox_enable_zoom": "yes", "lightbox_enable_share": "yes", "lightbox_title_src": "title", "lightbox_description_src": "description" }, "post": { "id": 17837, "title": "Home%2003%20%E2%80%93%20Interiqu%C3%A9", "excerpt": "", "featuredImage": false } };
		/* ]]> */
	</script>
	<script type="text/javascript" src="./wp-content/plugins/elementor/assets/js/frontend.min0d09.js?ver=3.29.0"
		id="elementor-frontend-js"></script>
	<script type="text/javascript" src="./wp-includes/js/jquery/ui/tabs.minb37e.js?ver=1.13.3"
		id="jquery-ui-tabs-js"></script>
	<script type="text/javascript"
		src="./wp-content/plugins/qi-addons-for-elementor/inc/shortcodes/progress-bar-circle/assets/js/plugins/progressbar.min68b3.js?ver=1"
		id="progress-bar-js"></script>
	<script type="text/javascript"
		src="./wp-content/plugins/qi-addons-for-elementor/assets/plugins/fslightbox/fslightbox.min0899.js?ver=6.8.1"
		id="fslightbox-js"></script>
	<script type="text/javascript"
		src="./wp-content/plugins/qi-addons-for-elementor/inc/masonry/assets/js/plugins/packery-mode.pkgd.min7406.js?ver=2.0.1"
		id="packery-js"></script>
	<script type="text/javascript" id="mediaelement-core-js-before">
		/* <![CDATA[ */
		var mejsL10n = { "language": "en", "strings": { "mejs.download-file": "Download File", "mejs.install-flash": "You are using a browser that does not have Flash player enabled or installed. Please turn on your Flash player plugin or download the latest version from https:\/\/get.adobe.com\/flashplayer\/", "mejs.fullscreen": "Fullscreen", "mejs.play": "Play", "mejs.pause": "Pause", "mejs.time-slider": "Time Slider", "mejs.time-help-text": "Use Left\/Right Arrow keys to advance one second, Up\/Down arrows to advance ten seconds.", "mejs.live-broadcast": "Live Broadcast", "mejs.volume-help-text": "Use Up\/Down Arrow keys to increase or decrease volume.", "mejs.unmute": "Unmute", "mejs.mute": "Mute", "mejs.volume-slider": "Volume Slider", "mejs.video-player": "Video Player", "mejs.audio-player": "Audio Player", "mejs.captions-subtitles": "Captions\/Subtitles", "mejs.captions-chapters": "Chapters", "mejs.none": "None", "mejs.afrikaans": "Afrikaans", "mejs.albanian": "Albanian", "mejs.arabic": "Arabic", "mejs.belarusian": "Belarusian", "mejs.bulgarian": "Bulgarian", "mejs.catalan": "Catalan", "mejs.chinese": "Chinese", "mejs.chinese-simplified": "Chinese (Simplified)", "mejs.chinese-traditional": "Chinese (Traditional)", "mejs.croatian": "Croatian", "mejs.czech": "Czech", "mejs.danish": "Danish", "mejs.dutch": "Dutch", "mejs.english": "English", "mejs.estonian": "Estonian", "mejs.filipino": "Filipino", "mejs.finnish": "Finnish", "mejs.french": "French", "mejs.galician": "Galician", "mejs.german": "German", "mejs.greek": "Greek", "mejs.haitian-creole": "Haitian Creole", "mejs.hebrew": "Hebrew", "mejs.hindi": "Hindi", "mejs.hungarian": "Hungarian", "mejs.icelandic": "Icelandic", "mejs.indonesian": "Indonesian", "mejs.irish": "Irish", "mejs.italian": "Italian", "mejs.japanese": "Japanese", "mejs.korean": "Korean", "mejs.latvian": "Latvian", "mejs.lithuanian": "Lithuanian", "mejs.macedonian": "Macedonian", "mejs.malay": "Malay", "mejs.maltese": "Maltese", "mejs.norwegian": "Norwegian", "mejs.persian": "Persian", "mejs.polish": "Polish", "mejs.portuguese": "Portuguese", "mejs.romanian": "Romanian", "mejs.russian": "Russian", "mejs.serbian": "Serbian", "mejs.slovak": "Slovak", "mejs.slovenian": "Slovenian", "mejs.spanish": "Spanish", "mejs.swahili": "Swahili", "mejs.swedish": "Swedish", "mejs.tagalog": "Tagalog", "mejs.thai": "Thai", "mejs.turkish": "Turkish", "mejs.ukrainian": "Ukrainian", "mejs.vietnamese": "Vietnamese", "mejs.welsh": "Welsh", "mejs.yiddish": "Yiddish" } };
		/* ]]> */
	</script>
	<script type="text/javascript" src="./wp-includes/js/mediaelement/mediaelement-and-player.min1f61.js?ver=4.2.17"
		id="mediaelement-core-js"></script>
	<script type="text/javascript" src="./wp-includes/js/mediaelement/mediaelement-migrate.min0899.js?ver=6.8.1"
		id="mediaelement-migrate-js"></script>
	<script type="text/javascript" id="mediaelement-js-extra">
		/* <![CDATA[ */
		var _wpmejsSettings = { "pluginPath": "\/interique\/demo-01\/wp-includes\/js\/mediaelement\/", "classPrefix": "mejs-", "stretching": "responsive", "audioShortcodeLibrary": "mediaelement", "videoShortcodeLibrary": "mediaelement" };
		/* ]]> */
	</script>
	<script type="text/javascript" src="./wp-includes/js/mediaelement/wp-mediaelement.min0899.js?ver=6.8.1"
		id="wp-mediaelement-js"></script>
	<script type="text/javascript" src="./wp-includes/js/mediaelement/renderers/vimeo.min1f61.js?ver=4.2.17"
		id="mediaelement-vimeo-js"></script>
	<script type="text/javascript"
		src="./wp-content/plugins/qi-addons-for-elementor/assets/plugins/swiper/8.4.5/swiper.min94a4.js?ver=8.4.5"
		id="swiper-js"></script>
	<script type="text/javascript"
		src="./wp-content/plugins/bdthemes-prime-slider-lite/assets/js/prime-slider-site.minca7c.js?ver=3.17.12"
		id="prime-slider-site-js"></script>
	<script type="text/javascript" src="./wp-includes/js/dist/hooks.min4fdd.js?ver=4d63a3d491d11ffd8ac6"
		id="wp-hooks-js"></script>
	<script type="text/javascript" src="./wp-includes/js/dist/i18n.minc33c.js?ver=5e580eb46a90c2b997e6"
		id="wp-i18n-js"></script>
	<script type="text/javascript" id="wp-i18n-js-after">
		/* <![CDATA[ */
		wp.i18n.setLocaleData({ 'text direction\u0004ltr': ['ltr'] });
		/* ]]> */
	</script>
	<script type="text/javascript"
		src="./wp-content/plugins/qi-addons-for-elementor/inc/plugins/elementor/assets/js/elementor0899.js?ver=6.8.1"
		id="qi-addons-for-elementor-elementor-js"></script>
</body>

<!-- Mirrored from themes.pixelwars.org/interique/demo-01/home-03/ by HTTrack Website Copier/3.x [XR&CO'2014], Mon, 23 Jun 2025 18:43:03 GMT -->

</html>