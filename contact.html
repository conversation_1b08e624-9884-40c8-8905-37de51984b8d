<!doctype html>
<html lang="en-US"
    class="no-featured-area is-header-small is-body-full-width is-single-post-title-default is-post-title-align-center is-post-media-fixed is-blog-text-align-left is-meta-with-icons is-header-light is-header-full-width is-header-parallax-no is-menu-sticky is-menu-fixed-width is-menu-align-center is-menu-light is-submenu-light is-submenu-align-left is-menu-uppercase is-featured-area-full is-slider-buttons-center-margin is-slider-buttons-rounded is-slider-buttons-dark is-slider-title-label is-slider-parallax is-slider-title-none-uppercase is-slider-more-link-show-on-hover is-slider-more-link-border-bottom is-slider-text-align-center is-slider-v-align-center is-slider-h-align-center is-link-box-title-default is-link-box-title-transform-none is-link-box-text-align-center is-link-box-v-align-center is-link-box-parallax is-intro-align-center is-intro-text-dark is-intro-parallax-no is-more-link-border-bottom-light is-about-author-minimal is-related-posts-parallax is-related-posts-fixed is-share-links-boxed is-tagcloud-minimal is-nav-single-rounded is-nav-single-no-animated is-comments-minimal is-comments-image-rounded is-comment-form-boxed is-comment-form-border is-sidebar-right is-sidebar-sticky is-sidebar-align-left is-widget-title-align-left is-widget-bottomline is-trending-posts-default is-footer-subscribe-light is-footer-widgets-align-left is-footer-full-width is-meta-uppercase is-slider-dots-rounded-line-grow is-site-title-uppercase is-top-bar-mobile-left-visible is-top-bar-uppercase is-top-bar-full is-sub-menu-ani-fade-in-left is-menu-hover-badge is-menu-hover-badge-round is-copyright-uppercase is-logo-bg-stretch-left is-header-sticky-shadow-soft-shorter is-header-transparent-border-bottom is-header-bg-blur-slightly is-footer-border-top is-footer-border-light"
    data-title-ratio="0.7" data-link-box-title-ratio="0.5" data-generic-button-style="" data-header-bg-shape="">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="profile" href="http://gmpg.org/xfn/11">
    <title>Contact &#8211; Motiff Square</title>
    <meta name='robots' content='max-image-preview:large' />
    <style>
        img:is([sizes="auto" i], [sizes^="auto," i]) {
            contain-intrinsic-size: 3000px 1500px
        }
    </style>
    <link rel='dns-prefetch' href='//fonts.googleapis.com' />
    <link rel="alternate" type="application/rss+xml" title="Motiff Square &raquo; Feed"
        href="https://themes.pixelwars.org/interique/demo-01/feed/" />
    <link rel="alternate" type="application/rss+xml" title="Motiff Square &raquo; Comments Feed"
        href="https://themes.pixelwars.org/interique/demo-01/comments/feed/" />
    <script type="text/javascript">
        /* <![CDATA[ */
        window._wpemojiSettings = { "baseUrl": "https:\/\/s.w.org\/images\/core\/emoji\/15.1.0\/72x72\/", "ext": ".png", "svgUrl": "https:\/\/s.w.org\/images\/core\/emoji\/15.1.0\/svg\/", "svgExt": ".svg", "source": { "concatemoji": "https:\/\/themes.pixelwars.org\/interique\/demo-01\/wp-includes\/js\/wp-emoji-release.min.js?ver=6.8.1" } };
        /*! This file is auto-generated */
        !function (i, n) { var o, s, e; function c(e) { try { var t = { supportTests: e, timestamp: (new Date).valueOf() }; sessionStorage.setItem(o, JSON.stringify(t)) } catch (e) { } } function p(e, t, n) { e.clearRect(0, 0, e.canvas.width, e.canvas.height), e.fillText(t, 0, 0); var t = new Uint32Array(e.getImageData(0, 0, e.canvas.width, e.canvas.height).data), r = (e.clearRect(0, 0, e.canvas.width, e.canvas.height), e.fillText(n, 0, 0), new Uint32Array(e.getImageData(0, 0, e.canvas.width, e.canvas.height).data)); return t.every(function (e, t) { return e === r[t] }) } function u(e, t, n) { switch (t) { case "flag": return n(e, "\ud83c\udff3\ufe0f\u200d\u26a7\ufe0f", "\ud83c\udff3\ufe0f\u200b\u26a7\ufe0f") ? !1 : !n(e, "\ud83c\uddfa\ud83c\uddf3", "\ud83c\uddfa\u200b\ud83c\uddf3") && !n(e, "\ud83c\udff4\udb40\udc67\udb40\udc62\udb40\udc65\udb40\udc6e\udb40\udc67\udb40\udc7f", "\ud83c\udff4\u200b\udb40\udc67\u200b\udb40\udc62\u200b\udb40\udc65\u200b\udb40\udc6e\u200b\udb40\udc67\u200b\udb40\udc7f"); case "emoji": return !n(e, "\ud83d\udc26\u200d\ud83d\udd25", "\ud83d\udc26\u200b\ud83d\udd25") }return !1 } function f(e, t, n) { var r = "undefined" != typeof WorkerGlobalScope && self instanceof WorkerGlobalScope ? new OffscreenCanvas(300, 150) : i.createElement("canvas"), a = r.getContext("2d", { willReadFrequently: !0 }), o = (a.textBaseline = "top", a.font = "600 32px Arial", {}); return e.forEach(function (e) { o[e] = t(a, e, n) }), o } function t(e) { var t = i.createElement("script"); t.src = e, t.defer = !0, i.head.appendChild(t) } "undefined" != typeof Promise && (o = "wpEmojiSettingsSupports", s = ["flag", "emoji"], n.supports = { everything: !0, everythingExceptFlag: !0 }, e = new Promise(function (e) { i.addEventListener("DOMContentLoaded", e, { once: !0 }) }), new Promise(function (t) { var n = function () { try { var e = JSON.parse(sessionStorage.getItem(o)); if ("object" == typeof e && "number" == typeof e.timestamp && (new Date).valueOf() < e.timestamp + 604800 && "object" == typeof e.supportTests) return e.supportTests } catch (e) { } return null }(); if (!n) { if ("undefined" != typeof Worker && "undefined" != typeof OffscreenCanvas && "undefined" != typeof URL && URL.createObjectURL && "undefined" != typeof Blob) try { var e = "postMessage(" + f.toString() + "(" + [JSON.stringify(s), u.toString(), p.toString()].join(",") + "));", r = new Blob([e], { type: "text/javascript" }), a = new Worker(URL.createObjectURL(r), { name: "wpTestEmojiSupports" }); return void (a.onmessage = function (e) { c(n = e.data), a.terminate(), t(n) }) } catch (e) { } c(n = f(s, u, p)) } t(n) }).then(function (e) { for (var t in e) n.supports[t] = e[t], n.supports.everything = n.supports.everything && n.supports[t], "flag" !== t && (n.supports.everythingExceptFlag = n.supports.everythingExceptFlag && n.supports[t]); n.supports.everythingExceptFlag = n.supports.everythingExceptFlag && !n.supports.flag, n.DOMReady = !1, n.readyCallback = function () { n.DOMReady = !0 } }).then(function () { return e }).then(function () { var e; n.supports.everything || (n.readyCallback(), (e = n.source || {}).concatemoji ? t(e.concatemoji) : e.wpemoji && e.twemoji && (t(e.twemoji), t(e.wpemoji))) })) }((window, document), window._wpemojiSettings);
        /* ]]> */
    </script>
    <style id='wp-emoji-styles-inline-css' type='text/css'>
        img.wp-smiley,
        img.emoji {
            display: inline !important;
            border: none !important;
            box-shadow: none !important;
            height: 1em !important;
            width: 1em !important;
            margin: 0 0.07em !important;
            vertical-align: -0.1em !important;
            background: none !important;
            padding: 0 !important;
        }
    </style>
    <style id='classic-theme-styles-inline-css' type='text/css'>
        /*! This file is auto-generated */
        .wp-block-button__link {
            color: #fff;
            background-color: #32373c;
            border-radius: 9999px;
            box-shadow: none;
            text-decoration: none;
            padding: calc(.667em + 2px) calc(1.333em + 2px);
            font-size: 1.125em
        }

        .wp-block-file__button {
            background: #32373c;
            color: #fff;
            text-decoration: none
        }
    </style>
    <style id='global-styles-inline-css' type='text/css'>
        :root {
            --wp--preset--aspect-ratio--square: 1;
            --wp--preset--aspect-ratio--4-3: 4/3;
            --wp--preset--aspect-ratio--3-4: 3/4;
            --wp--preset--aspect-ratio--3-2: 3/2;
            --wp--preset--aspect-ratio--2-3: 2/3;
            --wp--preset--aspect-ratio--16-9: 16/9;
            --wp--preset--aspect-ratio--9-16: 9/16;
            --wp--preset--color--black: #000000;
            --wp--preset--color--cyan-bluish-gray: #abb8c3;
            --wp--preset--color--white: #ffffff;
            --wp--preset--color--pale-pink: #f78da7;
            --wp--preset--color--vivid-red: #cf2e2e;
            --wp--preset--color--luminous-vivid-orange: #ff6900;
            --wp--preset--color--luminous-vivid-amber: #fcb900;
            --wp--preset--color--light-green-cyan: #7bdcb5;
            --wp--preset--color--vivid-green-cyan: #00d084;
            --wp--preset--color--pale-cyan-blue: #8ed1fc;
            --wp--preset--color--vivid-cyan-blue: #0693e3;
            --wp--preset--color--vivid-purple: #9b51e0;
            --wp--preset--gradient--vivid-cyan-blue-to-vivid-purple: linear-gradient(135deg, rgba(6, 147, 227, 1) 0%, rgb(155, 81, 224) 100%);
            --wp--preset--gradient--light-green-cyan-to-vivid-green-cyan: linear-gradient(135deg, rgb(122, 220, 180) 0%, rgb(0, 208, 130) 100%);
            --wp--preset--gradient--luminous-vivid-amber-to-luminous-vivid-orange: linear-gradient(135deg, rgba(252, 185, 0, 1) 0%, rgba(255, 105, 0, 1) 100%);
            --wp--preset--gradient--luminous-vivid-orange-to-vivid-red: linear-gradient(135deg, rgba(255, 105, 0, 1) 0%, rgb(207, 46, 46) 100%);
            --wp--preset--gradient--very-light-gray-to-cyan-bluish-gray: linear-gradient(135deg, rgb(238, 238, 238) 0%, rgb(169, 184, 195) 100%);
            --wp--preset--gradient--cool-to-warm-spectrum: linear-gradient(135deg, rgb(74, 234, 220) 0%, rgb(151, 120, 209) 20%, rgb(207, 42, 186) 40%, rgb(238, 44, 130) 60%, rgb(251, 105, 98) 80%, rgb(254, 248, 76) 100%);
            --wp--preset--gradient--blush-light-purple: linear-gradient(135deg, rgb(255, 206, 236) 0%, rgb(152, 150, 240) 100%);
            --wp--preset--gradient--blush-bordeaux: linear-gradient(135deg, rgb(254, 205, 165) 0%, rgb(254, 45, 45) 50%, rgb(107, 0, 62) 100%);
            --wp--preset--gradient--luminous-dusk: linear-gradient(135deg, rgb(255, 203, 112) 0%, rgb(199, 81, 192) 50%, rgb(65, 88, 208) 100%);
            --wp--preset--gradient--pale-ocean: linear-gradient(135deg, rgb(255, 245, 203) 0%, rgb(182, 227, 212) 50%, rgb(51, 167, 181) 100%);
            --wp--preset--gradient--electric-grass: linear-gradient(135deg, rgb(202, 248, 128) 0%, rgb(113, 206, 126) 100%);
            --wp--preset--gradient--midnight: linear-gradient(135deg, rgb(2, 3, 129) 0%, rgb(40, 116, 252) 100%);
            --wp--preset--font-size--small: 13px;
            --wp--preset--font-size--medium: 20px;
            --wp--preset--font-size--large: 36px;
            --wp--preset--font-size--x-large: 42px;
            --wp--preset--spacing--20: 0.44rem;
            --wp--preset--spacing--30: 0.67rem;
            --wp--preset--spacing--40: 1rem;
            --wp--preset--spacing--50: 1.5rem;
            --wp--preset--spacing--60: 2.25rem;
            --wp--preset--spacing--70: 3.38rem;
            --wp--preset--spacing--80: 5.06rem;
            --wp--preset--shadow--natural: 6px 6px 9px rgba(0, 0, 0, 0.2);
            --wp--preset--shadow--deep: 12px 12px 50px rgba(0, 0, 0, 0.4);
            --wp--preset--shadow--sharp: 6px 6px 0px rgba(0, 0, 0, 0.2);
            --wp--preset--shadow--outlined: 6px 6px 0px -3px rgba(255, 255, 255, 1), 6px 6px rgba(0, 0, 0, 1);
            --wp--preset--shadow--crisp: 6px 6px 0px rgba(0, 0, 0, 1);
        }

        :where(.is-layout-flex) {
            gap: 0.5em;
        }

        :where(.is-layout-grid) {
            gap: 0.5em;
        }

        body .is-layout-flex {
            display: flex;
        }

        .is-layout-flex {
            flex-wrap: wrap;
            align-items: center;
        }

        .is-layout-flex> :is(*, div) {
            margin: 0;
        }

        body .is-layout-grid {
            display: grid;
        }

        .is-layout-grid> :is(*, div) {
            margin: 0;
        }

        :where(.wp-block-columns.is-layout-flex) {
            gap: 2em;
        }

        :where(.wp-block-columns.is-layout-grid) {
            gap: 2em;
        }

        :where(.wp-block-post-template.is-layout-flex) {
            gap: 1.25em;
        }

        :where(.wp-block-post-template.is-layout-grid) {
            gap: 1.25em;
        }

        .has-black-color {
            color: var(--wp--preset--color--black) !important;
        }

        .has-cyan-bluish-gray-color {
            color: var(--wp--preset--color--cyan-bluish-gray) !important;
        }

        .has-white-color {
            color: var(--wp--preset--color--white) !important;
        }

        .has-pale-pink-color {
            color: var(--wp--preset--color--pale-pink) !important;
        }

        .has-vivid-red-color {
            color: var(--wp--preset--color--vivid-red) !important;
        }

        .has-luminous-vivid-orange-color {
            color: var(--wp--preset--color--luminous-vivid-orange) !important;
        }

        .has-luminous-vivid-amber-color {
            color: var(--wp--preset--color--luminous-vivid-amber) !important;
        }

        .has-light-green-cyan-color {
            color: var(--wp--preset--color--light-green-cyan) !important;
        }

        .has-vivid-green-cyan-color {
            color: var(--wp--preset--color--vivid-green-cyan) !important;
        }

        .has-pale-cyan-blue-color {
            color: var(--wp--preset--color--pale-cyan-blue) !important;
        }

        .has-vivid-cyan-blue-color {
            color: var(--wp--preset--color--vivid-cyan-blue) !important;
        }

        .has-vivid-purple-color {
            color: var(--wp--preset--color--vivid-purple) !important;
        }

        .has-black-background-color {
            background-color: var(--wp--preset--color--black) !important;
        }

        .has-cyan-bluish-gray-background-color {
            background-color: var(--wp--preset--color--cyan-bluish-gray) !important;
        }

        .has-white-background-color {
            background-color: var(--wp--preset--color--white) !important;
        }

        .has-pale-pink-background-color {
            background-color: var(--wp--preset--color--pale-pink) !important;
        }

        .has-vivid-red-background-color {
            background-color: var(--wp--preset--color--vivid-red) !important;
        }

        .has-luminous-vivid-orange-background-color {
            background-color: var(--wp--preset--color--luminous-vivid-orange) !important;
        }

        .has-luminous-vivid-amber-background-color {
            background-color: var(--wp--preset--color--luminous-vivid-amber) !important;
        }

        .has-light-green-cyan-background-color {
            background-color: var(--wp--preset--color--light-green-cyan) !important;
        }

        .has-vivid-green-cyan-background-color {
            background-color: var(--wp--preset--color--vivid-green-cyan) !important;
        }

        .has-pale-cyan-blue-background-color {
            background-color: var(--wp--preset--color--pale-cyan-blue) !important;
        }

        .has-vivid-cyan-blue-background-color {
            background-color: var(--wp--preset--color--vivid-cyan-blue) !important;
        }

        .has-vivid-purple-background-color {
            background-color: var(--wp--preset--color--vivid-purple) !important;
        }

        .has-black-border-color {
            border-color: var(--wp--preset--color--black) !important;
        }

        .has-cyan-bluish-gray-border-color {
            border-color: var(--wp--preset--color--cyan-bluish-gray) !important;
        }

        .has-white-border-color {
            border-color: var(--wp--preset--color--white) !important;
        }

        .has-pale-pink-border-color {
            border-color: var(--wp--preset--color--pale-pink) !important;
        }

        .has-vivid-red-border-color {
            border-color: var(--wp--preset--color--vivid-red) !important;
        }

        .has-luminous-vivid-orange-border-color {
            border-color: var(--wp--preset--color--luminous-vivid-orange) !important;
        }

        .has-luminous-vivid-amber-border-color {
            border-color: var(--wp--preset--color--luminous-vivid-amber) !important;
        }

        .has-light-green-cyan-border-color {
            border-color: var(--wp--preset--color--light-green-cyan) !important;
        }

        .has-vivid-green-cyan-border-color {
            border-color: var(--wp--preset--color--vivid-green-cyan) !important;
        }

        .has-pale-cyan-blue-border-color {
            border-color: var(--wp--preset--color--pale-cyan-blue) !important;
        }

        .has-vivid-cyan-blue-border-color {
            border-color: var(--wp--preset--color--vivid-cyan-blue) !important;
        }

        .has-vivid-purple-border-color {
            border-color: var(--wp--preset--color--vivid-purple) !important;
        }

        .has-vivid-cyan-blue-to-vivid-purple-gradient-background {
            background: var(--wp--preset--gradient--vivid-cyan-blue-to-vivid-purple) !important;
        }

        .has-light-green-cyan-to-vivid-green-cyan-gradient-background {
            background: var(--wp--preset--gradient--light-green-cyan-to-vivid-green-cyan) !important;
        }

        .has-luminous-vivid-amber-to-luminous-vivid-orange-gradient-background {
            background: var(--wp--preset--gradient--luminous-vivid-amber-to-luminous-vivid-orange) !important;
        }

        .has-luminous-vivid-orange-to-vivid-red-gradient-background {
            background: var(--wp--preset--gradient--luminous-vivid-orange-to-vivid-red) !important;
        }

        .has-very-light-gray-to-cyan-bluish-gray-gradient-background {
            background: var(--wp--preset--gradient--very-light-gray-to-cyan-bluish-gray) !important;
        }

        .has-cool-to-warm-spectrum-gradient-background {
            background: var(--wp--preset--gradient--cool-to-warm-spectrum) !important;
        }

        .has-blush-light-purple-gradient-background {
            background: var(--wp--preset--gradient--blush-light-purple) !important;
        }

        .has-blush-bordeaux-gradient-background {
            background: var(--wp--preset--gradient--blush-bordeaux) !important;
        }

        .has-luminous-dusk-gradient-background {
            background: var(--wp--preset--gradient--luminous-dusk) !important;
        }

        .has-pale-ocean-gradient-background {
            background: var(--wp--preset--gradient--pale-ocean) !important;
        }

        .has-electric-grass-gradient-background {
            background: var(--wp--preset--gradient--electric-grass) !important;
        }

        .has-midnight-gradient-background {
            background: var(--wp--preset--gradient--midnight) !important;
        }

        .has-small-font-size {
            font-size: var(--wp--preset--font-size--small) !important;
        }

        .has-medium-font-size {
            font-size: var(--wp--preset--font-size--medium) !important;
        }

        .has-large-font-size {
            font-size: var(--wp--preset--font-size--large) !important;
        }

        .has-x-large-font-size {
            font-size: var(--wp--preset--font-size--x-large) !important;
        }

        :where(.wp-block-post-template.is-layout-flex) {
            gap: 1.25em;
        }

        :where(.wp-block-post-template.is-layout-grid) {
            gap: 1.25em;
        }

        :where(.wp-block-columns.is-layout-flex) {
            gap: 2em;
        }

        :where(.wp-block-columns.is-layout-grid) {
            gap: 2em;
        }

        :root :where(.wp-block-pullquote) {
            font-size: 1.5em;
            line-height: 1.6;
        }
    </style>
    <link rel='stylesheet' id='fontello-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/pixelwars-core/themes/global/css/fonts/fontello/css/fontello.css'
        type='text/css' media='all' />
    <link rel='stylesheet' id='pixelwars-core-shortcodes-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/pixelwars-core/themes/global/css/shortcodes.css'
        type='text/css' media='all' />
    <link rel='stylesheet' id='qi-addons-for-elementor-grid-style-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/qi-addons-for-elementor/assets/css/grid.min.css?ver=1.8.9'
        type='text/css' media='all' />
    <link rel='stylesheet' id='qi-addons-for-elementor-helper-parts-style-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/qi-addons-for-elementor/assets/css/helper-parts.min.css?ver=1.8.9'
        type='text/css' media='all' />
    <link rel='stylesheet' id='qi-addons-for-elementor-style-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/qi-addons-for-elementor/assets/css/main.min.css?ver=1.8.9'
        type='text/css' media='all' />
    <link rel='stylesheet' id='interique-font-texgyreadventor-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/css/fonts/texgyreadventor/stylesheet.css?ver=6.8.1'
        type='text/css' media='all' />
    <link rel='stylesheet' id='interique-font-now-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/css/fonts/now/stylesheet.css?ver=6.8.1'
        type='text/css' media='all' />
    <link rel='stylesheet' id='interique-fonts-css'
        href='//fonts.googleapis.com/css?family=Jost%3A100%2C100i%2C200%2C200i%2C300%2C300i%2C400%2C400i%2C500%2C500i%2C600%2C600i%2C700%2C700i%2C800%2C800i%2C900%2C900i%7CBricolage+Grotesque%3A100%2C100i%2C200%2C200i%2C300%2C300i%2C400%2C400i%2C500%2C500i%2C600%2C600i%2C700%2C700i%2C800%2C800i%2C900%2C900i&#038;ver=6.8.1'
        type='text/css' media='all' />
    <link rel='stylesheet' id='normalize-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/css/normalize.css?ver=6.8.1'
        type='text/css' media='all' />
    <link rel='stylesheet' id='bootstrap-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/css/bootstrap.css?ver=6.8.1'
        type='text/css' media='all' />
    <link rel='stylesheet' id='fluidbox-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/js/fluidbox/fluidbox.css?ver=6.8.1'
        type='text/css' media='all' />
    <link rel='stylesheet' id='magnific-popup-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/js/jquery.magnific-popup/magnific-popup.css?ver=6.8.1'
        type='text/css' media='all' />
    <link rel='stylesheet' id='owl-carousel-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/js/owl-carousel/owl.carousel.css?ver=6.8.1'
        type='text/css' media='all' />
    <link rel='stylesheet' id='interique-main-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/css/main.css?ver=6.8.1'
        type='text/css' media='all' />
    <link rel='stylesheet' id='interique-768-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/css/768.css?ver=6.8.1'
        type='text/css' media='all' />
    <link rel='stylesheet' id='interique-992-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/css/992.css?ver=6.8.1'
        type='text/css' media='all' />
    <link rel='stylesheet' id='interique-style-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/style.css?ver=6.8.1'
        type='text/css' media='all' />
    <style id='interique-style-inline-css' type='text/css'>
        .site-title {
            font-family: 'TeXGyreAdventor', sans-serif;
        }

        .nav-menu,
        .entry-meta,
        .owl-nav,
        label,
        .page-links,
        .navigation,
        .entry-title i,
        .site-info,
        .filters {
            font-family: 'Jost';
        }

        .widget-title {
            font-family: 'TeXGyreAdventor', sans-serif;
        }

        h1,
        .entry-title,
        .footer-subscribe h3,
        .widget_categories ul li,
        .widget_recent_entries ul li a,
        .widget_pages ul li,
        .widget_nav_menu ul li,
        .widget_archive ul li,
        .widget_most_recommended_posts ul li a,
        .widget_calendar table caption,
        .tptn_title,
        .nav-single a,
        .widget_recent_comments ul li,
        .widget_product_categories ul li,
        .widget_meta ul li,
        .widget_rss ul a.rsswidget {
            font-family: 'Bricolage Grotesque';
        }

        h2,
        h3,
        h4,
        h5,
        h6,
        blockquote,
        .tab-titles {
            font-family: 'Jost';
        }

        .slider-box .entry-title {
            font-family: 'Now', sans-serif;
        }

        body {
            font-family: 'Jost';
        }

        .link-box .entry-title {
            font-family: 'Now', sans-serif;
        }

        .button,
        button,
        html .elementor-button,
        html .ekit-wid-con .elementskit-btn,
        html .ekit-wid-con .ekit_creative_button,
        .more-link {
            font-family: 'Jost';
        }

        .top-bar {
            font-family: 'Jost';
        }

        @media screen and (min-width: 992px) {
            .site-header .site-title {
                font-size: 24px;
            }
        }

        @media screen and (min-width: 992px) {
            .is-header-smaller .site-header.clone .site-title {
                font-size: 24px;
            }
        }

        @media screen and (max-width: 991px) {
            .site-header .site-title {
                font-size: 14px;
            }
        }

        @media screen and (min-width: 992px) {
            .blog-small .entry-title {
                font-size: 24px;
            }
        }

        @media screen and (min-width: 992px) {
            h1 {
                font-size: 102px;
            }
        }

        @media screen and (min-width: 992px) {
            html {
                font-size: 16px;
            }
        }

        @media screen and (max-width: 991px) {
            html {
                font-size: 15px;
            }
        }

        @media screen and (min-width: 992px) {
            .nav-menu>ul {
                font-size: 13px;
            }
        }

        @media screen and (min-width: 992px) {
            .blog-stream .entry-content {
                font-size: 16px;
            }
        }

        @media screen and (min-width: 992px) {
            .blog-stream.blog-small .entry-content {
                font-size: 15px;
            }
        }

        .widget-title {
            font-size: 13px;
        }

        @media screen and (min-width: 992px) {
            .nav-menu ul ul {
                font-size: 12px;
            }
        }

        .top-bar {
            font-size: 12px;
        }

        .site-footer .site-info {
            font-size: 12px;
        }

        .site-title {
            font-weight: 400;
        }

        h1,
        .entry-title,
        .footer-subscribe h3 {
            font-weight: 500;
        }

        h2,
        h3,
        h4,
        h5,
        h6,
        blockquote,
        .comment-meta .fn {
            font-weight: 500;
        }

        .slider-box .entry-title {
            font-weight: 700;
        }

        .widget-title {
            font-weight: 700;
        }

        @media screen and (min-width: 992px) {
            .nav-menu>ul {
                font-weight: 500;
            }
        }

        @media screen and (min-width: 992px) {
            .nav-menu ul ul {
                font-weight: 400;
            }
        }

        .link-box .entry-title {
            font-weight: 700;
        }

        .site-description {
            font-weight: 400;
        }

        .top-bar {
            font-weight: 500;
        }

        .site-footer .site-info {
            font-weight: 500;
        }

        .entry-meta {
            font-weight: 500;
        }

        @media screen and (min-width: 992px) {
            .nav-menu>ul {
                letter-spacing: 0px;
            }
        }

        @media screen and (min-width: 992px) {
            .nav-menu ul ul {
                letter-spacing: 0px;
            }
        }

        .widget-title {
            letter-spacing: 3px;
        }

        .site-footer .site-info {
            letter-spacing: 3px;
        }

        h1,
        .entry-title,
        .footer-subscribe h3,
        .widget_categories ul li,
        .widget_recent_entries ul li,
        .widget_pages ul li,
        .widget_archive ul li,
        .widget_calendar table caption,
        .tptn_title,
        .nav-single a {
            text-transform: none;
        }

        h2,
        h3,
        h4,
        h5,
        h6,
        blockquote,
        .comment-meta .fn {
            text-transform: none;
        }

        @media screen and (min-width: 992px) {
            html {
                line-height: 1.6;
            }
        }

        @media screen and (min-width: 992px) {
            .header-bg-shape {
                height: 50px;
            }
        }

        .header-wrap:after {
            bottom: -1px;
        }

        @media screen and (min-width: 992px) {
            .site-title img {
                max-height: 44px;
            }
        }

        @media screen and (max-width: 991px) {
            .site-title img {
                max-height: 24px;
            }
        }

        .top-bar {
            line-height: 36px;
        }

        html .site-header .site-title a {
            padding: 12px 24px;
        }

        @media screen and (min-width: 992px) {
            .site {
                margin-top: 0px;
                margin-bottom: 0px;
            }
        }

        .layout-medium,
        .is-header-row .header-wrap-inner,
        .is-header-small .header-wrap-inner,
        .is-menu-bar.is-menu-fixed-bg .menu-wrap,
        .is-header-fixed-width .header-wrap,
        .is-header-fixed-width.is-menu-bar .site-navigation,
        .is-header-float-box:not(.is-header-float-box-menu) .site-header:not(.clone) .header-wrap,
        .is-header-float-box.is-menu-bar .site-header:not(.clone) .site-navigation:not(.clone),
        .is-body-boxed .site,
        .is-body-boxed .header-wrap,
        .is-body-boxed.is-menu-bar .site-navigation,
        .is-body-boxed:not(.is-menu-bar) .site-header,
        .is-middle-boxed .site-main,
        .intro-content,
        .is-footer-boxed .site-footer,
        .is-content-boxed .site-main .layout-fixed,
        .top-bar .top-bar-wrap,
        .is-top-bar-fixed .top-bar,
        .is-top-bar-fixed-bg .top-bar,
        .is-menu-bottom.is-menu-bottom-overflow .site-header:not(.clone) .site-navigation:not(.clone) .menu-wrap,
        .site-branding-wrap,
        .is-header-border-fixed .header-wrap:after,
        .is-header-border-fixed .menu-wrap:after,
        html .tutor-container,
        html .lp-content-area,
        html .learn-press-breadcrumb {
            max-width: 1140px;
            margin-left: auto;
            margin-right: auto;
        }

        .layout-fixed,
        .blog-list,
        .blog-regular,
        .is-content-boxed .single .site-content,
        .is-content-boxed .page .site-content {
            max-width: 800px;
        }

        @media screen and (min-width: 992px) {

            .is-header-small .header-wrap,
            .is-menu-bar .nav-menu>ul>li,
            .is-header-vertical .nav-menu>ul>li {
                line-height: 80px;
            }

            .is-header-small .site-branding {
                max-height: 80px;
            }

        }

        @media screen and (min-width: 992px) {

            .is-header-small.is-header-smaller .site-header.clone .header-wrap,
            .is-header-row.is-header-smaller .site-header.clone .nav-menu>ul>li,
            .is-menu-bar.is-header-smaller .site-navigation.clone .nav-menu>ul>li,
            .is-menu-bar.is-header-smaller .site-header.clone .site-navigation .nav-menu>ul>li {
                line-height: 70px;
            }

            .is-header-small.is-header-smaller .site-header.clone .site-branding {
                max-height: 70px;
            }

        }

        .button.is-primary,
        .button.is-primary:after,
        html .elementor-button,
        .elementor-button.elementor-size-xs,
        .elementor-button.elementor-size-sm,
        .elementor-button.elementor-size-md,
        .elementor-button.elementor-size-lg,
        .elementor-button.elementor-size-xl,
        html .ekit-wid-con .elementskit-btn,
        html .ekit-wid-con .ekit_creative_button {
            border-radius: 0px;
        }

        .button.is-secondary,
        .button.is-secondary:after,
        .elementor-element.elementor-button-info .elementor-button {
            border-radius: 30px;
        }

        a {
            color: #d84156;
        }

        a:hover {
            color: #c60035;
        }

        .site-header .header-wrap {
            background-color: #fffefc;
        }

        html:not(.is-menu-bottom) .site-header .header-bg-shape {
            color: #fffefc;
        }

        .header-wrap:before {
            background: #fffefc;
        }

        .header-wrap:before {
            opacity: 0;
        }

        .is-header-half-transparent:not(.is-menu-toggled-on) .site-header:not(.clone) .header-wrap:before {
            opacity: 0.45;
        }

        .header-wrap:after {
            opacity: 0.08;
        }

        .site-header .menu-wrap {
            background-color: #ffffff;
        }

        html.is-menu-bottom .site-header .header-bg-shape {
            color: #ffffff;
        }

        @media screen and (min-width: 992px) {
            .nav-menu>ul>li.current-menu-item>a {
                color: #0a0a0a !important;
            }
        }

        @media screen and (min-width: 992px) {
            .nav-menu li.current-menu-item>a .link-text:before {
                background-color: #bbcfbd !important;
                border-color: #bbcfbd !important;
            }
        }

        @media screen and (min-width: 992px) {

            html .nav-menu>ul>li>a:hover,
            .nav-menu>ul>li.has-submenu:hover>a {
                color: #1c1a17;
            }
        }

        @media screen and (min-width: 992px) {

            html.loaded .nav-menu ul li a .link-text:before,
            .nav-menu li.has-submenu:hover>a .link-text:before {
                background-color: #d6cfc2;
                border-color: #d6cfc2;
            }
        }

        @media screen and (min-width: 992px) {
            .nav-menu ul ul li.current-menu-item>a .link-text {
                color: #0a0606 !important;
            }
        }

        @media screen and (min-width: 992px) {

            html .nav-menu ul ul li a:hover .link-text,
            .nav-menu ul ul li.has-submenu:hover>a .link-text {
                color: #111111;
            }
        }

        h1,
        h2,
        h3,
        h4,
        h5,
        h6,
        blockquote,
        .tab-titles {
            color: #020014;
        }

        body {
            color: #32455e;
        }

        body {
            background: #fffefc;
        }

        .site .footer-subscribe {
            background: #ebe6da;
        }

        .site-footer .site-info {
            background-color: #ffffff;
        }

        .site-footer .site-info {
            color: #0a0a0a;
        }

        .button.is-primary {
            color: #222222;
        }

        .button.is-primary.is-shadow,
        .button.is-primary.is-solid,
        .button.is-primary.is-solid-light,
        html .elementor-button,
        html .ekit-wid-con .elementskit-btn {
            background-color: #222222;
        }

        .button.is-primary.is-shadow {
            box-shadow: 0px 18px 23px -6px #222222;
        }

        .button.is-primary:hover {
            color: #215aed;
        }

        .button.is-primary.is-shadow:hover,
        .button.is-primary.is-solid:hover,
        .button.is-primary.is-solid-light:hover,
        .button.is-primary.is-shift:after,
        .button.is-primary.is-circle:before,
        html .elementor-button:hover,
        html .ekit-wid-con .elementskit-btn:hover {
            background-color: #215aed;
        }

        .button.is-primary.is-shadow:hover {
            box-shadow: 0px 2px 10px -5px #215aed;
        }

        .button.is-secondary {
            color: #f22000;
        }

        .button.is-secondary.is-shadow,
        .button.is-secondary.is-solid,
        .button.is-secondary.is-solid-light,
        .elementor-element.elementor-button-info .elementor-button {
            background-color: #f22000;
        }

        .button.is-secondary.is-shadow {
            box-shadow: 0px 18px 23px -6px #f22000;
        }

        .button.is-secondary:hover {
            color: #0026ff;
        }

        .button.is-secondary.is-shadow:hover,
        .button.is-secondary.is-solid:hover,
        .button.is-secondary.is-solid-light:hover,
        .button.is-secondary.is-shift:after,
        .button.is-secondary.is-circle:before,
        .elementor-element.elementor-button-info .elementor-button:hover {
            background-color: #0026ff;
        }

        .button.is-secondary.is-shadow:hover {
            box-shadow: 0px 2px 10px -5px #0026ff;
        }

        .top-bar,
        .top-bar select option {
            background-color: #262251;
        }

        .top-bar {
            background: linear-gradient(90deg, #262251 30%, #d10600 100%);
        }

        .header-wrap:after {
            color: #222222;
        }

        .is-menu-bar .menu-wrap:after {
            color: #222222;
        }
    </style>
    <link rel='stylesheet' id='swiper-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/qi-addons-for-elementor/assets/plugins/swiper/8.4.5/swiper.min.css?ver=8.4.5'
        type='text/css' media='all' />
    <link rel='stylesheet' id='elementor-frontend-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/elementor/assets/css/frontend.min.css?ver=3.29.0'
        type='text/css' media='all' />
    <link rel='stylesheet' id='elementor-post-6-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/elementor/css/post-6.css?ver=1748102204'
        type='text/css' media='all' />
    <link rel='stylesheet' id='e-animation-fadeIn-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/elementor/assets/lib/animations/styles/fadeIn.min.css?ver=3.29.0'
        type='text/css' media='all' />
    <link rel='stylesheet' id='widget-heading-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/elementor/assets/css/widget-heading.min.css?ver=3.29.0'
        type='text/css' media='all' />
    <link rel='stylesheet' id='widget-spacer-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/elementor/assets/css/widget-spacer.min.css?ver=3.29.0'
        type='text/css' media='all' />
    <link rel='stylesheet' id='widget-social-icons-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/elementor/assets/css/widget-social-icons.min.css?ver=3.29.0'
        type='text/css' media='all' />
    <link rel='stylesheet' id='e-apple-webkit-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/elementor/assets/css/conditionals/apple-webkit.min.css?ver=3.29.0'
        type='text/css' media='all' />
    <link rel='stylesheet' id='elementor-post-17820-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/elementor/css/post-17820.css?ver=1748200128'
        type='text/css' media='all' />
    <link rel='stylesheet' id='bdt-uikit-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/bdthemes-prime-slider-lite/assets/css/bdt-uikit.css?ver=3.21.7'
        type='text/css' media='all' />
    <link rel='stylesheet' id='prime-slider-site-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/bdthemes-prime-slider-lite/assets/css/prime-slider-site.css?ver=3.17.12'
        type='text/css' media='all' />
    <link rel='stylesheet' id='elementor-gf-local-roboto-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/elementor/google-fonts/css/roboto.css?ver=1745821354'
        type='text/css' media='all' />
    <link rel='stylesheet' id='elementor-gf-local-robotoslab-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/elementor/google-fonts/css/robotoslab.css?ver=1745821356'
        type='text/css' media='all' />
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-includes/js/jquery/jquery.min.js?ver=3.7.1"
        id="jquery-core-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-includes/js/jquery/jquery-migrate.min.js?ver=3.4.1"
        id="jquery-migrate-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/bdthemes-prime-slider-lite/assets/js/bdt-uikit.min.js?ver=3.21.7"
        id="bdt-uikit-js"></script>
    <link rel="https://api.w.org/" href="https://themes.pixelwars.org/interique/demo-01/wp-json/" />
    <link rel="alternate" title="JSON" type="application/json"
        href="https://themes.pixelwars.org/interique/demo-01/wp-json/wp/v2/pages/17820" />
    <link rel="EditURI" type="application/rsd+xml" title="RSD"
        href="https://themes.pixelwars.org/interique/demo-01/xmlrpc.php?rsd" />
    <meta name="generator" content="WordPress 6.8.1" />
    <link rel="canonical" href="contact.html" />
    <link rel='shortlink' href='https://themes.pixelwars.org/interique/demo-01/?p=17820' />
    <link rel="alternate" title="oEmbed (JSON)" type="application/json+oembed"
        href="https://themes.pixelwars.org/interique/demo-01/wp-json/oembed/1.0/embed?url=https%3A%2F%2Fthemes.pixelwars.org%2Finterique%2Fdemo-01%2Fcontact%2F" />
    <link rel="alternate" title="oEmbed (XML)" type="text/xml+oembed"
        href="https://themes.pixelwars.org/interique/demo-01/wp-json/oembed/1.0/embed?url=https%3A%2F%2Fthemes.pixelwars.org%2Finterique%2Fdemo-01%2Fcontact%2F&#038;format=xml" />
    <meta name="generator"
        content="Elementor 3.29.0; features: e_font_icon_svg, additional_custom_breakpoints, e_local_google_fonts, e_element_cache; settings: css_print_method-external, google_font-enabled, font_display-swap">
    <style>
        .e-con.e-parent:nth-of-type(n+4):not(.e-lazyloaded):not(.e-no-lazyload),
        .e-con.e-parent:nth-of-type(n+4):not(.e-lazyloaded):not(.e-no-lazyload) * {
            background-image: none !important;
        }

        @media screen and (max-height: 1024px) {

            .e-con.e-parent:nth-of-type(n+3):not(.e-lazyloaded):not(.e-no-lazyload),
            .e-con.e-parent:nth-of-type(n+3):not(.e-lazyloaded):not(.e-no-lazyload) * {
                background-image: none !important;
            }
        }

        @media screen and (max-height: 640px) {

            .e-con.e-parent:nth-of-type(n+2):not(.e-lazyloaded):not(.e-no-lazyload),
            .e-con.e-parent:nth-of-type(n+2):not(.e-lazyloaded):not(.e-no-lazyload) * {
                background-image: none !important;
            }
        }
    </style>
</head>

<body
    class="wp-singular page-template page-template-elementor_header_footer page page-id-17820 wp-theme-interique qodef-qi--no-touch qi-addons-for-elementor-1.8.9 elementor-default elementor-template-full-width elementor-kit-6 elementor-page elementor-page-17820">
    <div id="page" class="hfeed site">
        <header id="masthead" class="site-header" role="banner">
            <div class="header-wrap" data-parallax-video="">
                <div class="header-wrap-inner">
                    <div class="site-branding">
                        <div class="site-branding-wrap">
                            <div class="site-branding-left">
                            </div> <!-- .site-branding-left -->

                            <div class="site-branding-center">
                                <h1 class="site-title">
                                    <a href="index.html" rel="home">
                                        <span class="screen-reader-text">
                                            Motiff Square </span>
                                        <span class="site-title-text">
                                            Motiff Square </span>
                                    </a>
                                </h1> <!-- .site-title -->
                                <p class="site-description">
                                </p> <!-- .site-description -->
                            </div> <!-- .site-branding-center -->
                            <div class="site-branding-right">
                            </div> <!-- .site-branding-right -->
                        </div> <!-- .site-branding-wrap -->
                    </div> <!-- .site-branding -->

                    <nav id="site-navigation" class="main-navigation site-navigation" role="navigation">
                        <div class="menu-wrap">
                            <div class="layout-medium">
                                <a class="menu-toggle">
                                    <span class="lines"></span>
                                </a> <!-- .menu-toggle -->
                                <div class="nav-menu">
                                    <ul id="menu-mymenu" class="">
                                        <li id="menu-item-15712"
                                            class="menu-item menu-item-type-post_type menu-item-object-page menu-item-home menu-item-has-children menu-item-15712">
                                            <a href="index.html">Home</a>
                                            <!-- <ul class="sub-menu">
                                                <li id="menu-item-18584"
                                                    class="menu-item menu-item-type-post_type menu-item-object-page menu-item-home menu-item-18584">
                                                    <a href="index.html">Home
                                                        01</a></li>
                                                <li id="menu-item-18345"
                                                    class="menu-item menu-item-type-post_type menu-item-object-page menu-item-18345">
                                                    <a href="https://themes.pixelwars.org/interique/demo-01/home-02/">Home
                                                        02</a></li>
                                                <li id="menu-item-18342"
                                                    class="menu-item menu-item-type-post_type menu-item-object-page menu-item-18342">
                                                    <a href="https://themes.pixelwars.org/interique/demo-01/home-03/">Home
                                                        03</a></li>
                                                <li id="menu-item-18343"
                                                    class="menu-item menu-item-type-post_type menu-item-object-page menu-item-18343">
                                                    <a href="https://themes.pixelwars.org/interique/demo-01/home-04/">Home
                                                        04</a></li>
                                                <li id="menu-item-18344"
                                                    class="menu-item menu-item-type-post_type menu-item-object-page menu-item-18344">
                                                    <a href="https://themes.pixelwars.org/interique/demo-01/home-05/">Home
                                                        05</a></li>
                                            </ul> -->
                                        </li>
                                        <li id="menu-item-18246"
                                            class="menu-item menu-item-type-post_type menu-item-object-page menu-item-18246">
                                            <a href="about.html">About Us</a></li>
                                        <li id="menu-item-18245"
                                            class="menu-item menu-item-type-post_type menu-item-object-page menu-item-18245">
                                            <a
                                                href="service.html">Services</a>
                                        </li>
                                        <li id="menu-item-15714"
                                            class="menu-item menu-item-type-post_type menu-item-object-page menu-item-15714">
                                            <a
                                                href="portfolio.html">Portfolio</a>
                                        </li>
                                        <li id="menu-item-18244"
                                            class="menu-item menu-item-type-post_type menu-item-object-page current-menu-item page_item page-item-17820 current_page_item menu-item-18244">
                                            <a href="contact.html"
                                                aria-current="page">Contact</a></li>
                                    </ul>
                                </div> <a class="search-toggle toggle-link"></a>

                                <div class="search-container">
                                    <div class="search-box">
                                        <form class="search-form" method="get"
                                            action="index.html">
                                            <label>
                                                <span>
                                                    Search for </span>
                                                <input type="search" id="search-field" name="s"
                                                    placeholder="type and hit enter">
                                            </label>
                                            <input type="submit" class="search-submit" value="Search">
                                        </form> <!-- .search-form -->
                                    </div> <!-- .search-box -->
                                </div> <!-- .search-container -->
                                <div class="social-container widget-area">
                                    <a class="social-link instagram" target="_blank" href="#"></a>
                                    <a class="social-link twitter" target="_blank" href="#"></a>
                                </div> <!-- .social-container -->
                            </div> <!-- .layout-medium -->
                        </div> <!-- .menu-wrap -->
                    </nav> <!-- #site-navigation .main-navigation .site-navigation -->
                </div> <!-- .header-wrap-inner -->
            </div> <!-- .header-wrap -->
        </header> <!-- #masthead .site-header -->
        <div data-elementor-type="wp-page" data-elementor-id="17820" class="elementor elementor-17820">
            <div class="elementor-element elementor-element-1e0fe808 e-flex e-con-boxed e-con e-parent"
                data-id="1e0fe808" data-element_type="container"
                data-settings="{&quot;background_background&quot;:&quot;classic&quot;}">
                <div class="e-con-inner">
                    <div class="elementor-element elementor-element-1051bd88 e-con-full e-flex e-con e-child"
                        data-id="1051bd88" data-element_type="container"
                        data-settings="{&quot;background_background&quot;:&quot;classic&quot;}">
                    </div>
                </div>
            </div>
            <section
                class="elementor-section elementor-top-section elementor-element elementor-element-25536463 elementor-section-boxed elementor-section-height-default elementor-section-height-default"
                data-id="25536463" data-element_type="section"
                data-settings="{&quot;background_background&quot;:&quot;classic&quot;}">
                <div class="elementor-container elementor-column-gap-default">
                    <div class="elementor-column elementor-col-50 elementor-top-column elementor-element elementor-element-465220c6"
                        data-id="465220c6" data-element_type="column">
                        <div class="elementor-widget-wrap elementor-element-populated">
                            <div class="elementor-element elementor-element-2ac89737 elementor-widget elementor-widget-qi_addons_for_elementor_animated_text"
                                data-id="2ac89737" data-element_type="widget"
                                data-widget_type="qi_addons_for_elementor_animated_text.default">
                                <div class="elementor-widget-container">
                                    <div
                                        class="qodef-shortcode qodef-m qodef-qi-animated-text qodef--animated-by-letter qodef-qi--has-appear qodef--appear-from-right">
                                        <h1 class="qodef-m-title">
                                            <span class="qodef-e-word-holder">Let's</span> <span
                                                class="qodef-e-word-holder">Talk</span>
                                        </h1>
                                    </div>
                                </div>
                            </div>
                            <div class="elementor-element elementor-element-1b8f7af1 animated-slow elementor-invisible elementor-widget elementor-widget-heading"
                                data-id="1b8f7af1" data-element_type="widget"
                                data-settings="{&quot;_animation&quot;:&quot;fadeIn&quot;}"
                                data-widget_type="heading.default">
                                <div class="elementor-widget-container">
                                    <h2 class="elementor-heading-title elementor-size-default">Let’s start transforming
                                        your space—reach out and bring your vision to life today.</h2>
                                </div>
                            </div>
                            <div class="elementor-element elementor-element-6dfe56b4 elementor-widget elementor-widget-spacer"
                                data-id="6dfe56b4" data-element_type="widget" data-widget_type="spacer.default">
                                <div class="elementor-widget-container">
                                    <div class="elementor-spacer">
                                        <div class="elementor-spacer-inner"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="elementor-element elementor-element-d6da896 elementor-widget elementor-widget-qi_addons_for_elementor_animated_text"
                                data-id="d6da896" data-element_type="widget"
                                data-widget_type="qi_addons_for_elementor_animated_text.default">
                                <div class="elementor-widget-container">
                                    <div
                                        class="qodef-shortcode qodef-m qodef-qi-animated-text qodef--animated-by-letter qodef-qi--has-appear qodef--appear-fade">
                                        <h3 class="qodef-m-title">
                                            <span class="qodef-e-word-holder">Call</span> <span
                                                class="qodef-e-word-holder">Us</span>
                                        </h3>
                                    </div>
                                </div>
                            </div>
                            <div class="elementor-element elementor-element-2ea52000 elementor-widget elementor-widget-heading"
                                data-id="2ea52000" data-element_type="widget" data-widget_type="heading.default">
                                <div class="elementor-widget-container">
                                    <h2 class="elementor-heading-title elementor-size-default"><a
                                            href="tel:+12%209887%20234%20199">+12 9887 234 199</a></h2>
                                </div>
                            </div>
                            <div class="elementor-element elementor-element-53d7d94f elementor-shape-circle e-grid-align-left elementor-grid-0 elementor-widget elementor-widget-social-icons"
                                data-id="53d7d94f" data-element_type="widget" data-widget_type="social-icons.default">
                                <div class="elementor-widget-container">
                                    <div class="elementor-social-icons-wrapper elementor-grid" role="list">
                                        <span class="elementor-grid-item" role="listitem">
                                            <a class="elementor-icon elementor-social-icon elementor-social-icon-x-twitter elementor-repeater-item-469030c"
                                                href="#" target="_blank">
                                                <span class="elementor-screen-only">X-twitter</span>
                                                <svg class="e-font-icon-svg e-fab-x-twitter" viewBox="0 0 512 512"
                                                    xmlns="http://www.w3.org/2000/svg">
                                                    <path
                                                        d="M389.2 48h70.6L305.6 224.2 487 464H345L233.7 318.6 106.5 464H35.8L200.7 275.5 26.8 48H172.4L272.9 180.9 389.2 48zM364.4 421.8h39.1L151.1 88h-42L364.4 421.8z">
                                                    </path>
                                                </svg> </a>
                                        </span>
                                        <span class="elementor-grid-item" role="listitem">
                                            <a class="elementor-icon elementor-social-icon elementor-social-icon-youtube elementor-repeater-item-0c5b5a6"
                                                href="#" target="_blank">
                                                <span class="elementor-screen-only">Youtube</span>
                                                <svg class="e-font-icon-svg e-fab-youtube" viewBox="0 0 576 512"
                                                    xmlns="http://www.w3.org/2000/svg">
                                                    <path
                                                        d="M549.655 124.083c-6.281-23.65-24.787-42.276-48.284-48.597C458.781 64 288 64 288 64S117.22 64 74.629 75.486c-23.497 6.322-42.003 24.947-48.284 48.597-11.412 42.867-11.412 132.305-11.412 132.305s0 89.438 11.412 132.305c6.281 23.65 24.787 41.5 48.284 47.821C117.22 448 288 448 288 448s170.78 0 213.371-11.486c23.497-6.321 42.003-24.171 48.284-47.821 11.412-42.867 11.412-132.305 11.412-132.305s0-89.438-11.412-132.305zm-317.51 213.508V175.185l142.739 81.205-142.739 81.201z">
                                                    </path>
                                                </svg> </a>
                                        </span>
                                        <span class="elementor-grid-item" role="listitem">
                                            <a class="elementor-icon elementor-social-icon elementor-social-icon-instagram elementor-repeater-item-bcc75f6"
                                                href="#" target="_blank">
                                                <span class="elementor-screen-only">Instagram</span>
                                                <svg class="e-font-icon-svg e-fab-instagram" viewBox="0 0 448 512"
                                                    xmlns="http://www.w3.org/2000/svg">
                                                    <path
                                                        d="M224.1 141c-63.6 0-114.9 51.3-114.9 114.9s51.3 114.9 114.9 114.9S339 319.5 339 255.9 287.7 141 224.1 141zm0 189.6c-41.1 0-74.7-33.5-74.7-74.7s33.5-74.7 74.7-74.7 74.7 33.5 74.7 74.7-33.6 74.7-74.7 74.7zm146.4-194.3c0 14.9-12 26.8-26.8 26.8-14.9 0-26.8-12-26.8-26.8s12-26.8 26.8-26.8 26.8 12 26.8 26.8zm76.1 27.2c-1.7-35.9-9.9-67.7-36.2-93.9-26.2-26.2-58-34.4-93.9-36.2-37-2.1-147.9-2.1-184.9 0-35.8 1.7-67.6 9.9-93.9 36.1s-34.4 58-36.2 93.9c-2.1 37-2.1 147.9 0 184.9 1.7 35.9 9.9 67.7 36.2 93.9s58 34.4 93.9 36.2c37 2.1 147.9 2.1 184.9 0 35.9-1.7 67.7-9.9 93.9-36.2 26.2-26.2 34.4-58 36.2-93.9 2.1-37 2.1-147.8 0-184.8zM398.8 388c-7.8 19.6-22.9 34.7-42.6 42.6-29.5 11.7-99.5 9-132.1 9s-102.7 2.6-132.1-9c-19.6-7.8-34.7-22.9-42.6-42.6-11.7-29.5-9-99.5-9-132.1s-2.6-102.7 9-132.1c7.8-19.6 22.9-34.7 42.6-42.6 29.5-11.7 99.5-9 132.1-9s102.7-2.6 132.1 9c19.6 7.8 34.7 22.9 42.6 42.6 11.7 29.5 9 99.5 9 132.1s2.7 102.7-9 132.1z">
                                                    </path>
                                                </svg> </a>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="elementor-column elementor-col-50 elementor-top-column elementor-element elementor-element-11d647ff"
                        data-id="11d647ff" data-element_type="column">
                        <div class="elementor-widget-wrap elementor-element-populated">
                            <div class="elementor-element elementor-element-5ae052db elementor-widget elementor-widget-qi_addons_for_elementor_wp_forms"
                                data-id="5ae052db" data-element_type="widget"
                                data-widget_type="qi_addons_for_elementor_wp_forms.default">
                                <div class="elementor-widget-container">
                                    <div class="qodef-shortcode qodef-m qodef-qi-wp-forms">
                                        <div class="wpforms-container wpforms-container-full wpforms-render-modern"
                                            id="wpforms-18258">
                                            <form id="wpforms-form-18258"
                                                class="wpforms-validate wpforms-form wpforms-ajax-form"
                                                data-formid="18258" method="post" enctype="multipart/form-data"
                                                action="/interique/demo-01/contact/"
                                                data-token="c24e63a1c257a61d4f8ecb37e31c1c62"
                                                data-token-time="1750755889"><noscript
                                                    class="wpforms-error-noscript">Please enable JavaScript in your
                                                    browser to complete this form.</noscript>
                                                <div class="wpforms-hidden" id="wpforms-error-noscript">Please enable
                                                    JavaScript in your browser to complete this form.</div>
                                                <div class="wpforms-field-container">
                                                    <div id="wpforms-18258-field_3-container"
                                                        class="wpforms-field wpforms-field-text" data-field-id="3">
                                                        <label class="wpforms-field-label wpforms-label-hide"
                                                            for="wpforms-18258-field_3" aria-hidden="false">Name <span
                                                                class="wpforms-required-label"
                                                                aria-hidden="true">*</span></label><input type="text"
                                                            id="wpforms-18258-field_3"
                                                            class="wpforms-field-large wpforms-field-required"
                                                            name="wpforms[fields][3]" placeholder="Name"
                                                            aria-errormessage="wpforms-18258-field_3-error" required>
                                                    </div>
                                                    <div id="wpforms-18258-field_1-container"
                                                        class="wpforms-field wpforms-field-email" data-field-id="1">
                                                        <label class="wpforms-field-label wpforms-label-hide"
                                                            for="wpforms-18258-field_1" aria-hidden="false">Email <span
                                                                class="wpforms-required-label"
                                                                aria-hidden="true">*</span></label><input type="email"
                                                            id="wpforms-18258-field_1"
                                                            class="wpforms-field-large wpforms-field-required"
                                                            name="wpforms[fields][1]" placeholder="Email"
                                                            spellcheck="false"
                                                            aria-errormessage="wpforms-18258-field_1-error" required>
                                                    </div>
                                                    <div id="wpforms-18258-field_2-container"
                                                        class="wpforms-field wpforms-field-textarea" data-field-id="2">
                                                        <label class="wpforms-field-label wpforms-label-hide"
                                                            for="wpforms-18258-field_2" aria-hidden="false">Your Message
                                                            <span class="wpforms-required-label"
                                                                aria-hidden="true">*</span></label><textarea
                                                            id="wpforms-18258-field_2"
                                                            class="wpforms-field-large wpforms-field-required"
                                                            name="wpforms[fields][2]" placeholder="Your Message"
                                                            aria-errormessage="wpforms-18258-field_2-error"
                                                            required></textarea></div>
                                                </div><!-- .wpforms-field-container -->
                                                <div class="wpforms-submit-container"><input type="hidden"
                                                        name="wpforms[id]" value="18258"><input type="hidden"
                                                        name="page_title" value="Contact"><input type="hidden"
                                                        name="page_url"
                                                        value="https://themes.pixelwars.org/interique/demo-01/contact/"><input
                                                        type="hidden" name="url_referer"
                                                        value="https://themes.pixelwars.org/interique/demo-01/portfolio/"><input
                                                        type="hidden" name="page_id" value="17820"><input type="hidden"
                                                        name="wpforms[post_id]" value="17820"><button type="submit"
                                                        name="wpforms[submit]" id="wpforms-submit-18258"
                                                        class="wpforms-submit" data-alt-text="Sending..."
                                                        data-submit-text="Submit" aria-live="assertive"
                                                        value="wpforms-submit">Submit</button><img decoding="async"
                                                        src="https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/wpforms-lite/assets/images/submit-spin.svg"
                                                        class="wpforms-submit-spinner" style="display: none;" width="26"
                                                        height="26" alt="Loading"></div>
                                            </form>
                                        </div> <!-- .wpforms-container -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <div class="elementor-element elementor-element-5d4ad9fc -bg-light e-con-full e-flex e-con e-parent"
                data-id="5d4ad9fc" data-element_type="container"
                data-settings="{&quot;background_background&quot;:&quot;classic&quot;}">
                <div class="elementor-element elementor-element-6c0db115 e-con-full e-flex e-con e-child"
                    data-id="6c0db115" data-element_type="container">
                    <div class="elementor-element elementor-element-1a71dd2a e-con-full e-flex e-con e-child"
                        data-id="1a71dd2a" data-element_type="container"
                        data-settings="{&quot;background_background&quot;:&quot;classic&quot;}">
                        <div class="elementor-element elementor-element-2d82a998 e-con-full e-flex e-con e-child"
                            data-id="2d82a998" data-element_type="container"
                            data-settings="{&quot;background_background&quot;:&quot;classic&quot;}">
                            <div class="elementor-element elementor-element-6ff54eb2 elementor-widget elementor-widget-spacer"
                                data-id="6ff54eb2" data-element_type="widget" data-widget_type="spacer.default">
                                <div class="elementor-widget-container">
                                    <div class="elementor-spacer">
                                        <div class="elementor-spacer-inner"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="elementor-element elementor-element-6fb20aa2 e-con-full e-flex e-con e-child"
                            data-id="6fb20aa2" data-element_type="container">
                            <div class="elementor-element elementor-element-482c8b79 elementor-invisible elementor-widget elementor-widget-qi_addons_for_elementor_animated_text"
                                data-id="482c8b79" data-element_type="widget"
                                data-settings="{&quot;_animation&quot;:&quot;fadeIn&quot;}"
                                data-widget_type="qi_addons_for_elementor_animated_text.default">
                                <div class="elementor-widget-container">
                                    <div
                                        class="qodef-shortcode qodef-m qodef-qi-animated-text qodef--animated-by-letter qodef-qi--has-appear qodef--appear-fade">
                                        <h1 class="qodef-m-title">
                                            <span class="qodef-e-word-holder">Your</span> <span
                                                class="qodef-e-word-holder">Dream</span> <span
                                                class="qodef-e-word-holder">Starts</span> <span
                                                class="qodef-e-word-holder">Here</span>
                                        </h1>
                                    </div>
                                </div>
                            </div>
                            <div class="elementor-element elementor-element-488aef58 -text-faded elementor-widget elementor-widget-heading"
                                data-id="488aef58" data-element_type="widget" data-widget_type="heading.default">
                                <div class="elementor-widget-container">
                                    <h4 class="elementor-heading-title elementor-size-default">All queries are replied
                                        within <u>24hrs</u>.</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>


        <footer id="colophon" class="site-footer" role="contentinfo">
            <div class="site-info">

                <p>© 2025 Motiff Square </p>
            </div> <!-- .site-info -->
        </footer> <!-- #colophon .site-footer -->
    </div>
    <script type="speculationrules">
{"prefetch":[{"source":"document","where":{"and":[{"href_matches":"\/interique\/demo-01\/*"},{"not":{"href_matches":["\/interique\/demo-01\/wp-*.php","\/interique\/demo-01\/wp-admin\/*","\/interique\/demo-01\/wp-content\/uploads\/sites\/2\/*","\/interique\/demo-01\/wp-content\/*","\/interique\/demo-01\/wp-content\/plugins\/*","\/interique\/demo-01\/wp-content\/themes\/interique\/*","\/interique\/demo-01\/*\\?(.+)"]}},{"not":{"selector_matches":"a[rel~=\"nofollow\"]"}},{"not":{"selector_matches":".no-prefetch, .no-prefetch a"}}]},"eagerness":"conservative"}]}
</script>
    <script>
        const lazyloadRunObserver = () => {
            const lazyloadBackgrounds = document.querySelectorAll(`.e-con.e-parent:not(.e-lazyloaded)`);
            const lazyloadBackgroundObserver = new IntersectionObserver((entries) => {
                entries.forEach((entry) => {
                    if (entry.isIntersecting) {
                        let lazyloadBackground = entry.target;
                        if (lazyloadBackground) {
                            lazyloadBackground.classList.add('e-lazyloaded');
                        }
                        lazyloadBackgroundObserver.unobserve(entry.target);
                    }
                });
            }, { rootMargin: '200px 0px 200px 0px' });
            lazyloadBackgrounds.forEach((lazyloadBackground) => {
                lazyloadBackgroundObserver.observe(lazyloadBackground);
            });
        };
        const events = [
            'DOMContentLoaded',
            'elementor/lazyload/observe',
        ];
        events.forEach((event) => {
            document.addEventListener(event, lazyloadRunObserver);
        });
    </script>
    <link rel='stylesheet' id='wpforms-modern-full-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/wpforms-lite/assets/css/frontend/modern/wpforms-full.min.css?ver=*******'
        type='text/css' media='all' />
    <style id='wpforms-modern-full-inline-css' type='text/css'>
        :root {
            --wpforms-field-border-radius: 3px;
            --wpforms-field-border-style: solid;
            --wpforms-field-border-size: 1px;
            --wpforms-field-background-color: #ffffff;
            --wpforms-field-border-color: rgba(0, 0, 0, 0.25);
            --wpforms-field-border-color-spare: rgba(0, 0, 0, 0.25);
            --wpforms-field-text-color: rgba(0, 0, 0, 0.7);
            --wpforms-field-menu-color: #ffffff;
            --wpforms-label-color: rgba(0, 0, 0, 0.85);
            --wpforms-label-sublabel-color: rgba(0, 0, 0, 0.55);
            --wpforms-label-error-color: #d63637;
            --wpforms-button-border-radius: 3px;
            --wpforms-button-border-style: none;
            --wpforms-button-border-size: 1px;
            --wpforms-button-background-color: #066aab;
            --wpforms-button-border-color: #066aab;
            --wpforms-button-text-color: #ffffff;
            --wpforms-page-break-color: #066aab;
            --wpforms-background-image: none;
            --wpforms-background-position: center center;
            --wpforms-background-repeat: no-repeat;
            --wpforms-background-size: cover;
            --wpforms-background-width: 100px;
            --wpforms-background-height: 100px;
            --wpforms-background-color: rgba(0, 0, 0, 0);
            --wpforms-background-url: none;
            --wpforms-container-padding: 0px;
            --wpforms-container-border-style: none;
            --wpforms-container-border-width: 1px;
            --wpforms-container-border-color: #000000;
            --wpforms-container-border-radius: 3px;
            --wpforms-field-size-input-height: 43px;
            --wpforms-field-size-input-spacing: 15px;
            --wpforms-field-size-font-size: 16px;
            --wpforms-field-size-line-height: 19px;
            --wpforms-field-size-padding-h: 14px;
            --wpforms-field-size-checkbox-size: 16px;
            --wpforms-field-size-sublabel-spacing: 5px;
            --wpforms-field-size-icon-size: 1;
            --wpforms-label-size-font-size: 16px;
            --wpforms-label-size-line-height: 19px;
            --wpforms-label-size-sublabel-font-size: 14px;
            --wpforms-label-size-sublabel-line-height: 17px;
            --wpforms-button-size-font-size: 17px;
            --wpforms-button-size-height: 41px;
            --wpforms-button-size-padding-h: 15px;
            --wpforms-button-size-margin-top: 10px;
            --wpforms-container-shadow-size-box-shadow: none;

        }
    </style>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-includes/js/jquery/ui/core.min.js?ver=1.13.3"
        id="jquery-ui-core-js"></script>
    <script type="text/javascript" id="qi-addons-for-elementor-script-js-extra">
        /* <![CDATA[ */
        var qodefQiAddonsGlobal = { "vars": { "adminBarHeight": 0, "iconArrowLeft": "<svg  xmlns=\"http:\/\/www.w3.org\/2000\/svg\" x=\"0px\" y=\"0px\" viewBox=\"0 0 34.2 32.3\" xml:space=\"preserve\" style=\"stroke-width: 2;\"><line x1=\"0.5\" y1=\"16\" x2=\"33.5\" y2=\"16\"\/><line x1=\"0.3\" y1=\"16.5\" x2=\"16.2\" y2=\"0.7\"\/><line x1=\"0\" y1=\"15.4\" x2=\"16.2\" y2=\"31.6\"\/><\/svg>", "iconArrowRight": "<svg  xmlns=\"http:\/\/www.w3.org\/2000\/svg\" x=\"0px\" y=\"0px\" viewBox=\"0 0 34.2 32.3\" xml:space=\"preserve\" style=\"stroke-width: 2;\"><line x1=\"0\" y1=\"16\" x2=\"33\" y2=\"16\"\/><line x1=\"17.3\" y1=\"0.7\" x2=\"33.2\" y2=\"16.5\"\/><line x1=\"17.3\" y1=\"31.6\" x2=\"33.5\" y2=\"15.4\"\/><\/svg>", "iconClose": "<svg  xmlns=\"http:\/\/www.w3.org\/2000\/svg\" x=\"0px\" y=\"0px\" viewBox=\"0 0 9.1 9.1\" xml:space=\"preserve\"><g><path d=\"M8.5,0L9,0.6L5.1,4.5L9,8.5L8.5,9L4.5,5.1L0.6,9L0,8.5L4,4.5L0,0.6L0.6,0L4.5,4L8.5,0z\"\/><\/g><\/svg>" } };
        /* ]]> */
    </script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/qi-addons-for-elementor/assets/js/main.min.js?ver=1.8.9"
        id="qi-addons-for-elementor-script-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/js/jquery.fitvids.js"
        id="fitvids-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/js/jarallax.min.js"
        id="jarallax-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/js/jarallax-video.min.js"
        id="jarallax-video-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/js/fluidbox/jquery.fluidbox.min.js"
        id="fluidbox-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/js/jquery-validation/jquery.validate.js"
        id="jqueryvalidation-js"></script>
    <script type="text/javascript" id="jqueryvalidation-js-after">
        /* <![CDATA[ */
        (function ($) {
            "use strict";
            $.extend($.validator.messages, {
                required: "This field is required.",
                remote: "Please fix this field.",
                email: "Please enter a valid email address.",
                url: "Please enter a valid URL.",
                date: "Please enter a valid date.",
                dateISO: "Please enter a valid date ( ISO ).",
                number: "Please enter a valid number.",
                digits: "Please enter only digits.",
                equalTo: "Please enter the same value again.",
                maxlength: $.validator.format("Please enter no more than {0} characters."),
                minlength: $.validator.format("Please enter at least {0} characters."),
                rangelength: $.validator.format("Please enter a value between {0} and {1} characters long."),
                range: $.validator.format("Please enter a value between {0} and {1}."),
                max: $.validator.format("Please enter a value less than or equal to {0}."),
                min: $.validator.format("Please enter a value greater than or equal to {0}."),
                step: $.validator.format("Please enter a multiple of {0}.")
            });
        })(jQuery);
        /* ]]> */
    </script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/qi-addons-for-elementor/inc/masonry/assets/js/plugins/isotope.pkgd.min.js?ver=3.0.6"
        id="isotope-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/js/jquery.magnific-popup/jquery.magnific-popup.min.js"
        id="magnific-popup-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/js/owl-carousel/owl.carousel.min.js"
        id="owl-carousel-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-includes/js/imagesloaded.min.js?ver=5.0.0"
        id="imagesloaded-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/js/jquery.collagePlus.min.js"
        id="collagePlus-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/js/jquery.fittext.js"
        id="fittext-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/js/resize-sensor.js"
        id="resize-sensor-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/js/jquery.sticky-sidebar.min.js"
        id="sticky-sidebar-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/js/main.js"
        id="interique-main-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/pixelwars-core/themes/global/js/shortcodes.js"
        id="pixelwars-core-shortcodes-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/elementor/assets/js/webpack.runtime.min.js?ver=3.29.0"
        id="elementor-webpack-runtime-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/elementor/assets/js/frontend-modules.min.js?ver=3.29.0"
        id="elementor-frontend-modules-js"></script>
    <script type="text/javascript" id="elementor-frontend-js-before">
        /* <![CDATA[ */
        var elementorFrontendConfig = { "environmentMode": { "edit": false, "wpPreview": false, "isScriptDebug": false }, "i18n": { "shareOnFacebook": "Share on Facebook", "shareOnTwitter": "Share on Twitter", "pinIt": "Pin it", "download": "Download", "downloadImage": "Download image", "fullscreen": "Fullscreen", "zoom": "Zoom", "share": "Share", "playVideo": "Play Video", "previous": "Previous", "next": "Next", "close": "Close", "a11yCarouselPrevSlideMessage": "Previous slide", "a11yCarouselNextSlideMessage": "Next slide", "a11yCarouselFirstSlideMessage": "This is the first slide", "a11yCarouselLastSlideMessage": "This is the last slide", "a11yCarouselPaginationBulletMessage": "Go to slide" }, "is_rtl": false, "breakpoints": { "xs": 0, "sm": 480, "md": 768, "lg": 1025, "xl": 1440, "xxl": 1600 }, "responsive": { "breakpoints": { "mobile": { "label": "Mobile Portrait", "value": 767, "default_value": 767, "direction": "max", "is_enabled": true }, "mobile_extra": { "label": "Mobile Landscape", "value": 880, "default_value": 880, "direction": "max", "is_enabled": false }, "tablet": { "label": "Tablet Portrait", "value": 1024, "default_value": 1024, "direction": "max", "is_enabled": true }, "tablet_extra": { "label": "Tablet Landscape", "value": 1200, "default_value": 1200, "direction": "max", "is_enabled": false }, "laptop": { "label": "Laptop", "value": 1366, "default_value": 1366, "direction": "max", "is_enabled": false }, "widescreen": { "label": "Widescreen", "value": 2400, "default_value": 2400, "direction": "min", "is_enabled": false } }, "hasCustomBreakpoints": false }, "version": "3.29.0", "is_static": false, "experimentalFeatures": { "e_font_icon_svg": true, "additional_custom_breakpoints": true, "container": true, "e_local_google_fonts": true, "nested-elements": true, "e_element_cache": true, "home_screen": true, "launchpad-checklist": true, "cloud-library": true, "e_opt_in_v4_page": true }, "urls": { "assets": "https:\/\/themes.pixelwars.org\/interique\/demo-01\/wp-content\/plugins\/elementor\/assets\/", "ajaxurl": "https:\/\/themes.pixelwars.org\/interique\/demo-01\/wp-admin\/admin-ajax.php", "uploadUrl": "https:\/\/themes.pixelwars.org\/interique\/demo-01\/wp-content\/uploads\/sites\/2" }, "nonces": { "floatingButtonsClickTracking": "0b692f7042" }, "swiperClass": "swiper", "settings": { "page": [], "editorPreferences": [] }, "kit": { "active_breakpoints": ["viewport_mobile", "viewport_tablet"], "global_image_lightbox": "yes", "lightbox_enable_counter": "yes", "lightbox_enable_fullscreen": "yes", "lightbox_enable_zoom": "yes", "lightbox_enable_share": "yes", "lightbox_title_src": "title", "lightbox_description_src": "description" }, "post": { "id": 17820, "title": "Contact%20%E2%80%93%20Interiqu%C3%A9", "excerpt": "", "featuredImage": false } };
        /* ]]> */
    </script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/elementor/assets/js/frontend.min.js?ver=3.29.0"
        id="elementor-frontend-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/bdthemes-prime-slider-lite/assets/js/prime-slider-site.min.js?ver=3.17.12"
        id="prime-slider-site-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-includes/js/dist/hooks.min.js?ver=4d63a3d491d11ffd8ac6"
        id="wp-hooks-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-includes/js/dist/i18n.min.js?ver=5e580eb46a90c2b997e6"
        id="wp-i18n-js"></script>
    <script type="text/javascript" id="wp-i18n-js-after">
        /* <![CDATA[ */
        wp.i18n.setLocaleData({ 'text direction\u0004ltr': ['ltr'] });
        /* ]]> */
    </script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/qi-addons-for-elementor/inc/plugins/elementor/assets/js/elementor.js?ver=6.8.1"
        id="qi-addons-for-elementor-elementor-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/wpforms-lite/assets/lib/jquery.validate.min.js?ver=1.21.0"
        id="wpforms-validation-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/wpforms-lite/assets/lib/mailcheck.min.js?ver=1.1.2"
        id="wpforms-mailcheck-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/wpforms-lite/assets/lib/punycode.min.js?ver=1.0.0"
        id="wpforms-punycode-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/wpforms-lite/assets/js/share/utils.min.js?ver=*******"
        id="wpforms-generic-utils-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/wpforms-lite/assets/js/frontend/wpforms.min.js?ver=*******"
        id="wpforms-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/wpforms-lite/assets/js/frontend/wpforms-modern.min.js?ver=*******"
        id="wpforms-modern-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/wpforms-lite/assets/js/frontend/fields/address.min.js?ver=*******"
        id="wpforms-address-field-js"></script>
    <script type='text/javascript'>
        /* <![CDATA[ */
        var wpforms_settings = { "val_required": "This field is required.", "val_email": "Please enter a valid email address.", "val_email_suggestion": "Did you mean {suggestion}?", "val_email_suggestion_title": "Click to accept this suggestion.", "val_email_restricted": "This email address is not allowed.", "val_number": "Please enter a valid number.", "val_number_positive": "Please enter a valid positive number.", "val_minimum_price": "Amount entered is less than the required minimum.", "val_confirm": "Field values do not match.", "val_checklimit": "You have exceeded the number of allowed selections: {#}.", "val_limit_characters": "{count} of {limit} max characters.", "val_limit_words": "{count} of {limit} max words.", "val_recaptcha_fail_msg": "Google reCAPTCHA verification failed, please try again later.", "val_turnstile_fail_msg": "Cloudflare Turnstile verification failed, please try again later.", "val_inputmask_incomplete": "Please fill out the field in required format.", "uuid_cookie": "", "locale": "en", "country": "", "country_list_label": "Country list", "wpforms_plugin_url": "https:\/\/themes.pixelwars.org\/interique\/demo-01\/wp-content\/plugins\/wpforms-lite\/", "gdpr": "", "ajaxurl": "https:\/\/themes.pixelwars.org\/interique\/demo-01\/wp-admin\/admin-ajax.php", "mailcheck_enabled": "1", "mailcheck_domains": [], "mailcheck_toplevel_domains": ["dev"], "is_ssl": "1", "currency_code": "USD", "currency_thousands": ",", "currency_decimals": "2", "currency_decimal": ".", "currency_symbol": "$", "currency_symbol_pos": "left", "val_requiredpayment": "Payment is required.", "val_creditcard": "Please enter a valid credit card number.", "css_vars": ["field-border-radius", "field-border-style", "field-border-size", "field-background-color", "field-border-color", "field-text-color", "field-menu-color", "label-color", "label-sublabel-color", "label-error-color", "button-border-radius", "button-border-style", "button-border-size", "button-background-color", "button-border-color", "button-text-color", "page-break-color", "background-image", "background-position", "background-repeat", "background-size", "background-width", "background-height", "background-color", "background-url", "container-padding", "container-border-style", "container-border-width", "container-border-color", "container-border-radius", "field-size-input-height", "field-size-input-spacing", "field-size-font-size", "field-size-line-height", "field-size-padding-h", "field-size-checkbox-size", "field-size-sublabel-spacing", "field-size-icon-size", "label-size-font-size", "label-size-line-height", "label-size-sublabel-font-size", "label-size-sublabel-line-height", "button-size-font-size", "button-size-height", "button-size-padding-h", "button-size-margin-top", "container-shadow-size-box-shadow"], "isModernMarkupEnabled": "1", "formErrorMessagePrefix": "Form error message", "errorMessagePrefix": "Error message", "submitBtnDisabled": "Submit button is disabled during form submission.", "error_updating_token": "Error updating token. Please try again or contact support if the issue persists.", "network_error": "Network error or server is unreachable. Check your connection or try again later.", "token_cache_lifetime": "86400", "hn_data": [], "address_field": { "list_countries_without_states": ["GB", "DE", "CH", "NL"] } }
        /* ]]> */
    </script>
</body>

</html>