/*! bdtUIkit 3.21.7 | https://www.getuikit.com | (c) 2014 - 2024 YOOtheme | MIT License */!function(t,e){"object"==typeof exports&&typeof module<"u"?module.exports=e():"function"==typeof define&&define.amd?define("uikit",e):(t=typeof globalThis<"u"?globalThis:t||self).bdtUIkit=e()}(this,(function(){"use strict";const{hasOwnProperty:t,toString:e}=Object.prototype;function i(e,i){return t.call(e,i)}const n=/\B([A-Z])/g,s=Z((t=>t.replace(n,"-$1").toLowerCase())),o=/-(\w)/g,r=Z((t=>(t.charAt(0).toLowerCase()+t.slice(1)).replace(o,((t,e)=>e.toUpperCase())))),a=Z((t=>t.charAt(0).toUpperCase()+t.slice(1)));function l(t,e){var i;return null==(i=null==t?void 0:t.startsWith)?void 0:i.call(t,e)}function h(t,e){var i;return null==(i=null==t?void 0:t.endsWith)?void 0:i.call(t,e)}function c(t,e){var i;return null==(i=null==t?void 0:t.includes)?void 0:i.call(t,e)}function d(t,e){var i;return null==(i=null==t?void 0:t.findIndex)?void 0:i.call(t,e)}const{isArray:u,from:f}=Array,{assign:p}=Object;function g(t){return"function"==typeof t}function m(t){return null!==t&&"object"==typeof t}function v(t){return"[object Object]"===e.call(t)}function b(t){return m(t)&&t===t.window}function w(t){return 9===y(t)}function $(t){return y(t)>=1}function x(t){return 1===y(t)}function y(t){return!b(t)&&m(t)&&t.nodeType}function S(t){return"boolean"==typeof t}function I(t){return"string"==typeof t}function k(t){return"number"==typeof t}function C(t){return k(t)||I(t)&&!isNaN(t-parseFloat(t))}function T(t){return!(u(t)?t.length:m(t)&&Object.keys(t).length)}function E(t){return void 0===t}function A(t){return S(t)?t:"true"===t||"1"===t||""===t||"false"!==t&&"0"!==t&&t}function D(t){const e=Number(t);return!isNaN(e)&&e}function _(t){return parseFloat(t)||0}function M(t){return t&&P(t)[0]}function P(t){return $(t)?[t]:Array.from(t||[]).filter($)}function O(t){if(b(t))return t;const e=w(t=M(t))?t:null==t?void 0:t.ownerDocument;return(null==e?void 0:e.defaultView)||window}function B(t,e){return t===e||m(t)&&m(e)&&Object.keys(t).length===Object.keys(e).length&&H(t,((t,i)=>t===e[i]))}function N(t,e,i){return t.replace(new RegExp(`${e}|${i}`,"g"),(t=>t===e?i:e))}function z(t){return t[t.length-1]}function H(t,e){for(const i in t)if(!1===e(t[i],i))return!1;return!0}function F(t,e){return t.slice().sort((({[e]:t=0},{[e]:i=0})=>t>i?1:i>t?-1:0))}function j(t,e){return t.reduce(((t,i)=>t+_(g(e)?e(i):i[e])),0)}function L(t,e){const i=new Set;return t.filter((({[e]:t})=>!i.has(t)&&i.add(t)))}function W(t,e){return e.reduce(((e,i)=>({...e,[i]:t[i]})),{})}function q(t,e=0,i=1){return Math.min(Math.max(D(t)||0,e),i)}function V(){}function R(...t){return[["bottom","top"],["right","left"]].every((([e,i])=>Math.min(...t.map((({[e]:t})=>t)))-Math.max(...t.map((({[i]:t})=>t)))>0))}function U(t,e){return t.x<=e.right&&t.x>=e.left&&t.y<=e.bottom&&t.y>=e.top}function Y(t,e,i){const n="width"===e?"height":"width";return{[n]:t[e]?Math.round(i*t[n]/t[e]):t[n],[e]:i}}function X(t,e){t={...t};for(const i in t)t=t[i]>e[i]?Y(t,i,e[i]):t;return t}const J={ratio:Y,contain:X,cover:function(t,e){t=X(t,e);for(const i in t)t=t[i]<e[i]?Y(t,i,e[i]):t;return t}};function G(t,e,i=0,n=!1){e=P(e);const{length:s}=e;return s?(t=C(t)?D(t):"next"===t?i+1:"previous"===t?i-1:"last"===t?s-1:e.indexOf(M(t)),n?q(t,0,s-1):(t%=s)<0?t+s:t):-1}function Z(t){const e=Object.create(null);return(i,...n)=>e[i]||(e[i]=t(i,...n))}function K(t,...e){for(const i of P(t)){const t=nt(e).filter((t=>!et(i,t)));t.length&&i.classList.add(...t)}}function Q(t,...e){for(const i of P(t)){const t=nt(e).filter((t=>et(i,t)));t.length&&i.classList.remove(...t)}}function tt(t,e,i){i=nt(i),Q(t,e=nt(e).filter((t=>!c(i,t)))),K(t,i)}function et(t,e){return[e]=nt(e),P(t).some((t=>t.classList.contains(e)))}function it(t,e,i){const n=nt(e);E(i)||(i=!!i);for(const e of P(t))for(const t of n)e.classList.toggle(t,i)}function nt(t){return t?u(t)?t.map(nt).flat():String(t).split(" ").filter(Boolean):[]}function st(t,e,i){var n;if(m(e))for(const i in e)st(t,i,e[i]);else{if(E(i))return null==(n=M(t))?void 0:n.getAttribute(e);for(const n of P(t))g(i)&&(i=i.call(n,st(n,e))),null===i?rt(n,e):n.setAttribute(e,i)}}function ot(t,e){return P(t).some((t=>t.hasAttribute(e)))}function rt(t,e){P(t).forEach((t=>t.removeAttribute(e)))}function at(t,e){for(const i of[e,`data-${e}`])if(ot(t,i))return st(t,i)}const lt=typeof window<"u",ht=lt&&"rtl"===document.dir,ct=lt&&"ontouchstart"in window,dt=lt&&window.PointerEvent,ut=dt?"pointerdown":ct?"touchstart":"mousedown",ft=dt?"pointermove":ct?"touchmove":"mousemove",pt=dt?"pointerup":ct?"touchend":"mouseup",gt=dt?"pointerenter":ct?"":"mouseenter",mt=dt?"pointerleave":ct?"":"mouseleave",vt=dt?"pointercancel":"touchcancel",bt={area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0};function wt(t){return P(t).some((t=>bt[t.tagName.toLowerCase()]))}const $t=lt&&Element.prototype.checkVisibility||function(){return this.offsetWidth||this.offsetHeight||this.getClientRects().length};function xt(t){return P(t).some((t=>$t.call(t)))}const yt="input,select,textarea,button";function St(t){return P(t).some((t=>Et(t,yt)))}const It=`${yt},a[href],[tabindex]`;function kt(t){return Et(t,It)}function Ct(t){var e;return null==(e=M(t))?void 0:e.parentElement}function Tt(t,e){return P(t).filter((t=>Et(t,e)))}function Et(t,e){return P(t).some((t=>t.matches(e)))}function At(t,e){const i=[];for(;t=Ct(t);)(!e||Et(t,e))&&i.push(t);return i}function Dt(t,e){const i=(t=M(t))?f(t.children):[];return e?Tt(i,e):i}function _t(t,e){return e?P(t).indexOf(M(e)):Dt(Ct(t)).indexOf(t)}function Mt(t){return(t=M(t))&&["origin","pathname","search"].every((e=>t[e]===location[e]))}function Pt(t){if(Mt(t)){const{hash:e,ownerDocument:i}=M(t),n=decodeURIComponent(e).slice(1);return i.getElementById(n)||i.getElementsByName(n)[0]}}function Ot(t,e){return Nt(t,Ht(t,e))}function Bt(t,e){return zt(t,Ht(t,e))}function Nt(t,e){return M(Vt(t,M(e),"querySelector"))}function zt(t,e){return P(Vt(t,M(e),"querySelectorAll"))}function Ht(t,e=document){return I(t)&&Lt(t).isContextSelector||w(e)?e:e.ownerDocument}const Ft=/([!>+~-])(?=\s+[!>+~-]|\s*$)/g,jt=/(\([^)]*\)|[^,])+/g,Lt=Z((t=>{t=t.replace(Ft,"$1 *");let e=!1;const i=[];for(let n of t.match(jt))n=n.trim(),e||(e=["!","+","~","-",">"].includes(n[0])),i.push(n);return{selector:i.join(","),selectors:i,isContextSelector:e}})),Wt=/(\([^)]*\)|\S)*/,qt=Z((t=>{t=t.slice(1).trim();const[e]=t.match(Wt);return[e,t.slice(e.length+1)]}));function Vt(t,e=document,i){if(!t||!I(t))return t;const n=Lt(t);if(!n.isContextSelector)return Rt(e,i,n.selector);t="";const s=1===n.selectors.length;for(let o of n.selectors){let n,r=e;if("!"===o[0]&&([n,o]=qt(o),r=e.parentElement.closest(n),!o&&s)||r&&"-"===o[0]&&([n,o]=qt(o),r=r.previousElementSibling,r=Et(r,n)?r:null,!o&&s))return r;if(r){if(s)return"~"===o[0]||"+"===o[0]?(o=`:scope > :nth-child(${_t(r)+1}) ${o}`,r=r.parentElement):">"===o[0]&&(o=`:scope ${o}`),Rt(r,i,o);t+=`${t?",":""}${Ut(r)} ${o}`}}return w(e)||(e=e.ownerDocument),Rt(e,i,t)}function Rt(t,e,i){try{return t[e](i)}catch{return null}}function Ut(t){const e=[];for(;t.parentNode;){const i=st(t,"id");if(i){e.unshift(`#${Yt(i)}`);break}{let{tagName:i}=t;"HTML"!==i&&(i+=`:nth-child(${_t(t)+1})`),e.unshift(i),t=t.parentNode}}return e.join(" > ")}function Yt(t){return I(t)?CSS.escape(t):""}function Xt(...t){let[e,i,n,s,o=!1]=Qt(t);s.length>1&&(s=function(t){return e=>u(e.detail)?t(e,...e.detail):t(e)}(s)),null!=o&&o.self&&(s=function(t){return function(e){if(e.target===e.currentTarget||e.target===e.current)return t.call(null,e)}}(s)),n&&(s=function(t,e){return i=>{const n=">"===t[0]?zt(t,i.currentTarget).reverse().find((t=>t.contains(i.target))):i.target.closest(t);n&&(i.current=n,e.call(this,i),delete i.current)}}(n,s));for(const t of i)for(const i of e)i.addEventListener(t,s,o);return()=>Jt(e,i,s,o)}function Jt(...t){let[e,i,,n,s=!1]=Qt(t);for(const t of i)for(const i of e)i.removeEventListener(t,n,s)}function Gt(...t){const[e,i,n,s,o=!1,r]=Qt(t),a=Xt(e,i,n,(t=>{const e=!r||r(t);e&&(a(),s(t,e))}),o);return a}function Zt(t,e,i){return ie(t).every((t=>t.dispatchEvent(Kt(e,!0,!0,i))))}function Kt(t,e=!0,i=!1,n){return I(t)&&(t=new CustomEvent(t,{bubbles:e,cancelable:i,detail:n})),t}function Qt(t){return t[0]=ie(t[0]),I(t[1])&&(t[1]=t[1].split(" ")),g(t[2])&&t.splice(2,0,!1),t}function te(t){return t&&"addEventListener"in t}function ee(t){return te(t)?t:M(t)}function ie(t){return u(t)?t.map(ee).filter(Boolean):I(t)?zt(t):te(t)?[t]:P(t)}function ne(t){return"touch"===t.pointerType||!!t.touches}function se(t){var e,i;const{clientX:n,clientY:s}=(null==(e=t.touches)?void 0:e[0])||(null==(i=t.changedTouches)?void 0:i[0])||t;return{x:n,y:s}}const oe={"animation-iteration-count":!0,"column-count":!0,"fill-opacity":!0,"flex-grow":!0,"flex-shrink":!0,"font-weight":!0,"line-height":!0,opacity:!0,order:!0,orphans:!0,"stroke-dasharray":!0,"stroke-dashoffset":!0,widows:!0,"z-index":!0,zoom:!0};function re(t,e,i,n){const s=P(t);for(const t of s)if(I(e)){if(e=ae(e),E(i))return getComputedStyle(t).getPropertyValue(e);t.style.setProperty(e,C(i)&&!oe[e]?`${i}px`:i||k(i)?i:"",n)}else{if(u(e)){const i={};for(const n of e)i[n]=re(t,n);return i}if(m(e))for(const n in e)re(t,n,e[n],i)}return s[0]}const ae=Z((t=>{if(l(t,"--"))return t;t=s(t);const{style:e}=document.documentElement;if(t in e)return t;for(const i of["webkit","moz"]){const n=`-${i}-${t}`;if(n in e)return n}})),le="bdt-transition",he="transitionend",ce="transitioncanceled";const de={start:function(t,e,i=400,n="linear"){return i=Math.round(i),Promise.all(P(t).map((t=>new Promise(((s,o)=>{for(const i in e)re(t,i);const r=setTimeout((()=>Zt(t,he)),i);Gt(t,[he,ce],(({type:e})=>{clearTimeout(r),Q(t,le),re(t,{transitionProperty:"",transitionDuration:"",transitionTimingFunction:""}),e===ce?o():s(t)}),{self:!0}),K(t,le),re(t,{transitionProperty:Object.keys(e).map(ae).join(","),transitionDuration:`${i}ms`,transitionTimingFunction:n,...e})})))))},async stop(t){Zt(t,he),await Promise.resolve()},async cancel(t){Zt(t,ce),await Promise.resolve()},inProgress:t=>et(t,le)},ue="bdt-animation",fe="animationend",pe="animationcanceled";function ge(t,e,i=200,n,s){return Promise.all(P(t).map((t=>new Promise(((o,r)=>{et(t,ue)&&Zt(t,pe);const a=[e,ue,`${ue}-${s?"leave":"enter"}`,n&&`bdt-transform-origin-${n}`,s&&`${ue}-reverse`],l=setTimeout((()=>Zt(t,fe)),i);Gt(t,[fe,pe],(({type:e})=>{clearTimeout(l),e===pe?r():o(t),re(t,"animationDuration",""),Q(t,a)}),{self:!0}),re(t,"animationDuration",`${i}ms`),K(t,a)})))))}const me={in:ge,out:(t,e,i,n)=>ge(t,e,i,n,!0),inProgress:t=>et(t,ue),cancel(t){Zt(t,pe)}};function ve(t,...e){return e.some((e=>{var i;return(null==(i=null==t?void 0:t.tagName)?void 0:i.toLowerCase())===e.toLowerCase()}))}function be(t){return(t=Pe(t)).innerHTML="",t}function we(t,e){return E(e)?Pe(t).innerHTML:xe(be(t),e)}const $e=Ie("prepend"),xe=Ie("append"),ye=Ie("before"),Se=Ie("after");function Ie(t){return function(e,i){var n;const s=P(I(i)?De(i):i);return null==(n=Pe(e))||n[t](...s),_e(s)}}function ke(t){P(t).forEach((t=>t.remove()))}function Ce(t,e){for(e=M(ye(t,e));e.firstElementChild;)e=e.firstElementChild;return xe(e,t),e}function Te(t,e){return P(P(t).map((t=>t.hasChildNodes()?Ce(f(t.childNodes),e):xe(t,e))))}function Ee(t){P(t).map(Ct).filter(((t,e,i)=>i.indexOf(t)===e)).forEach((t=>t.replaceWith(...t.childNodes)))}const Ae=/^<(\w+)\s*\/?>(?:<\/\1>)?$/;function De(t){const e=Ae.exec(t);if(e)return document.createElement(e[1]);const i=document.createElement("template");return i.innerHTML=t.trim(),_e(i.content.childNodes)}function _e(t){return t.length>1?t:t[0]}function Me(t,e){if(x(t))for(e(t),t=t.firstElementChild;t;)Me(t,e),t=t.nextElementSibling}function Pe(t,e){return Be(t)?M(De(t)):Nt(t,e)}function Oe(t,e){return Be(t)?P(De(t)):zt(t,e)}function Be(t){return I(t)&&l(t.trim(),"<")}const Ne={width:["left","right"],height:["top","bottom"]};function ze(t){const e=x(t)?M(t).getBoundingClientRect():{height:Le(t),width:We(t),top:0,left:0};return{height:e.height,width:e.width,top:e.top,left:e.left,bottom:e.top+e.height,right:e.left+e.width}}function He(t,e){e&&re(t,{left:0,top:0});const i=ze(t);if(t){const{scrollY:e,scrollX:n}=O(t),s={height:e,width:n};for(const t in Ne)for(const e of Ne[t])i[e]+=s[t]}if(!e)return i;for(const n of["left","top"])re(t,n,e[n]-i[n])}function Fe(t){let{top:e,left:i}=He(t);const{ownerDocument:{body:n,documentElement:s},offsetParent:o}=M(t);let r=o||s;for(;r&&(r===n||r===s)&&"static"===re(r,"position");)r=r.parentNode;if(x(r)){const t=He(r);e-=t.top+_(re(r,"borderTopWidth")),i-=t.left+_(re(r,"borderLeftWidth"))}return{top:e-_(re(t,"marginTop")),left:i-_(re(t,"marginLeft"))}}function je(t){const e=[(t=M(t)).offsetTop,t.offsetLeft];for(;t=t.offsetParent;)if(e[0]+=t.offsetTop+_(re(t,"borderTopWidth")),e[1]+=t.offsetLeft+_(re(t,"borderLeftWidth")),"fixed"===re(t,"position")){const i=O(t);return e[0]+=i.scrollY,e[1]+=i.scrollX,e}return e}const Le=qe("height"),We=qe("width");function qe(t){const e=a(t);return(i,n)=>{if(E(n)){if(b(i))return i[`inner${e}`];if(w(i)){const t=i.documentElement;return Math.max(t[`offset${e}`],t[`scroll${e}`])}return(n="auto"===(n=re(i=M(i),t))?i[`offset${e}`]:_(n)||0)-Ve(i,t)}return re(i,t,n||0===n?+n+Ve(i,t)+"px":"")}}function Ve(t,e,i="border-box"){return re(t,"boxSizing")===i?j(Ne[e].map(a),(e=>_(re(t,`padding${e}`))+_(re(t,`border${e}Width`)))):0}function Re(t){for(const e in Ne)for(const i in Ne[e])if(Ne[e][i]===t)return Ne[e][1-i];return t}function Ue(t,e="width",i=window,n=!1){return I(t)?j(Xe(t),(t=>{const s=Ge(t);return s?function(t,e){return t*_(e)/100}("vh"===s?Ze||(Ke||(Ke=Pe("<div>"),re(Ke,{height:"100vh",position:"fixed"}),Xt(window,"resize",(()=>Ze=null))),xe(document.body,Ke),Ze=Ke.clientHeight,ke(Ke),Ze):"vw"===s?We(O(i)):n?i[`offset${a(e)}`]:ze(i)[e],t):t})):_(t)}const Ye=/-?\d+(?:\.\d+)?(?:v[wh]|%|px)?/g,Xe=Z((t=>t.toString().replace(/\s/g,"").match(Ye)||[])),Je=/(?:v[hw]|%)$/,Ge=Z((t=>(t.match(Je)||[])[0]));let Ze,Ke;const Qe={read:function(t){return ti.push(t),si(),t},write:function(t){return ei.push(t),si(),t},clear:function(t){ri(ti,t),ri(ei,t)},flush:ni},ti=[],ei=[];let ii=!1;function ni(){oi(ti),oi(ei.splice(0)),ii=!1,(ti.length||ei.length)&&si()}function si(){ii||(ii=!0,queueMicrotask(ni))}function oi(t){let e;for(;e=t.shift();)try{e()}catch(t){console.error(t)}}function ri(t,e){const i=t.indexOf(e);return~i&&t.splice(i,1)}class ai{init(){let t;this.positions=[],this.unbind=Xt(document,"mousemove",(e=>t=se(e))),this.interval=setInterval((()=>{t&&(this.positions.push(t),this.positions.length>5&&this.positions.shift())}),50)}cancel(){var t;null==(t=this.unbind)||t.call(this),clearInterval(this.interval)}movesTo(t){if(!this.positions||this.positions.length<2)return!1;const e=ze(t),{left:i,right:n,top:s,bottom:o}=e,[r]=this.positions,a=z(this.positions),l=[r,a];return!U(a,e)&&[[{x:i,y:s},{x:n,y:o}],[{x:i,y:o},{x:n,y:s}]].some((t=>{const i=function([{x:t,y:e},{x:i,y:n}],[{x:s,y:o},{x:r,y:a}]){const l=(a-o)*(i-t)-(r-s)*(n-e);if(0===l)return!1;const h=((r-s)*(e-o)-(a-o)*(t-s))/l;return!(h<0)&&{x:t+h*(i-t),y:e+h*(n-e)}}(l,t);return i&&U(i,e)}))}}function li(t,e,i={},{intersecting:n=!0}={}){const s=new IntersectionObserver(n?(t,i)=>{t.some((t=>t.isIntersecting))&&e(t,i)}:e,i);for(const e of P(t))s.observe(e);return s}const hi=lt&&window.ResizeObserver;function ci(t,e,i={box:"border-box"}){if(hi)return fi(ResizeObserver,t,e,i);const n=[Xt(window,"load resize",e),Xt(document,"loadedmetadata load",e,!0)];return{disconnect:()=>n.map((t=>t()))}}function di(t){return{disconnect:Xt([window,window.visualViewport],"resize",t)}}function ui(t,e,i){return fi(MutationObserver,t,e,i)}function fi(t,e,i,n){const s=new t(i);for(const t of P(e))s.observe(t,n);return s}function pi(t){wi(t)&&yi(t,{func:"playVideo",method:"play"}),bi(t)&&t.play().catch(V)}function gi(t){wi(t)&&yi(t,{func:"pauseVideo",method:"pause"}),bi(t)&&t.pause()}function mi(t){wi(t)&&yi(t,{func:"mute",method:"setVolume",value:0}),bi(t)&&(t.muted=!0)}function vi(t){return bi(t)||wi(t)}function bi(t){return ve(t,"video")}function wi(t){return ve(t,"iframe")&&($i(t)||xi(t))}function $i(t){return!!t.src.match(/\/\/.*?youtube(-nocookie)?\.[a-z]+\/(watch\?v=[^&\s]+|embed)|youtu\.be\/.*/)}function xi(t){return!!t.src.match(/vimeo\.com\/video\/.*/)}async function yi(t,e){await function(t){if(t[Ii])return t[Ii];const e=$i(t),i=xi(t),n=++ki;let s;return t[Ii]=new Promise((o=>{e&&Gt(t,"load",(()=>{const e=()=>Si(t,{event:"listening",id:n});s=setInterval(e,100),e()})),Gt(window,"message",o,!1,(({data:t})=>{try{return t=JSON.parse(t),e&&(null==t?void 0:t.id)===n&&"onReady"===t.event||i&&Number(null==t?void 0:t.player_id)===n}catch{}})),t.src=`${t.src}${c(t.src,"?")?"&":"?"}${e?"enablejsapi=1":`api=1&player_id=${n}`}`})).then((()=>clearInterval(s)))}(t),Si(t,e)}function Si(t,e){t.contentWindow.postMessage(JSON.stringify({event:"command",...e}),"*")}const Ii="_ukPlayer";let ki=0;function Ci(t,{offset:e=0}={}){const i=xt(t)?Ei(t,!1,["hidden"]):[];return i.reduce(((n,s,o)=>{const{scrollTop:r,scrollHeight:a,offsetHeight:l}=s,h=_i(s),c=a-h.height,{height:d,top:u}=i[o-1]?_i(i[o-1]):He(t);let f=Math.ceil(u-h.top-e+r);return e>0&&l<d+e?f+=e:e=0,f>c?(e-=f-c,f=c):f<0&&(e-=f,f=0),()=>function(t,e,n,s){return new Promise((o=>{const r=t.scrollTop,a=function(t){return 40*Math.pow(t,.375)}(Math.abs(e)),l=Date.now(),h=Bi(t)===t,c=He(n).top+(h?0:r);let d=0,u=15;!function f(){const p=function(t){return.5*(1-Math.cos(Math.PI*t))}(q((Date.now()-l)/a));let g=0;i[0]===t&&r+e<s&&(g=He(n).top+(h?0:t.scrollTop)-c-ze(Mi(n)).height),t.scrollTop=r+(e+g)*p,1!==p||d!==g&&u--?(d=g,requestAnimationFrame(f)):o()}()}))}(s,f-r,t,c).then(n)}),(()=>Promise.resolve()))()}function Ti(t,e=0,i=0){if(!xt(t))return 0;const n=Ai(t,!0),{scrollHeight:s,scrollTop:o}=n,{height:r}=_i(n),a=s-r,l=je(t)[0]-je(n)[0],h=Math.max(0,l-r+e),c=Math.min(a,l+t.offsetHeight-i);return h<c?q((o-h)/(c-h)):1}function Ei(t,e=!1,i=[]){const n=Bi(t);let s=At(t).reverse();s=s.slice(s.indexOf(n)+1);const o=d(s,(t=>"fixed"===re(t,"position")));return~o&&(s=s.slice(o)),[n].concat(s.filter((t=>re(t,"overflow").split(" ").some((t=>c(["auto","scroll",...i],t)))&&(!e||t.scrollHeight>_i(t).height)))).reverse()}function Ai(...t){return Ei(...t)[0]}function Di(t){return Ei(t,!1,["hidden","clip"])}function _i(t){const e=O(t),i=Bi(t),n=t.contains(i);if(n&&e.visualViewport){let{height:t,width:i,scale:n,pageTop:s,pageLeft:o}=e.visualViewport;return t=Math.round(t*n),i=Math.round(i*n),{height:t,width:i,top:s,left:o,bottom:s+t,right:o+i}}let s=He(n?e:t);if("inline"===re(t,"display"))return s;const{body:o,documentElement:r}=e.document,l=n?i===r||i.clientHeight<o.clientHeight?i:o:t;for(let[t,e,i,n]of[["width","x","left","right"],["height","y","top","bottom"]]){const o=s[t]%1;s[i]+=_(re(l,`border-${i}-width`)),s[t]=s[e]=l[`client${a(t)}`]-(o?o<.5?-o:1-o:0),s[n]=s[t]+s[i]}return s}function Mi(t){const{left:e,width:i,top:n}=ze(t);for(const s of n?[0,n]:[0]){let n;for(const o of O(t).document.elementsFromPoint(e+i/2,s))!o.contains(t)&&!et(o,"bdt-togglable-leave")&&(Oi(o,"fixed")&&Pi(At(t).reverse().find((t=>!t.contains(o)&&!Oi(t,"static"))))<Pi(o)||Oi(o,"sticky")&&Ct(o).contains(t))&&(!n||ze(n).height<ze(o).height)&&(n=o);if(n)return n}}function Pi(t){return _(re(t,"zIndex"))}function Oi(t,e){return re(t,"position")===e}function Bi(t){return O(t).document.scrollingElement}const Ni=[["width","x","left","right"],["height","y","top","bottom"]];function zi(t,e,i){i={attach:{element:["left","top"],target:["left","top"],...i.attach},offset:[0,0],placement:[],...i},u(e)||(e=[e,e]),He(t,Hi(t,e,i))}function Hi(t,e,i){const n=Fi(t,e,i),{boundary:s,viewportOffset:o=0,placement:r}=i;let a=n;for(const[l,[h,,c,d]]of Object.entries(Ni)){const u=Wi(t,e[l],o,s,l);if(Ui(n,u,l))continue;let f=0;if("flip"===r[l]){const s=i.attach.target[l];if(s===d&&n[d]<=u[d]||s===c&&n[c]>=u[c])continue;f=Yi(t,e,i,l)[c]-n[c];const r=qi(t,e[l],o,l);if(!Ui(ji(n,f,l),r,l)){if(Ui(n,r,l))continue;if(i.recursion)return!1;const s=Xi(t,e,i);if(s&&Ui(s,r,1-l))return s;continue}}else if("shift"===r[l]){const t=He(e[l]),{offset:s}=i;f=q(q(n[c],u[c],u[d]-n[h]),t[c]-n[h]+s[l],t[d]-s[l])-n[c]}a=ji(a,f,l)}return a}function Fi(t,e,i){let{attach:n,offset:s}={attach:{element:["left","top"],target:["left","top"],...i.attach},offset:[0,0],...i},o=He(t);for(const[t,[i,,r,a]]of Object.entries(Ni)){const l=n.target[t]===n.element[t]?_i(e[t]):He(e[t]);o=ji(o,l[r]-o[r]+Li(n.target[t],a,l[i])-Li(n.element[t],a,o[i])+ +s[t],t)}return o}function ji(t,e,i){const[,n,s,o]=Ni[i],r={...t};return r[s]=t[n]=t[s]+e,r[o]+=e,r}function Li(t,e,i){return"center"===t?i/2:t===e?i:0}function Wi(t,e,i,n,s){let o=Ri(...Vi(t,e).map(_i));return i&&(o[Ni[s][2]]+=i,o[Ni[s][3]]-=i),n&&(o=Ri(o,He(u(n)?n[s]:n))),o}function qi(t,e,i,n){const[s,o,r,l]=Ni[n],[h]=Vi(t,e),c=_i(h);return["auto","scroll"].includes(re(h,`overflow-${o}`))&&(c[r]-=h[`scroll${a(r)}`],c[l]=c[r]+h[`scroll${a(s)}`]),c[r]+=i,c[l]-=i,c}function Vi(t,e){return Di(e).filter((e=>e.contains(t)))}function Ri(...t){let e={};for(const i of t)for(const[,,t,n]of Ni)e[t]=Math.max(e[t]||0,i[t]),e[n]=Math.min(...[e[n],i[n]].filter(Boolean));return e}function Ui(t,e,i){const[,,n,s]=Ni[i];return t[n]>=e[n]&&t[s]<=e[s]}function Yi(t,e,{offset:i,attach:n},s){return Fi(t,e,{attach:{element:Ji(n.element,s),target:Ji(n.target,s)},offset:Zi(i,s)})}function Xi(t,e,i){return Hi(t,e,{...i,attach:{element:i.attach.element.map(Gi).reverse(),target:i.attach.target.map(Gi).reverse()},offset:i.offset.reverse(),placement:i.placement.reverse(),recursion:!0})}function Ji(t,e){const i=[...t],n=Ni[e].indexOf(t[e]);return~n&&(i[e]=Ni[e][1-n%2+2]),i}function Gi(t){for(let e=0;e<Ni.length;e++){const i=Ni[e].indexOf(t);if(~i)return Ni[1-e][i%2+2]}}function Zi(t,e){return(t=[...t])[e]*=-1,t}var Ki=Object.freeze({__proto__:null,$:Pe,$$:Oe,Animation:me,Dimensions:J,MouseTracker:ai,Transition:de,addClass:K,after:Se,append:xe,apply:Me,assign:p,attr:st,before:ye,boxModelAdjust:Ve,camelize:r,children:Dt,clamp:q,createEvent:Kt,css:re,data:at,dimensions:ze,each:H,empty:be,endsWith:h,escape:Yt,fastdom:Qe,filter:Tt,find:Nt,findAll:zt,findIndex:d,flipPosition:Re,fragment:De,getCoveringElement:Mi,getEventPos:se,getIndex:G,getTargetedElement:Pt,hasAttr:ot,hasClass:et,hasOwn:i,hasTouch:ct,height:Le,html:we,hyphenate:s,inBrowser:lt,includes:c,index:_t,intersectRect:R,isArray:u,isBoolean:S,isDocument:w,isElement:x,isEmpty:T,isEqual:B,isFocusable:kt,isFunction:g,isInView:function(t,e=0,i=0){return!!xt(t)&&R(...Di(t).map((t=>{const{top:n,left:s,bottom:o,right:r}=_i(t);return{top:n-e,left:s-i,bottom:o+e,right:r+i}})).concat(He(t)))},isInput:St,isNode:$,isNumber:k,isNumeric:C,isObject:m,isPlainObject:v,isRtl:ht,isSameSiteAnchor:Mt,isString:I,isTag:ve,isTouch:ne,isUndefined:E,isVideo:vi,isVisible:xt,isVoidElement:wt,isWindow:b,last:z,matches:Et,memoize:Z,mute:mi,noop:V,observeIntersection:li,observeMutation:ui,observeResize:ci,observeViewportResize:di,off:Jt,offset:He,offsetPosition:je,offsetViewport:_i,on:Xt,once:Gt,overflowParents:Di,parent:Ct,parents:At,pause:gi,pick:W,play:pi,pointInRect:U,pointerCancel:vt,pointerDown:ut,pointerEnter:gt,pointerLeave:mt,pointerMove:ft,pointerUp:pt,position:Fe,positionAt:zi,prepend:$e,propName:ae,query:Ot,queryAll:Bt,ready:function(t){"loading"===document.readyState?Gt(document,"DOMContentLoaded",t):t()},remove:ke,removeAttr:rt,removeClass:Q,replaceClass:tt,scrollIntoView:Ci,scrollParent:Ai,scrollParents:Ei,scrolledOver:Ti,selFocusable:It,selInput:yt,sortBy:F,startsWith:l,sumBy:j,swap:N,toArray:f,toBoolean:A,toEventTargets:ie,toFloat:_,toNode:M,toNodes:P,toNumber:D,toPx:Ue,toWindow:O,toggleClass:it,trigger:Zt,ucfirst:a,uniqueBy:L,unwrap:Ee,width:We,wrapAll:Ce,wrapInner:Te}),Qi={connected(){K(this.$el,this.$options.id)}};const tn=["days","hours","minutes","seconds"];var en={mixins:[Qi],props:{date:String,clsWrapper:String,role:String},data:{date:"",clsWrapper:".bdt-countdown-%unit%",role:"timer"},connected(){st(this.$el,"role",this.role),this.date=_(Date.parse(this.$props.date)),this.end=!1,this.start()},disconnected(){this.stop()},events:{name:"visibilitychange",el:()=>document,handler(){document.hidden?this.stop():this.start()}},methods:{start(){this.stop(),this.update(),this.timer||(Zt(this.$el,"countdownstart"),this.timer=setInterval(this.update,1e3))},stop(){this.timer&&(clearInterval(this.timer),Zt(this.$el,"countdownstop"),this.timer=null)},update(){const t=function(t){const e=Math.max(0,t-Date.now())/1e3;return{total:e,seconds:e%60,minutes:e/60%60,hours:e/60/60%24,days:e/60/60/24}}(this.date);t.total||(this.stop(),this.end||(Zt(this.$el,"countdownend"),this.end=!0));for(const e of tn){const i=Pe(this.clsWrapper.replace("%unit%",e),this.$el);if(!i)continue;let n=Math.trunc(t[e]).toString().padStart(2,"0");i.textContent!==n&&(n=n.split(""),n.length!==i.children.length&&we(i,n.map((()=>"<span></span>")).join("")),n.forEach(((t,e)=>i.children[e].textContent=t)))}}}};const nn={};function sn(t,e,i){return nn.computed(g(t)?t.call(i,i):t,g(e)?e.call(i,i):e)}function on(t,e){return t=t&&!u(t)?[t]:t,e?t?t.concat(e):u(e)?e:[e]:t}function rn(t,e){return E(e)?t:e}function an(t,e,n){const s={};if(g(e)&&(e=e.options),e.extends&&(t=an(t,e.extends,n)),e.mixins)for(const i of e.mixins)t=an(t,i,n);for(const e in t)o(e);for(const n in e)i(t,n)||o(n);function o(i){s[i]=(nn[i]||rn)(t[i],e[i],n)}return s}function ln(t,e=[]){try{return t?l(t,"{")?JSON.parse(t):e.length&&!c(t,":")?{[e[0]]:t}:t.split(";").reduce(((t,e)=>{const[i,n]=e.split(/:(.*)/);return i&&!E(n)&&(t[i.trim()]=n.trim()),t}),{}):{}}catch{return{}}}function hn(t,e){return t===Boolean?A(e):t===Number?D(e):"list"===t?function(t){return u(t)?t:I(t)?t.split(cn).map((t=>C(t)?D(t):A(t.trim()))):[t]}(e):t===Object&&I(e)?ln(e):t?t(e):e}nn.events=nn.watch=nn.observe=nn.created=nn.beforeConnect=nn.connected=nn.beforeDisconnect=nn.disconnected=nn.destroy=on,nn.args=function(t,e){return!1!==e&&on(e||t)},nn.update=function(t,e){return F(on(t,g(e)?{read:e}:e),"order")},nn.props=function(t,e){if(u(e)){const t={};for(const i of e)t[i]=String;e=t}return nn.methods(t,e)},nn.computed=nn.methods=function(t,e){return e?t?{...t,...e}:e:t},nn.i18n=nn.data=function(t,e,i){return i?sn(t,e,i):e?t?function(i){return sn(t,e,i)}:e:t};const cn=/,(?![^(]*\))/;function dn(t,e="update"){t._connected&&t._updates.length&&(t._queued||(t._queued=new Set,Qe.read((()=>{t._connected&&function(t,e){for(const{read:i,write:n,events:s=[]}of t._updates){if(!e.has("update")&&!s.some((t=>e.has(t))))continue;let o;i&&(o=i.call(t,t._data,e),o&&v(o)&&p(t._data,o)),n&&!1!==o&&Qe.write((()=>{t._connected&&n.call(t,t._data,e)}))}}(t,t._queued),t._queued=null}))),t._queued.add(e.type||e))}function un(t){return wn(ci,t,"resize")}function fn(t){return wn(li,t)}function pn(t){return wn(ui,t)}function gn(t={}){return fn({handler:function(e,i){const{targets:n=this.$el,preload:s=5}=t;for(const t of P(g(n)?n(this):n))Oe('[loading="lazy"]',t).slice(0,s-1).forEach((t=>rt(t,"loading")));for(const t of e.filter((({isIntersecting:t})=>t)).map((({target:t})=>t)))i.unobserve(t)},...t})}function mn(t){return wn(((t,e)=>di(e)),t,"resize")}function vn(t){return wn(((t,e)=>({disconnect:Xt($n(t),"scroll",e,{passive:!0})})),t,"scroll")}function bn(t){return{observe:(t,e)=>({observe:V,unobserve:V,disconnect:Xt(t,ut,e,{passive:!0})}),handler(t){if(!ne(t))return;const e=se(t),i="tagName"in t.target?t.target:Ct(t.target);Gt(document,`${pt} ${vt} scroll`,(t=>{const{x:n,y:s}=se(t);("scroll"!==t.type&&i&&n&&Math.abs(e.x-n)>100||s&&Math.abs(e.y-s)>100)&&setTimeout((()=>{Zt(i,"swipe"),Zt(i,`swipe${function(t,e,i,n){return Math.abs(t-i)>=Math.abs(e-n)?t-i>0?"Left":"Right":e-n>0?"Up":"Down"}(e.x,e.y,n,s)}`)}))}))},...t}}function wn(t,e,i){return{observe:t,handler(){dn(this,i)},...e}}function $n(t){return P(t).map((t=>{const{ownerDocument:e}=t,i=Ai(t,!0);return i===e.scrollingElement?e:i}))}var xn={props:{margin:String,firstColumn:Boolean},data:{margin:"bdt-margin-small-top",firstColumn:"bdt-first-column"},observe:[pn({options:{childList:!0}}),pn({options:{attributes:!0,attributeFilter:["style"]},target:({$el:t})=>[t,...Dt(t)]}),un({target:({$el:t})=>[t,...Dt(t)]})],update:{read(){return{rows:yn(Dt(this.$el))}},write({rows:t}){for(const e of t)for(const i of e)it(i,this.margin,t[0]!==e),it(i,this.firstColumn,e[ht?e.length-1:0]===i)},events:["resize"]}};function yn(t){const e=[[]],i=t.some(((e,i)=>i&&t[i-1].offsetParent!==e.offsetParent));for(const n of t){if(!xt(n))continue;const t=Sn(n,i);for(let s=e.length-1;s>=0;s--){const o=e[s];if(!o[0]){o.push(n);break}const r=Sn(o[0],i);if(t.top>=r.bottom-1&&t.top!==r.top){e.push([n]);break}if(t.bottom-1>r.top||t.top===r.top){let e=o.length-1;for(;e>=0;e--){const n=Sn(o[e],i);if(t.left>=n.left)break}o.splice(e+1,0,n);break}if(0===s){e.unshift([n]);break}}}return e}function Sn(t,e=!1){let{offsetTop:i,offsetLeft:n,offsetHeight:s,offsetWidth:o}=t;return e&&([i,n]=je(t)),{top:i,left:n,bottom:i+s,right:n+o}}async function In(t,e,i){await Tn();let n=Dt(e);const s=n.map((t=>kn(t,!0))),o={...re(e,["height","padding"]),display:"block"},r=n.concat(e);await Promise.all(r.map(de.cancel)),re(r,"transitionProperty","none"),await t(),n=n.concat(Dt(e).filter((t=>!c(n,t)))),await Promise.resolve(),re(r,"transitionProperty","");const a=st(e,"style"),l=re(e,["height","padding"]),[h,d]=function(t,e,i){const n=e.map(((t,e)=>!(!Ct(t)||!(e in i))&&(i[e]?xt(t)?Cn(t):{opacity:0}:{opacity:xt(t)?1:0}))),s=n.map(((n,s)=>{const o=Ct(e[s])===t&&(i[s]||kn(e[s]));if(!o)return!1;if(n){if(!("opacity"in n)){const{opacity:t}=o;t%1?n.opacity=1:delete o.opacity}}else delete o.opacity;return o}));return[n,s]}(e,n,s),u=n.map((t=>({style:st(t,"style")})));n.forEach(((t,e)=>d[e]&&re(t,d[e]))),re(e,o),Zt(e,"scroll"),await Tn();const f=n.map(((t,n)=>Ct(t)===e&&de.start(t,h[n],i,"ease"))).concat(de.start(e,l,i,"ease"));try{await Promise.all(f),n.forEach(((t,i)=>{st(t,u[i]),Ct(t)===e&&re(t,"display",0===h[i].opacity?"none":"")})),st(e,"style",a)}catch{st(n,"style",""),function(t,e){for(const i in e)re(t,i,"")}(e,o)}}function kn(t,e){const i=re(t,"zIndex");return!!xt(t)&&{display:"",opacity:e?re(t,"opacity"):"0",pointerEvents:"none",position:"absolute",zIndex:"auto"===i?_t(t):i,...Cn(t)}}function Cn(t){const{height:e,width:i}=ze(t);return{height:e,width:i,transform:"",...Fe(t),...re(t,["marginTop","marginLeft"])}}function Tn(){return new Promise((t=>requestAnimationFrame(t)))}const En="bdt-transition-leave",An="bdt-transition-enter";function Dn(t,e,i,n=0){const s=_n(e,!0),o={opacity:1},r={opacity:0},a=t=>()=>s===_n(e)?t():Promise.reject(),l=a((async()=>{K(e,En),await Promise.all(Pn(e).map(((t,e)=>new Promise((s=>setTimeout((()=>de.start(t,r,i/2,"ease").then(s)),e*n)))))),Q(e,En)})),h=a((async()=>{const a=Le(e);K(e,An),t(),re(Dt(e),{opacity:0}),await Tn();const l=Dt(e),h=Le(e);re(e,"alignContent","flex-start"),Le(e,a);const c=Pn(e);re(l,r);const d=c.map((async(t,e)=>{await function(t){return new Promise((e=>setTimeout(e,t)))}(e*n),await de.start(t,o,i/2,"ease")}));a!==h&&d.push(de.start(e,{height:h},i/2+c.length*n,"ease")),await Promise.all(d).then((()=>{Q(e,An),s===_n(e)&&(re(e,{height:"",alignContent:""}),re(l,{opacity:""}),delete e.dataset.transition)}))}));return et(e,En)?Mn(e).then(h):et(e,An)?Mn(e).then(l).then(h):l().then(h)}function _n(t,e){return e&&(t.dataset.transition=1+_n(t)),D(t.dataset.transition)||0}function Mn(t){return Promise.all(Dt(t).filter(de.inProgress).map((t=>new Promise((e=>Gt(t,"transitionend transitioncanceled",e))))))}function Pn(t){return yn(Dt(t)).flat().filter(xt)}var On={props:{duration:Number,animation:Boolean},data:{duration:150,animation:"slide"},methods:{animate(t,e=this.$el){const i=this.animation;return("fade"===i?Dn:"delayed-fade"===i?(...t)=>Dn(...t,40):i?In:()=>(t(),Promise.resolve()))(t,e,this.duration).catch(V)}}};const Bn=9,Nn=27,zn=32,Hn=35,Fn=36,jn=37,Ln=38,Wn=39,qn=40;var Vn={mixins:[On],args:"target",props:{target:String,selActive:Boolean},data:{target:"",selActive:!1,attrItem:"bdt-filter-control",cls:"bdt-active",duration:250},computed:{children:({target:t},e)=>Oe(`${t} > *`,e),toggles:({attrItem:t},e)=>Oe(`[${t}],[data-${t}]`,e)},watch:{toggles(t){this.updateState();const e=Oe(this.selActive,this.$el);for(const i of t){!1!==this.selActive&&it(i,this.cls,c(e,i));const t=Xn(i);ve(t,"a")&&st(t,"role","button")}},children(t,e){e&&this.updateState()}},events:{name:"click keydown",delegate:({attrItem:t})=>`[${t}],[data-${t}]`,handler(t){"keydown"===t.type&&t.keyCode!==zn||t.target.closest("a,button")&&(t.preventDefault(),this.apply(t.current))}},methods:{apply(t){const e=this.getState(),i=Un(t,this.attrItem,this.getState());(function(t,e){return["filter","sort"].every((i=>B(t[i],e[i])))})(e,i)||this.setState(i)},getState(){return this.toggles.filter((t=>et(t,this.cls))).reduce(((t,e)=>Un(e,this.attrItem,t)),{filter:{"":""},sort:[]})},async setState(t,e=!0){t={filter:{"":""},sort:[],...t},Zt(this.$el,"beforeFilter",[this,t]);for(const e of this.toggles)it(e,this.cls,Yn(e,this.attrItem,t));await Promise.all(Oe(this.target,this.$el).map((i=>{const n=()=>function(t,e,i){const n=Object.values(t.filter).join("");for(const t of i)re(t,"display",n&&!Et(t,n)?"none":"");const[s,o]=t.sort;if(s){const t=function(t,e,i){return[...t].sort(((t,n)=>at(t,e).localeCompare(at(n,e),void 0,{numeric:!0})*("asc"===i||-1)))}(i,s,o);B(t,i)||xe(e,t)}}(t,i,Dt(i));return e?this.animate(n,i):n()}))),Zt(this.$el,"afterFilter",[this])},updateState(){Qe.write((()=>this.setState(this.getState(),!1)))}}};function Rn(t,e){return ln(at(t,e),["filter"])}function Un(t,e,i){const{filter:n,group:s,sort:o,order:r="asc"}=Rn(t,e);return(n||E(o))&&(s?n?(delete i.filter[""],i.filter[s]=n):(delete i.filter[s],(T(i.filter)||""in i.filter)&&(i.filter={"":n||""})):i.filter={"":n||""}),E(o)||(i.sort=[o,r]),i}function Yn(t,e,{filter:i={"":""},sort:[n,s]}){const{filter:o="",group:r="",sort:a,order:l="asc"}=Rn(t,e);return E(a)?r in i&&o===i[r]||!o&&r&&!(r in i)&&!i[""]:n===a&&s===l}function Xn(t){return Pe("a,button",t)||t}let Jn;function Gn(t){const e=Xt(t,"touchstart",(e=>{if(1!==e.targetTouches.length||Et(e.target,'input[type="range"'))return;let i=se(e).y;const n=Xt(t,"touchmove",(e=>{const n=se(e).y;n!==i&&(i=n,Ei(e.target).some((e=>{if(!t.contains(e))return!1;let{scrollHeight:i,clientHeight:n}=e;return n<i}))||e.preventDefault())}),{passive:!1});Gt(t,"scroll touchend touchcanel",n,{capture:!0})}),{passive:!0});if(Jn)return e;Jn=!0;const{scrollingElement:i}=document;return re(i,{overflowY:CSS.supports("overflow","clip")?"clip":"hidden",touchAction:"none",paddingRight:We(window)-i.clientWidth||""}),()=>{Jn=!1,e(),re(i,{overflowY:"",touchAction:"",paddingRight:""})}}var Zn={props:{container:Boolean},data:{container:!0},computed:{container({container:t}){return!0===t&&this.$container||t&&Pe(t)}}},Kn={props:{cls:Boolean,animation:"list",duration:Number,velocity:Number,origin:String,transition:String},data:{cls:!1,animation:[!1],duration:200,velocity:.2,origin:!1,transition:"ease",clsEnter:"bdt-togglable-enter",clsLeave:"bdt-togglable-leave"},computed:{hasAnimation:({animation:t})=>!!t[0],hasTransition:({animation:t})=>["slide","reveal"].some((e=>l(t[0],e)))},methods:{async toggleElement(t,e,i){try{return await Promise.all(P(t).map((t=>{const n=S(e)?e:!this.isToggled(t);if(!Zt(t,"before"+(n?"show":"hide"),[this]))return Promise.reject();const s=(g(i)?i:!1!==i&&this.hasAnimation?this.hasTransition?ts:es:Qn)(t,n,this),o=n?this.clsEnter:this.clsLeave;K(t,o),Zt(t,n?"show":"hide",[this]);const r=()=>{Q(t,o),Zt(t,n?"shown":"hidden",[this])};return s?s.then(r,(()=>(Q(t,o),Promise.reject()))):r()}))),!0}catch{return!1}},isToggled(t=this.$el){return!!et(t=M(t),this.clsEnter)||!et(t,this.clsLeave)&&(this.cls?et(t,this.cls.split(" ")[0]):xt(t))},_toggle(t,e){if(!t)return;let i;e=!!e,this.cls?(i=c(this.cls," ")||e!==et(t,this.cls),i&&it(t,this.cls,c(this.cls," ")?void 0:e)):(i=e===t.hidden,i&&(t.hidden=!e)),Oe("[autofocus]",t).some((t=>xt(t)?t.focus()||!0:t.blur())),i&&Zt(t,"toggled",[e,this])}}};function Qn(t,e,{_toggle:i}){return me.cancel(t),de.cancel(t),i(t,e)}async function ts(t,e,{animation:i,duration:n,velocity:s,transition:o,_toggle:r}){var a;const[l="reveal",h="top"]=(null==(a=i[0])?void 0:a.split("-"))||[],d=[["left","right"],["top","bottom"]],u=d[c(d[0],h)?0:1],f=u[1]===h,p=["width","height"][d.indexOf(u)],g=`margin-${u[0]}`,m=`margin-${h}`;let v=ze(t)[p];const b=de.inProgress(t);await de.cancel(t),e&&r(t,!0);const w=Object.fromEntries(["padding","border","width","height","minWidth","minHeight","overflowY","overflowX",g,m].map((e=>[e,t.style[e]]))),$=ze(t),x=_(re(t,g)),y=_(re(t,m)),S=$[p]+y;!b&&!e&&(v+=y);const[I]=Te(t,"<div>");re(I,{boxSizing:"border-box",height:$.height,width:$.width,...re(t,["overflow","padding","borderTop","borderRight","borderBottom","borderLeft","borderImage",m])}),re(t,{padding:0,border:0,minWidth:0,minHeight:0,[m]:0,width:$.width,height:$.height,overflow:"hidden",[p]:v});const k=v/S;n=(s*S+n)*(e?1-k:k);const C={[p]:e?S:0};f&&(re(t,g,S-v+x),C[g]=e?x:S+x),!f^"reveal"===l&&(re(I,g,-S+v),de.start(I,{[g]:e?0:-S},n,o));try{await de.start(t,C,n,o)}finally{re(t,w),Ee(I.firstChild),e||r(t,!1)}}function es(t,e,i){const{animation:n,duration:s,_toggle:o}=i;return e?(o(t,!0),me.in(t,n[0],s,i.origin)):me.out(t,n[1]||n[0],s,i.origin).then((()=>o(t,!1)))}const is=[];var ns={mixins:[Qi,Zn,Kn],props:{selPanel:String,selClose:String,escClose:Boolean,bgClose:Boolean,stack:Boolean,role:String},data:{cls:"bdt-open",escClose:!0,bgClose:!0,overlay:!0,stack:!1,role:"dialog"},computed:{panel:({selPanel:t},e)=>Pe(t,e),transitionElement(){return this.panel},bgClose({bgClose:t}){return t&&this.panel}},connected(){st(this.panel||this.$el,"role",this.role),this.overlay&&st(this.panel||this.$el,"aria-modal",!0)},beforeDisconnect(){c(is,this)&&this.toggleElement(this.$el,!1,!1)},events:[{name:"click",delegate:({selClose:t})=>`${t},a[href*="#"]`,handler(t){const{current:e,defaultPrevented:i}=t,{hash:n}=e;!i&&n&&Mt(e)&&!this.$el.contains(Pe(n))?this.hide():Et(e,this.selClose)&&(t.preventDefault(),this.hide())}},{name:"toggle",self:!0,handler(t){t.defaultPrevented||(t.preventDefault(),this.isToggled()===c(is,this)&&this.toggle())}},{name:"beforeshow",self:!0,handler(t){if(c(is,this))return!1;!this.stack&&is.length?(Promise.all(is.map((t=>t.hide()))).then(this.show),t.preventDefault()):is.push(this)}},{name:"show",self:!0,handler(){this.stack&&re(this.$el,"zIndex",_(re(this.$el,"zIndex"))+is.length);const t=[this.overlay&&os(this),this.overlay&&Gn(this.$el),this.bgClose&&rs(this),this.escClose&&as(this)];Gt(this.$el,"hidden",(()=>t.forEach((t=>t&&t()))),{self:!0}),K(document.documentElement,this.clsPage)}},{name:"shown",self:!0,handler(){kt(this.$el)||st(this.$el,"tabindex","-1"),Et(this.$el,":focus-within")||this.$el.focus()}},{name:"hidden",self:!0,handler(){c(is,this)&&is.splice(is.indexOf(this),1),re(this.$el,"zIndex",""),is.some((t=>t.clsPage===this.clsPage))||Q(document.documentElement,this.clsPage)}}],methods:{toggle(){return this.isToggled()?this.hide():this.show()},show(){return this.container&&Ct(this.$el)!==this.container?(xe(this.container,this.$el),new Promise((t=>requestAnimationFrame((()=>this.show().then(t)))))):this.toggleElement(this.$el,!0,ss)},hide(){return this.toggleElement(this.$el,!1,ss)}}};function ss(t,e,{transitionElement:i,_toggle:n}){return new Promise(((s,o)=>Gt(t,"show hide",(()=>{var r;null==(r=t._reject)||r.call(t),t._reject=o,n(t,e);const a=Gt(i,"transitionstart",(()=>{Gt(i,"transitionend transitioncancel",s,{self:!0}),clearTimeout(l)}),{self:!0}),l=setTimeout((()=>{a(),s()}),function(t){return t?h(t,"ms")?_(t):1e3*_(t):0}(re(i,"transitionDuration")))})))).then((()=>delete t._reject))}function os(t){return Xt(document,"focusin",(e=>{z(is)===t&&!t.$el.contains(e.target)&&t.$el.focus()}))}function rs(t){return Xt(document,ut,(({target:e})=>{z(is)!==t||t.overlay&&!t.$el.contains(e)||t.panel.contains(e)||Gt(document,`${pt} ${vt} scroll`,(({defaultPrevented:i,type:n,target:s})=>{!i&&n===pt&&e===s&&t.hide()}),!0)}))}function as(t){return Xt(document,"keydown",(e=>{27===e.keyCode&&z(is)===t&&t.hide()}))}var ls={slide:{show:t=>[{transform:cs(-100*t)},{transform:cs()}],percent:t=>hs(t),translate:(t,e)=>[{transform:cs(-100*e*t)},{transform:cs(100*e*(1-t))}]}};function hs(t){return Math.abs(new DOMMatrix(re(t,"transform")).m41/t.offsetWidth)}function cs(t=0,e="%"){return`translate3d(${t+=t?e:""}, 0, 0)`}function ds(t){return`scale3d(${t}, ${t}, 1)`}function us(t,e,i){Zt(t,Kt(e,!1,!1,i))}function fs(){let t;return{promise:new Promise((e=>t=e)),resolve:t}}var ps={props:{i18n:Object},data:{i18n:null},methods:{t(t,...e){var i,n,s;let o=0;return(null==(s=(null==(i=this.i18n)?void 0:i[t])||(null==(n=this.$options.i18n)?void 0:n[t]))?void 0:s.replace(/%s/g,(()=>e[o++]||"")))||""}}},gs={props:{autoplay:Boolean,autoplayInterval:Number,pauseOnHover:Boolean},data:{autoplay:!1,autoplayInterval:7e3,pauseOnHover:!0},connected(){st(this.list,"aria-live",this.autoplay?"off":"polite"),this.autoplay&&this.startAutoplay()},disconnected(){this.stopAutoplay()},update(){st(this.slides,"tabindex","-1")},events:[{name:"visibilitychange",el:()=>document,filter:({autoplay:t})=>t,handler(){document.hidden?this.stopAutoplay():this.startAutoplay()}}],methods:{startAutoplay(){this.stopAutoplay(),this.interval=setInterval((()=>{this.stack.length||this.draggable&&Et(this.$el,":focus-within")&&!Et(this.$el,":focus")||this.pauseOnHover&&Et(this.$el,":hover")||this.show("next")}),this.autoplayInterval)},stopAutoplay(){clearInterval(this.interval)}}};const ms={passive:!1,capture:!0},vs={passive:!0,capture:!0},bs="touchmove mousemove",ws="touchend touchcancel mouseup click input scroll",$s=t=>t.preventDefault();var xs={props:{draggable:Boolean},data:{draggable:!0,threshold:10},created(){for(const t of["start","move","end"]){const e=this[t];this[t]=t=>{const i=se(t).x*(ht?-1:1);this.prevPos=i===this.pos?this.prevPos:this.pos,this.pos=i,e(t)}}},events:[{name:"touchstart mousedown",passive:!0,delegate:({selList:t})=>`${t} > *`,handler(t){!this.draggable||this.parallax||!ne(t)&&function(t){return"none"!==re(t,"userSelect")&&f(t.childNodes).some((t=>3===t.nodeType&&t.textContent.trim()))}(t.target)||t.target.closest(yt)||t.button>0||this.length<2||this.start(t)}},{name:"dragstart",handler(t){t.preventDefault()}},{name:bs,el:({list:t})=>t,handler:V,...ms}],methods:{start(){this.drag=this.pos,this._transitioner?(this.percent=this._transitioner.percent(),this.drag+=this._transitioner.getDistance()*this.percent*this.dir,this._transitioner.cancel(),this._transitioner.translate(this.percent),this.dragging=!0,this.stack=[]):this.prevIndex=this.index,Xt(document,bs,this.move,ms),Xt(document,ws,this.end,vs),re(this.list,"userSelect","none")},move(t){const e=this.pos-this.drag;if(0===e||this.prevPos===this.pos||!this.dragging&&Math.abs(e)<this.threshold)return;this.dragging||Xt(this.list,"click",$s,ms),t.cancelable&&t.preventDefault(),this.dragging=!0,this.dir=e<0?1:-1;let{slides:i,prevIndex:n}=this,s=Math.abs(e),o=this.getIndex(n+this.dir),r=ys.call(this,n,o);for(;o!==n&&s>r;)this.drag-=r*this.dir,n=o,s-=r,o=this.getIndex(n+this.dir),r=ys.call(this,n,o);this.percent=s/r;const a=i[n],l=i[o],h=this.index!==o,d=n===o;let u;for(const t of[this.index,this.prevIndex])c([o,n],t)||(Zt(i[t],"itemhidden",[this]),d&&(u=!0,this.prevIndex=n));(this.index===n&&this.prevIndex!==n||u)&&Zt(i[this.index],"itemshown",[this]),h&&(this.prevIndex=n,this.index=o,d||(Zt(a,"beforeitemhide",[this]),Zt(a,"itemhide",[this])),Zt(l,"beforeitemshow",[this]),Zt(l,"itemshow",[this])),this._transitioner=this._translate(Math.abs(this.percent),a,!d&&l)},end(){if(Jt(document,bs,this.move,ms),Jt(document,ws,this.end,vs),this.dragging)if(this.dragging=null,this.index===this.prevIndex)this.percent=1-this.percent,this.dir*=-1,this._show(!1,this.index,!0),this._transitioner=null;else{const t=(ht?this.dir*(ht?1:-1):this.dir)<0==this.prevPos>this.pos;this.index=t?this.index:this.prevIndex,t&&(this.percent=1-this.percent),this.show(this.dir>0&&!t||this.dir<0&&t?"next":"previous",!0)}setTimeout((()=>Jt(this.list,"click",$s,ms))),re(this.list,{userSelect:""}),this.drag=this.percent=null}}};function ys(t,e){return this._getTransitioner(t,t!==e&&e).getDistance()||this.slides[t].offsetWidth}function Ss(t,e,i){t._watches.push({name:i,...v(e)?e:{handler:e}})}const Is={subtree:!0,childList:!0};function ks(t,e,n){t._hasComputed=!0,Object.defineProperty(t,e,{enumerable:!0,get(){const{_computed:s,$props:o,$el:r}=t;if(!i(s,e)&&(s[e]=(n.get||n).call(t,o,r),n.observe&&t._computedObserver)){const e=n.observe.call(t,o);t._computedObserver.observe(["~","+","-"].includes(e[0])?r.parentElement:r.getRootNode(),Is)}return s[e]},set(i){const{_computed:s}=t;s[e]=n.set?n.set.call(t,i):i,E(s[e])&&delete s[e]}})}function Cs(t){t._hasComputed&&(function(t,e){t._updates.unshift(e)}(t,{read:()=>function(t,e){for(const{name:n,handler:s,immediate:o=!0}of t._watches)(t._initial&&o||i(e,n)&&!B(e[n],t[n]))&&s.call(t,t[n],e[n]);t._initial=!1}(t,Ts(t)),events:["resize","computed"]}),t._computedObserver=ui(t.$el,(()=>dn(t,"computed")),Is))}function Ts(t){const e={...t._computed};return t._computed={},e}function Es(t,e,i){let{name:n,el:s,handler:o,capture:r,passive:a,delegate:l,filter:h,self:c}=v(e)?e:{name:i,handler:e};s=g(s)?s.call(t,t):s||t.$el,!(!s||u(s)&&!s.length||h&&!h.call(t,t))&&t._events.push(Xt(s,n,l?I(l)?l:l.call(t,t):null,I(o)?t[o]:o.bind(t),{passive:a,capture:r,self:c}))}function As(t,...e){t._observers.push(...e)}function Ds(t,e){let{observe:n,target:s=t.$el,handler:o,options:r,filter:a,args:l}=e;if(a&&!a.call(t,t))return;const h=`_observe${t._observers.length}`;g(s)&&!i(t,h)&&ks(t,h,(()=>{const e=s.call(t,t);return u(e)?P(e):e})),o=I(o)?t[o]:o.bind(t),g(r)&&(r=r.call(t,t));const c=n(i(t,h)?t[h]:s,o,r,l);g(s)&&u(t[h])&&Ss(t,{handler:_s(c,r),immediate:!1},h),As(t,c)}function _s(t,e){return(i,n)=>{for(const e of n)c(i,e)||(t.unobserve?t.unobserve(e):t.observe&&t.disconnect());for(const s of i)(!c(n,s)||!t.unobserve)&&t.observe(s,e)}}function Ms(t){const e={},{args:i=[],props:n={},el:o,id:a}=t;if(!n)return e;for(const t in n){const i=s(t);let r=at(o,i);E(r)||(r=n[t]===Boolean&&""===r||hn(n[t],r),("target"!==i||!l(r,"_"))&&(e[t]=r))}const h=ln(at(o,a),i);for(const t in h){const i=r(t);E(n[i])||(e[i]=hn(n[i],h[t]))}return e}const Ps=Z(((t,e)=>{const i=Object.keys(e),n=i.concat(t).map((t=>[s(t),`data-${s(t)}`])).flat();return{attributes:i,filter:n}}));function Os(t,e){var i;null==(i=t.$options[e])||i.forEach((e=>e.call(t)))}function Bs(t){t._connected||(function(t){const{$options:e,$props:n}=t,s=Ms(e);p(n,s);const{computed:o,methods:r}=e;for(let e in n)e in s&&(!o||!i(o,e))&&(!r||!i(r,e))&&(t[e]=n[e])}(t),Os(t,"beforeConnect"),t._connected=!0,function(t){t._events=[];for(const e of t.$options.events||[])if(i(e,"handler"))Es(t,e);else for(const i in e)Es(t,e[i],i)}(t),function(t){t._data={},t._updates=[...t.$options.update||[]]}(t),function(t){t._watches=[];for(const e of t.$options.watch||[])for(const[i,n]of Object.entries(e))Ss(t,n,i);t._initial=!0}(t),function(t){t._observers=[];for(const e of t.$options.observe||[])Ds(t,e)}(t),function(t){const{$options:e,$props:i}=t,{id:n,props:s,el:o}=e;if(!s)return;const{attributes:a,filter:l}=Ps(n,s),h=new MutationObserver((s=>{const o=Ms(e);s.some((({attributeName:t})=>{const e=t.replace("data-","");return(e===n?a:[r(e),r(t)]).some((t=>!E(o[t])&&o[t]!==i[t]))}))&&t.$reset()}));h.observe(o,{attributes:!0,attributeFilter:l}),As(t,h)}(t),Cs(t),Os(t,"connected"),dn(t))}function Ns(t){t._connected&&(Os(t,"beforeDisconnect"),function(t){t._events.forEach((t=>t())),delete t._events}(t),function(t){t._data=null}(t),function(t){for(const e of t._observers)e.disconnect()}(t),function(t){var e;null==(e=t._computedObserver)||e.disconnect(),delete t._computedObserver,Ts(t)}(t),Os(t,"disconnected"),t._connected=!1)}let zs=0;function Hs(t,e={}){e.data=function({data:t={}},{args:e=[],props:i={}}){u(t)&&(t=t.slice(0,e.length).reduce(((t,i,n)=>(v(i)?p(t,i):t[e[n]]=i,t)),{}));for(const e in t)E(t[e])?delete t[e]:i[e]&&(t[e]=hn(i[e],t[e]));return t}(e,t.constructor.options),t.$options=an(t.constructor.options,e,t),t.$props={},t._uid=zs++,function(t){const{data:e={}}=t.$options;for(const i in e)t.$props[i]=t[i]=e[i]}(t),function(t){const{methods:e}=t.$options;if(e)for(const i in e)t[i]=e[i].bind(t)}(t),function(t){const{computed:e}=t.$options;if(t._computed={},e)for(const i in e)ks(t,i,e[i])}(t),Os(t,"created"),e.el&&t.$mount(e.el)}const Fs=function(t){Hs(this,t)};Fs.util=Ki,Fs.options={},Fs.version="3.21.7";const js="__uikit__",Ls={};function Ws(t,e){var i,n;const o="bdt-"+s(t);if(!e)return Ls[o].options||(Ls[o]=Fs.extend(Ls[o])),Ls[o];t=r(t),Fs[t]=(e,i)=>qs(t,e,i);const a=null!=(i=e.options)?i:{...e};return a.id=o,a.name=t,null==(n=a.install)||n.call(a,Fs,a,t),Fs._initialized&&!a.functional&&requestAnimationFrame((()=>qs(t,`[${o}],[data-${o}]`))),Ls[o]=a}function qs(t,e,i,...n){const s=Ws(t);return s.options.functional?new s({data:v(e)?e:[e,i,...n]}):e?Oe(e).map(o)[0]:o();function o(e){const n=Rs(e,t);if(n){if(!i)return n;n.$destroy()}return new s({el:e,data:i})}}function Vs(t){return(null==t?void 0:t[js])||{}}function Rs(t,e){return Vs(t)[e]}function Us(t,e){t=t?M(t):document.body;for(const i of At(t).reverse())Ys(i,e);Me(t,(t=>Ys(t,e)))}function Ys(t,e){const i=Vs(t);for(const t in i)dn(i[t],e)}let Xs=1;function Js(t,e=null){return(null==e?void 0:e.id)||`${t.$options.id}-${Xs++}`}var Gs={i18n:{next:"Next slide",previous:"Previous slide",slideX:"Slide %s",slideLabel:"%s of %s",role:"String"},data:{selNav:!1,role:"region"},computed:{nav:({selNav:t},e)=>Pe(t,e),navChildren(){return Dt(this.nav)},selNavItem:({attrItem:t})=>`[${t}],[data-${t}]`,navItems(t,e){return Oe(this.selNavItem,e)}},watch:{nav(t,e){st(t,"role","tablist"),this.padNavitems(),e&&this.$emit()},list(t){ve(t,"ul")&&st(t,"role","presentation")},navChildren(t){st(t,"role","presentation"),this.padNavitems(),this.updateNav()},navItems(t){for(const e of t){const t=at(e,this.attrItem),i=Pe("a,button",e)||e;let n,s=null;if(C(t)){const e=D(t),o=this.slides[e];o&&(o.id||(o.id=Js(this,o)),s=o.id),n=this.t("slideX",_(t)+1),st(i,"role","tab")}else this.list&&(this.list.id||(this.list.id=Js(this,this.list)),s=this.list.id),n=this.t(t);st(i,{"aria-controls":s,"aria-label":st(i,"aria-label")||n})}},slides(t){t.forEach(((t,e)=>st(t,{role:this.nav?"tabpanel":"group","aria-label":this.t("slideLabel",e+1,this.length),"aria-roledescription":this.nav?null:"slide"}))),this.padNavitems()}},connected(){st(this.$el,{role:this.role,"aria-roledescription":"carousel"})},update:[{write(){this.navItems.concat(this.nav).forEach((t=>t&&(t.hidden=!this.maxIndex))),this.updateNav()},events:["resize"]}],events:[{name:"click keydown",delegate:({selNavItem:t})=>t,filter:({parallax:t})=>!t,handler(t){t.target.closest("a,button")&&("click"===t.type||t.keyCode===zn)&&(t.preventDefault(),this.show(at(t.current,this.attrItem)))}},{name:"itemshow",handler:"updateNav"},{name:"keydown",delegate:({selNavItem:t})=>t,filter:({parallax:t})=>!t,handler(t){const{current:e,keyCode:i}=t;if(!C(at(e,this.attrItem)))return;let n=i===Fn?0:i===Hn?"last":i===jn?"previous":i===Wn?"next":-1;~n&&(t.preventDefault(),this.show(n))}}],methods:{updateNav(){const t=this.getValidIndex();for(const e of this.navItems){const i=at(e,this.attrItem),n=Pe("a,button",e)||e;if(C(i)){const s=D(i)===t;it(e,this.clsActive,s),it(n,"bdt-disabled",this.parallax),st(n,{"aria-selected":s,tabindex:s&&!this.parallax?null:-1}),s&&n&&Et(Ct(e),":focus-within")&&n.focus()}else it(e,"bdt-invisible",this.finite&&("previous"===i&&0===t||"next"===i&&t>=this.maxIndex))}},padNavitems(){if(!this.nav)return;const t=[];for(let e=0;e<this.length;e++){const i=`${this.attrItem}="${e}"`;t[e]=this.navChildren.findLast((t=>t.matches(`[${i}]`)))||Pe(`<li ${i}><a href></a></li>`)}B(t,this.navChildren)||we(this.nav,t)}}};var Zs={mixins:[gs,xs,Gs,ps],props:{clsActivated:String,easing:String,index:Number,finite:Boolean,velocity:Number},data:()=>({easing:"ease",finite:!1,velocity:1,index:0,prevIndex:-1,stack:[],percent:0,clsActive:"bdt-active",clsActivated:"",clsEnter:"bdt-slide-enter",clsLeave:"bdt-slide-leave",clsSlideActive:"bdt-slide-active",Transitioner:!1,transitionOptions:{}}),connected(){this.prevIndex=-1,this.index=this.getValidIndex(this.$props.index),this.stack=[]},disconnected(){Q(this.slides,this.clsActive)},computed:{duration:({velocity:t},e)=>Ks(e.offsetWidth/t),list:({selList:t},e)=>Pe(t,e),maxIndex(){return this.length-1},slides(){return Dt(this.list)},length(){return this.slides.length}},watch:{slides(t,e){e&&this.$emit()}},events:{itemshow({target:t}){K(t,this.clsEnter,this.clsSlideActive)},itemshown({target:t}){Q(t,this.clsEnter)},itemhide({target:t}){K(t,this.clsLeave)},itemhidden({target:t}){Q(t,this.clsLeave,this.clsSlideActive)}},methods:{show(t,e=!1){var i;if(this.dragging||!this.length||this.parallax)return;const{stack:n}=this,s=e?0:n.length,o=()=>{n.splice(s,1),n.length&&this.show(n.shift(),!0)};if(n[e?"unshift":"push"](t),!e&&n.length>1)return void(2===n.length&&(null==(i=this._transitioner)||i.forward(Math.min(this.duration,200))));const r=this.getIndex(this.index),a=et(this.slides,this.clsActive)&&this.slides[r],l=this.getIndex(t,this.index),h=this.slides[l];if(a===h)return void o();if(this.dir=function(t,e){return"next"===t?1:"previous"===t||t<e?-1:1}(t,r),this.prevIndex=r,this.index=l,a&&!Zt(a,"beforeitemhide",[this])||!Zt(h,"beforeitemshow",[this,a]))return this.index=this.prevIndex,void o();const c=this._show(a,h,e).then((()=>{a&&Zt(a,"itemhidden",[this]),Zt(h,"itemshown",[this]),n.shift(),this._transitioner=null,n.length&&requestAnimationFrame((()=>n.length&&this.show(n.shift(),!0)))}));return a&&Zt(a,"itemhide",[this]),Zt(h,"itemshow",[this]),c},getIndex(t=this.index,e=this.index){return q(G(t,this.slides,e,this.finite),0,Math.max(0,this.maxIndex))},getValidIndex(t=this.index,e=this.prevIndex){return this.getIndex(t,e)},async _show(t,e,i){if(this._transitioner=this._getTransitioner(t,e,this.dir,{easing:i?e.offsetWidth<600?"cubic-bezier(0.25, 0.46, 0.45, 0.94)":"cubic-bezier(0.165, 0.84, 0.44, 1)":this.easing,...this.transitionOptions}),!i&&!t)return void this._translate(1);const{length:n}=this.stack;return this._transitioner[n>1?"forward":"show"](n>1?Math.min(this.duration,75+75/(n-1)):this.duration,this.percent)},_translate(t,e=this.prevIndex,i=this.index){const n=this._getTransitioner(e!==i&&e,i);return n.translate(t),n},_getTransitioner(t=this.prevIndex,e=this.index,i=this.dir||1,n=this.transitionOptions){return new this.Transitioner(k(t)?this.slides[t]:t,k(e)?this.slides[e]:e,i*(ht?-1:1),n)}}};function Ks(t){return.5*t+300}var Qs={mixins:[Zs],props:{animation:String},data:{animation:"slide",clsActivated:"bdt-transition-active",Animations:ls,Transitioner:function(t,e,i,{animation:n,easing:s}){const{percent:o,translate:r,show:a=V}=n,l=a(i),{promise:h,resolve:c}=fs();return{dir:i,show(n,o=0,r){const a=r?"linear":s;return n-=Math.round(n*q(o,-1,1)),this.translate(o),us(e,"itemin",{percent:o,duration:n,timing:a,dir:i}),us(t,"itemout",{percent:1-o,duration:n,timing:a,dir:i}),Promise.all([de.start(e,l[1],n,a),de.start(t,l[0],n,a)]).then((()=>{this.reset(),c()}),V),h},cancel:()=>de.cancel([e,t]),reset(){for(const i in l[0])re([e,t],i,"")},async forward(t,e=this.percent()){return await this.cancel(),this.show(t,e,!0)},translate(n){this.reset();const s=r(n,i);re(e,s[1]),re(t,s[0]),us(e,"itemtranslatein",{percent:n,dir:i}),us(t,"itemtranslateout",{percent:1-n,dir:i})},percent:()=>o(t||e,e,i),getDistance:()=>null==t?void 0:t.offsetWidth}}},computed:{animation:({animation:t,Animations:e})=>({...e[t]||e.slide,name:t}),transitionOptions(){return{animation:this.animation}}},observe:un(),events:{beforeitemshow({target:t}){K(t,this.clsActive)},itemshown({target:t}){K(t,this.clsActivated)},itemhidden({target:t}){Q(t,this.clsActive,this.clsActivated)}}},to={...ls,fade:{show:()=>[{opacity:0},{opacity:1}],percent:t=>1-re(t,"opacity"),translate:t=>[{opacity:1-t},{opacity:t}]},scale:{show:()=>[{opacity:0,transform:ds(.8)},{opacity:1,transform:ds(1)}],percent:t=>1-re(t,"opacity"),translate:t=>[{opacity:1-t,transform:ds(1-.2*t)},{opacity:t,transform:ds(.8+.2*t)}]}},eo={mixins:[ns,Qs],functional:!0,props:{delayControls:Number,preload:Number,videoAutoplay:Boolean,template:String},data:()=>({preload:1,videoAutoplay:!1,delayControls:3e3,items:[],cls:"bdt-open",clsPage:"bdt-lightbox-page",selList:".bdt-lightbox-items",attrItem:"bdt-lightbox-item",selClose:".bdt-close-large",selCaption:".bdt-lightbox-caption",pauseOnHover:!1,velocity:2,Animations:to,template:'<div class="bdt-lightbox bdt-overflow-hidden"> <div class="bdt-lightbox-items"></div> <div class="bdt-lightbox-toolbar bdt-position-top bdt-text-right bdt-transition-slide-top bdt-transition-opaque"> <button class="bdt-lightbox-toolbar-icon bdt-close-large" type="button" bdt-close></button> </div> <a class="bdt-lightbox-button bdt-position-center-left bdt-position-medium bdt-transition-fade" href bdt-slidenav-previous bdt-lightbox-item="previous"></a> <a class="bdt-lightbox-button bdt-position-center-right bdt-position-medium bdt-transition-fade" href bdt-slidenav-next bdt-lightbox-item="next"></a> <div class="bdt-lightbox-toolbar bdt-lightbox-caption bdt-position-bottom bdt-text-center bdt-transition-slide-bottom bdt-transition-opaque"></div> </div>'}),created(){const t=Pe(this.template),e=Pe(this.selList,t);this.items.forEach((()=>xe(e,"<div>")));const i=Pe("[bdt-close]",t),n=this.t("close");i&&n&&(i.dataset.i18n=JSON.stringify({label:n})),this.$mount(xe(this.container,t))},events:[{name:`${ft} ${ut} keydown`,handler:"showControls"},{name:"click",self:!0,delegate:({selList:t})=>`${t} > *`,handler(t){t.defaultPrevented||this.hide()}},{name:"shown",self:!0,handler:"showControls"},{name:"hide",self:!0,handler(){this.hideControls(),Q(this.slides,this.clsActive),de.stop(this.slides)}},{name:"hidden",self:!0,handler(){this.$destroy(!0)}},{name:"keyup",el:()=>document,handler({keyCode:t}){if(!this.isToggled(this.$el)||!this.draggable)return;let e=-1;t===jn?e="previous":t===Wn?e="next":t===Fn?e=0:t===Hn&&(e="last"),~e&&this.show(e)}},{name:"beforeitemshow",handler(t){this.isToggled()||(this.draggable=!1,t.preventDefault(),this.toggleElement(this.$el,!0,!1),this.animation=to.scale,Q(t.target,this.clsActive),this.stack.splice(1,0,this.index))}},{name:"itemshow",handler(){we(Pe(this.selCaption,this.$el),this.getItem().caption||"");for(let t=-this.preload;t<=this.preload;t++)this.loadItem(this.index+t)}},{name:"itemshown",handler(){this.draggable=this.$props.draggable}},{name:"itemload",async handler(t,e){const{source:i,type:n,alt:s="",poster:o,attrs:r={}}=e;if(this.setItem(e,"<span bdt-spinner></span>"),!i)return;let a;const l={allowfullscreen:"",style:"max-width: 100%; box-sizing: border-box;","bdt-responsive":"","bdt-video":`${this.videoAutoplay}`};if("image"===n||i.match(/\.(avif|jpe?g|jfif|a?png|gif|svg|webp)($|\?)/i)){const t=io("img",{src:i,alt:s,...r});Xt(t,"load",(()=>this.setItem(e,t))),Xt(t,"error",(()=>this.setError(e)))}else if("video"===n||i.match(/\.(mp4|webm|ogv)($|\?)/i)){const t=io("video",{src:i,poster:o,controls:"",playsinline:"","bdt-video":`${this.videoAutoplay}`,...r});Xt(t,"loadedmetadata",(()=>this.setItem(e,t))),Xt(t,"error",(()=>this.setError(e)))}else if("iframe"===n||i.match(/\.(html|php)($|\?)/i))this.setItem(e,io("iframe",{src:i,allowfullscreen:"",class:"bdt-lightbox-iframe",...r}));else if(a=i.match(/\/\/(?:.*?youtube(-nocookie)?\..*?(?:[?&]v=|\/shorts\/)|youtu\.be\/)([\w-]{11})[&?]?(.*)?/))this.setItem(e,io("iframe",{src:`https://www.youtube${a[1]||""}.com/embed/${a[2]}${a[3]?`?${a[3]}`:""}`,width:1920,height:1080,...l,...r}));else if(a=i.match(/\/\/.*?vimeo\.[a-z]+\/(\d+)[&?]?(.*)?/))try{const{height:t,width:n}=await(await fetch(`https://vimeo.com/api/oembed.json?maxwidth=1920&url=${encodeURI(i)}`,{credentials:"omit"})).json();this.setItem(e,io("iframe",{src:`https://player.vimeo.com/video/${a[1]}${a[2]?`?${a[2]}`:""}`,width:n,height:t,...l,...r}))}catch{this.setError(e)}}}],methods:{loadItem(t=this.index){const e=this.getItem(t);this.getSlide(e).childElementCount||Zt(this.$el,"itemload",[e])},getItem(t=this.index){return this.items[G(t,this.slides)]},setItem(t,e){Zt(this.$el,"itemloaded",[this,we(this.getSlide(t),e)])},getSlide(t){return this.slides[this.items.indexOf(t)]},setError(t){this.setItem(t,'<span bdt-icon="icon: bolt; ratio: 2"></span>')},showControls(){clearTimeout(this.controlsTimer),this.controlsTimer=setTimeout(this.hideControls,this.delayControls),K(this.$el,"bdt-active","bdt-transition-active")},hideControls(){Q(this.$el,"bdt-active","bdt-transition-active")}}};function io(t,e){const i=De(`<${t}>`);return st(i,e),i}var no={install:function(t,e){t.lightboxPanel||t.component("lightboxPanel",eo),p(e.props,t.component("lightboxPanel").options.props)},props:{toggle:String},data:{toggle:"a"},computed:{toggles:({toggle:t},e)=>Oe(t,e)},watch:{toggles(t){this.hide();for(const e of t)ve(e,"a")&&st(e,"role","button")}},disconnected(){this.hide()},events:{name:"click",delegate:({toggle:t})=>`${t}:not(.bdt-disabled)`,handler(t){t.defaultPrevented||(t.preventDefault(),this.show(t.current))}},methods:{show(t){const e=L(this.toggles.map(so),"source");if(x(t)){const{source:i}=so(t);t=d(e,(({source:t})=>i===t))}return this.panel=this.panel||this.$create("lightboxPanel",{...this.$props,items:e}),Xt(this.panel.$el,"hidden",(()=>this.panel=null)),this.panel.show(t)},hide(){var t;return null==(t=this.panel)?void 0:t.hide()}}};function so(t){const e={};for(const i of["href","caption","type","poster","alt","attrs"])e["href"===i?"source":i]=at(t,i);return e.attrs=ln(e.attrs),e}var oo={mixins:[Zn],functional:!0,args:["message","status"],data:{message:"",status:"",timeout:5e3,group:"",pos:"top-center",clsContainer:"bdt-notification",clsClose:"bdt-notification-close",clsMsg:"bdt-notification-message"},install:function(t){t.notification.closeAll=function(e,i){Me(document.body,(n=>{const s=t.getComponent(n,"notification");s&&(!e||e===s.group)&&s.close(i)}))}},computed:{marginProp:({pos:t})=>`margin-${t.match(/[a-z]+(?=-)/)[0]}`,startProps(){return{opacity:0,[this.marginProp]:-this.$el.offsetHeight}}},created(){const t=`${this.clsContainer}-${this.pos}`,e=`data-${this.clsContainer}-container`,i=Pe(`.${t}[${e}]`,this.container)||xe(this.container,`<div class="${this.clsContainer} ${t}" ${e}></div>`);this.$mount(xe(i,`<div class="${this.clsMsg}${this.status?` ${this.clsMsg}-${this.status}`:""}" role="alert"> <a href class="${this.clsClose}" data-bdt-close></a> <div>${this.message}</div> </div>`))},async connected(){const t=_(re(this.$el,this.marginProp));await de.start(re(this.$el,this.startProps),{opacity:1,[this.marginProp]:t}),this.timeout&&(this.timer=setTimeout(this.close,this.timeout))},events:{click(t){t.target.closest('a[href="#"],a[href=""]')&&t.preventDefault(),this.close()},[gt](){this.timer&&clearTimeout(this.timer)},[mt](){this.timeout&&(this.timer=setTimeout(this.close,this.timeout))}},methods:{async close(t){this.timer&&clearTimeout(this.timer),t||await de.start(this.$el,this.startProps),(t=>{const e=Ct(t);Zt(t,"close",[this]),ke(t),null!=e&&e.hasChildNodes()||ke(e)})(this.$el)}}};var ro={props:{media:Boolean},data:{media:!1},connected(){const t=function(t,e){if(I(t))if(l(t,"@"))t=_(re(e,`--bdt-breakpoint-${t.slice(1)}`));else if(isNaN(t))return t;return t&&C(t)?`(min-width: ${t}px)`:""}(this.media,this.$el);if(this.matchMedia=!0,t){this.mediaObj=window.matchMedia(t);const e=()=>{this.matchMedia=this.mediaObj.matches,Zt(this.$el,Kt("mediachange",!1,!0,[this.mediaObj]))};this.offMediaObj=Xt(this.mediaObj,"change",(()=>{e(),this.$emit("resize")})),e()}},disconnected(){var t;null==(t=this.offMediaObj)||t.call(this)}};function ao(t){return xt(t)?Math.ceil(Math.max(0,...Oe("[stroke]",t).map((t=>{var e;return(null==(e=t.getTotalLength)?void 0:e.call(t))||0})))):0}const lo={x:uo,y:uo,rotate:uo,scale:uo,color:fo,backgroundColor:fo,borderColor:fo,blur:po,hue:po,fopacity:po,grayscale:po,invert:po,saturate:po,sepia:po,opacity:function(t,e,i){return 1===i.length&&i.unshift(Co(e,t,"")),i=xo(i),(e,n)=>{e[t]=So(i,n)}},stroke:function(t,e,i){1===i.length&&i.unshift(0);const n=ko(i),s=ao(e);return(i=xo(i.reverse(),(t=>(t=_(t),"%"===n?t*s/100:t)))).some((([t])=>t))?(re(e,"strokeDasharray",s),(t,e)=>{t.strokeDashoffset=So(i,e)}):V},bgx:go,bgy:go},{keys:ho}=Object;var co={mixins:[ro],props:To(ho(lo),"list"),data:To(ho(lo),void 0),computed:{props(t,e){const i={};for(const e in t)e in lo&&!E(t[e])&&(i[e]=t[e].slice());const n={};for(const t in i)n[t]=lo[t](t,e,i[t],i);return n}},events:{load(){this.$emit()}},methods:{reset(){for(const t in this.getCss(0))re(this.$el,t,"")},getCss(t){const e={};for(const i in this.props)this.props[i](e,q(t));return e.willChange=Object.keys(e).map(ae).join(","),e}}};function uo(t,e,i){let n,s=ko(i)||{x:"px",y:"px",rotate:"deg"}[t]||"";return"x"===t||"y"===t?(t=`translate${a(t)}`,n=t=>_(_(t).toFixed("px"===s?0:6))):"scale"===t&&(s="",n=t=>{var i;return ko([t])?Ue(t,"width",e,!0)/e["offset"+(null!=(i=t.endsWith)&&i.call(t,"vh")?"Height":"Width")]:_(t)}),1===i.length&&i.unshift("scale"===t?1:0),i=xo(i,n),(e,n)=>{e.transform=`${e.transform||""} ${t}(${So(i,n)}${s})`}}function fo(t,e,i){return 1===i.length&&i.unshift(Co(e,t,"")),i=xo(i,(t=>function(t,e){return Co(t,"color",e).split(/[(),]/g).slice(1,-1).concat(1).slice(0,4).map(_)}(e,t))),(e,n)=>{const[s,o,r]=yo(i,n),a=s.map(((t,e)=>(t+=r*(o[e]-t),3===e?_(t):parseInt(t,10)))).join(",");e[t]=`rgba(${a})`}}function po(t,e,i){1===i.length&&i.unshift(0);const n=ko(i)||{blur:"px",hue:"deg"}[t]||"%";return t={fopacity:"opacity",hue:"hue-rotate"}[t]||t,i=xo(i),(e,s)=>{const o=So(i,s);e.filter=`${e.filter||""} ${t}(${o+n})`}}function go(t,e,i,n){1===i.length&&i.unshift(0);const s="bgy"===t?"height":"width";n[t]=xo(i,(t=>Ue(t,s,e)));const o=["bgx","bgy"].filter((t=>t in n));if(2===o.length&&"bgx"===t)return V;if("cover"===Co(e,"backgroundSize",""))return function(t,e,i,n){const s=function(t){const e=re(t,"backgroundImage").replace(/^none|url\(["']?(.+?)["']?\)$/,"$1");if(wo[e])return wo[e];const i=new Image;return!e||(i.src=e,i.naturalWidth||bo[e])?wo[e]=$o(i):(Gt(i,"error load",(()=>{wo[e]=$o(i),Zt(t,Kt("load",!1))})),bo[e]=!0,$o(i))}(e);if(!s.width)return V;const o={width:e.offsetWidth,height:e.offsetHeight},r=["bgx","bgy"].filter((t=>t in n)),a={};for(const t of r){const e=n[t].map((([t])=>t)),i=Math.min(...e),s=Math.max(...e),r=e.indexOf(i)<e.indexOf(s),l=s-i;a[t]=(r?-l:0)-(r?i:s)+"px",o["bgy"===t?"height":"width"]+=l}const l=J.cover(s,o);for(const t of r){const i="bgy"===t?"height":"width",n=l[i]-o[i];a[t]=`max(${mo(e,t)},-${n}px) + ${a[t]}`}const h=vo(r,a,n);return(t,e)=>{h(t,e),t.backgroundSize=`${l.width}px ${l.height}px`,t.backgroundRepeat="no-repeat"}}(0,e,0,n);const r={};for(const t of o)r[t]=mo(e,t);return vo(o,r,n)}function mo(t,e){return Co(t,`background-position-${e.slice(-1)}`,"")}function vo(t,e,i){return function(n,s){for(const o of t){const t=So(i[o],s);n[`background-position-${o.slice(-1)}`]=`calc(${e[o]} + ${t}px)`}}}const bo={},wo={};function $o(t){return{width:t.naturalWidth,height:t.naturalHeight}}function xo(t,e=_){const i=[],{length:n}=t;let s=0;for(let o=0;o<n;o++){let[r,a]=I(t[o])?t[o].trim().split(/ (?![^(]*\))/):[t[o]];if(r=e(r),a=a?_(a)/100:null,0===o?null===a?a=0:a&&i.push([r,0]):o===n-1&&(null===a?a=1:1!==a&&(i.push([r,a]),a=1)),i.push([r,a]),null===a)s++;else if(s){const t=i[o-s-1][1],e=(a-t)/(s+1);for(let n=s;n>0;n--)i[o-n][1]=t+e*(s-n+1);s=0}}return i}function yo(t,e){const i=d(t.slice(1),(([,t])=>e<=t))+1;return[t[i-1][0],t[i][0],(e-t[i-1][1])/(t[i][1]-t[i-1][1])]}function So(t,e){const[i,n,s]=yo(t,e);return i+Math.abs(i-n)*s*(i<n?1:-1)}const Io=/^-?\d+(?:\.\d+)?(\S+)?/;function ko(t,e){var i;for(const e of t){const t=null==(i=e.match)?void 0:i.call(e,Io);if(t)return t[1]}return e}function Co(t,e,i){const n=t.style[e],s=re(re(t,e,i),e);return t.style[e]=n,s}function To(t,e){return t.reduce(((t,i)=>(t[i]=e,t)),{})}function Eo(t,e){return e>=0?Math.pow(t,e+1):1-Math.pow(1-t,1-e)}var Ao={mixins:[co],props:{target:String,viewport:Number,easing:Number,start:String,end:String},data:{target:!1,viewport:1,easing:1,start:0,end:0},computed:{target:({target:t},e)=>Do(t&&Ot(t,e)||e),start({start:t}){return Ue(t,"height",this.target,!0)},end({end:t,viewport:e}){return Ue(t||(e=100*(1-e))&&`${e}vh+${e}%`,"height",this.target,!0)}},observe:[mn(),vn({target:({target:t})=>t}),un({target:({$el:t,target:e})=>[t,e,Ai(e,!0)]})],update:{read({percent:t},e){if(e.has("scroll")||(t=!1),!xt(this.$el))return!1;if(!this.matchMedia)return;const i=t;return{percent:t=Eo(Ti(this.target,this.start,this.end),this.easing),style:i!==t&&this.getCss(t)}},write({style:t}){this.matchMedia?t&&re(this.$el,t):this.reset()},events:["scroll","resize"]}};function Do(t){return t?"offsetTop"in t?t:Do(Ct(t)):document.documentElement}var _o={props:{parallax:Boolean,parallaxTarget:Boolean,parallaxStart:String,parallaxEnd:String,parallaxEasing:Number},data:{parallax:!1,parallaxTarget:!1,parallaxStart:0,parallaxEnd:0,parallaxEasing:0},observe:[un({target:({$el:t,parallaxTarget:e})=>[t,e],filter:({parallax:t})=>t}),vn({filter:({parallax:t})=>t})],computed:{parallaxTarget({parallaxTarget:t},e){return t&&Ot(t,e)||this.list}},update:{read(){if(!this.parallax)return!1;const t=this.parallaxTarget;if(!t)return!1;const e=Eo(Ti(t,Ue(this.parallaxStart,"height",t,!0),Ue(this.parallaxEnd,"height",t,!0)),this.parallaxEasing);return{parallax:this.getIndexAt(e)}},write({parallax:t}){const[e,i]=t,n=this.getValidIndex(e+Math.ceil(i)),s=this.slides[e],o=this.slides[n],{triggerShow:r,triggerShown:a,triggerHide:l,triggerHidden:h}=function(t){const{clsSlideActive:e,clsEnter:i,clsLeave:n}=t;return{triggerShow:s,triggerShown:o,triggerHide:r,triggerHidden:a};function s(i){et(i,n)&&(r(i),a(i)),et(i,e)||(Zt(i,"beforeitemshow",[t]),Zt(i,"itemshow",[t]))}function o(e){et(e,i)&&Zt(e,"itemshown",[t])}function r(r){et(r,e)||s(r),et(r,i)&&o(r),et(r,n)||(Zt(r,"beforeitemhide",[t]),Zt(r,"itemhide",[t]))}function a(e){et(e,n)&&Zt(e,"itemhidden",[t])}}(this);if(~this.prevIndex)for(const t of new Set([this.index,this.prevIndex]))c([n,e],t)||(l(this.slides[t]),h(this.slides[t]));const d=this.prevIndex!==e||this.index!==n;this.dir=1,this.prevIndex=e,this.index=n,s!==o&&l(s),r(o),d&&a(s),this._translate(s===o?1:i,s,o)},events:["scroll","resize"]},methods:{getIndexAt(t){const e=t*(this.length-1);return[Math.floor(e),e%1]}}};var Mo={update:{write(){if(this.stack.length||this.dragging||this.parallax)return;const t=this.getValidIndex();~this.prevIndex&&this.index===t?this._translate(1):this.show(t)},events:["resize"]}},Po={observe:gn({target:({slides:t})=>t,targets:t=>t.getAdjacentSlides()}),methods:{getAdjacentSlides(){return[1,-1].map((t=>this.slides[this.getIndex(this.index+t)]))}}};function Oo(t,e,i){const n=zo(t,e);return i?n-function(t,e){return ze(e).width/2-ze(t).width/2}(t,e):Math.min(n,Bo(e))}function Bo(t){return Math.max(0,No(t)-ze(t).width)}function No(t,e){return j(Dt(t).slice(0,e),(t=>ze(t).width))}function zo(t,e){return t&&(Fe(t).left+(ht?ze(t).width-ze(e).width:0))*(ht?-1:1)||0}function Ho(t,e){e-=1;const i=ze(t).width,n=e+i+2;return Dt(t).filter((s=>{const o=zo(s,t),r=o+Math.min(ze(s).width,i);return o>=e&&r<=n}))}var Fo={mixins:[Qi,Zs,Mo,_o,Po],props:{center:Boolean,sets:Boolean,active:String},data:{center:!1,sets:!1,attrItem:"bdt-slider-item",selList:".bdt-slider-items",selNav:".bdt-slider-nav",clsContainer:"bdt-slider-container",active:"all",Transitioner:function(t,e,i,{center:n,easing:s,list:o}){const r=t?Oo(t,o,n):Oo(e,o,n)+ze(e).width*i,a=e?Oo(e,o,n):r+ze(t).width*i*(ht?-1:1),{promise:l,resolve:h}=fs();return{dir:i,show(e,n=0,r){const c=r?"linear":s;return e-=Math.round(e*q(n,-1,1)),re(o,"transitionProperty","none"),this.translate(n),re(o,"transitionProperty",""),n=t?n:q(n,0,1),us(this.getItemIn(),"itemin",{percent:n,duration:e,timing:c,dir:i}),t&&us(this.getItemIn(!0),"itemout",{percent:1-n,duration:e,timing:c,dir:i}),de.start(o,{transform:cs(-a*(ht?-1:1),"px")},e,c).then(h,V),l},cancel:()=>de.cancel(o),reset(){re(o,"transform","")},async forward(t,e=this.percent()){return await this.cancel(),this.show(t,e,!0)},translate(n){if(n===this.percent())return;const s=this.getDistance()*i*(ht?-1:1);re(o,"transform",cs(q(s-s*n-a,-No(o),ze(o).width)*(ht?-1:1),"px"));const r=this.getActives(),l=this.getItemIn(),h=this.getItemIn(!0);n=t?q(n,-1,1):0;for(const s of Dt(o)){const a=c(r,s),d=s===l,u=s===h;us(s,"itemtranslate"+(d||!u&&(a||i*(ht?-1:1)==-1^zo(s,o)>zo(t||e))?"in":"out"),{dir:i,percent:u?1-n:d?n:a?1:0})}},percent:()=>Math.abs((new DOMMatrix(re(o,"transform")).m41*(ht?-1:1)+r)/(a-r)),getDistance:()=>Math.abs(a-r),getItemIn(i=!1){let s=this.getActives(),r=Ho(o,Oo(e||t,o,n));if(i){const t=s;s=r,r=t}return r[d(r,(t=>!c(s,t)))]},getActives:()=>Ho(o,Oo(t||e,o,n))}}},computed:{finite({finite:t}){return t||function(t,e){if(!t||t.length<2)return!0;const{width:i}=ze(t);if(!e)return Math.ceil(No(t))<Math.trunc(i+function(t){return Math.max(0,...Dt(t).map((t=>ze(t).width)))}(t));const n=Dt(t),s=Math.trunc(i/2);for(const t in n){const e=n[t],i=ze(e).width,o=new Set([e]);let r=0;for(const e of[-1,1]){let a=i/2,l=0;for(;a<s;){const i=n[G(+t+e+l++*e,n)];if(o.has(i))return!0;a+=ze(i).width,o.add(i)}r=Math.max(r,i/2+ze(n[G(+t+e,n)]).width/2-(a-s))}if(Math.trunc(r)>j(n.filter((t=>!o.has(t))),(t=>ze(t).width)))return!0}return!1}(this.list,this.center)},maxIndex(){if(!this.finite||this.center&&!this.sets)return this.length-1;if(this.center)return z(this.sets);let t=0;const e=Bo(this.list),i=d(this.slides,(i=>{if(t>=e)return!0;t+=ze(i).width}));return~i?i:this.length-1},sets({sets:t}){if(!t||this.parallax)return;let e=0;const i=[],n=ze(this.list).width;for(let t=0;t<this.length;t++){const s=ze(this.slides[t]).width;e+s>n&&(e=0),this.center?e<n/2&&e+s+ze(this.slides[G(t+1,this.slides)]).width/2>n/2&&(i.push(t),e=n/2-s/2):0===e&&i.push(Math.min(t,this.maxIndex)),e+=s}return i.length?i:void 0},transitionOptions(){return{center:this.center,list:this.list}},slides(){return Dt(this.list).filter(xt)}},connected(){it(this.$el,this.clsContainer,!Pe(`.${this.clsContainer}`,this.$el))},observe:un({target:({slides:t,$el:e})=>[e,...t]}),update:{write(){for(const t of this.navItems){const e=D(at(t,this.attrItem));!1!==e&&(t.hidden=!this.maxIndex||e>this.maxIndex||this.sets&&!c(this.sets,e))}this.reorder(),this.parallax||this._translate(1),this.updateActiveClasses()},events:["resize"]},events:{beforeitemshow(t){!this.dragging&&this.sets&&this.stack.length<2&&!c(this.sets,this.index)&&(this.index=this.getValidIndex());const e=Math.abs(this.index-this.prevIndex+(this.dir>0&&this.index<this.prevIndex||this.dir<0&&this.index>this.prevIndex?(this.maxIndex+1)*this.dir:0));if(!this.dragging&&e>1){for(let t=0;t<e;t++)this.stack.splice(1,0,this.dir>0?"next":"previous");return void t.preventDefault()}const i=this.dir<0||!this.slides[this.prevIndex]?this.index:this.prevIndex,n=No(this.list)/this.length;this.duration=Ks(n/this.velocity)*(ze(this.slides[i]).width/n),this.reorder()},itemshow(){~this.prevIndex&&K(this._getTransitioner().getItemIn(),this.clsActive),this.updateActiveClasses(this.prevIndex)},itemshown(){this.updateActiveClasses()}},methods:{reorder(){if(this.finite)return void re(this.slides,"order","");const t=this.dir>0&&this.slides[this.prevIndex]?this.prevIndex:this.index;if(this.slides.forEach(((e,i)=>re(e,"order",this.dir>0&&i<t?1:this.dir<0&&i>=this.index?-1:""))),!this.center||!this.length)return;const e=this.slides[t];let i=ze(this.list).width/2-ze(e).width/2,n=0;for(;i>0;){const e=this.getIndex(--n+t,t),s=this.slides[e];re(s,"order",e>t?-2:-1),i-=ze(s).width}},updateActiveClasses(t=this.index){let e=this._getTransitioner(t).getActives();"all"!==this.active&&(e=[this.slides[this.getValidIndex(t)]]);const n=[this.clsActive,!this.sets||c(this.sets,_(this.index))?this.clsActivated:""];for(const t of this.slides){const s=c(e,t);it(t,n,s),st(t,"aria-hidden",!s);for(const e of Oe(It,t))i(e,"_tabindex")||(e._tabindex=st(e,"tabindex")),st(e,"tabindex",s?e._tabindex:-1)}},getValidIndex(t=this.index,e=this.prevIndex){if(t=this.getIndex(t,e),!this.sets)return t;let i;do{if(c(this.sets,t))return t;i=t,t=this.getIndex(t+this.dir,e)}while(t!==i);return t},getAdjacentSlides(){const{width:t}=ze(this.list),e=-t,i=2*t,n=ze(this.slides[this.index]).width,s=this.center?t/2-n/2:0,o=new Set;for(const t of[-1,1]){let r=s+(t>0?n:0),a=0;do{const e=this.slides[this.getIndex(this.index+t+a++*t)];r+=ze(e).width*t,o.add(e)}while(this.length>a&&r>e&&r<i)}return Array.from(o)},getIndexAt(t){let e=-1;let i=t*(this.center?No(this.list)-(ze(this.slides[0]).width/2+ze(z(this.slides)).width/2):No(this.list,this.maxIndex)),n=0;do{const t=ze(this.slides[++e]).width,s=this.center?t/2+ze(this.slides[e+1]).width/2:t;n=i/s%1,i-=s}while(i>=0&&e<this.maxIndex);return[e,n]}}};var jo={mixins:[co],beforeConnect(){this.item=this.$el.closest(`.${this.$options.id.replace("parallax","items")} > *`)},disconnected(){this.item=null},events:[{name:"itemin itemout",self:!0,el:({item:t})=>t,handler({type:t,detail:{percent:e,duration:i,timing:n,dir:s}}){Qe.read((()=>{if(!this.matchMedia)return;const o=this.getCss(Wo(t,s,e)),r=this.getCss(Lo(t)?.5:s>0?1:0);Qe.write((()=>{re(this.$el,o),de.start(this.$el,r,i,n).catch(V)}))}))}},{name:"transitioncanceled transitionend",self:!0,el:({item:t})=>t,handler(){de.cancel(this.$el)}},{name:"itemtranslatein itemtranslateout",self:!0,el:({item:t})=>t,handler({type:t,detail:{percent:e,dir:i}}){Qe.read((()=>{if(!this.matchMedia)return void this.reset();const n=this.getCss(Wo(t,i,e));Qe.write((()=>re(this.$el,n)))}))}}]};function Lo(t){return h(t,"in")}function Wo(t,e,i){return i/=2,Lo(t)^e<0?i:1-i}var qo={...ls,fade:{show:()=>[{opacity:0,zIndex:0},{zIndex:-1}],percent:t=>1-re(t,"opacity"),translate:t=>[{opacity:1-t,zIndex:0},{zIndex:-1}]},scale:{show:()=>[{opacity:0,transform:ds(1.5),zIndex:0},{zIndex:-1}],percent:t=>1-re(t,"opacity"),translate:t=>[{opacity:1-t,transform:ds(1+.5*t),zIndex:0},{zIndex:-1}]},pull:{show:t=>t<0?[{transform:cs(30),zIndex:-1},{transform:cs(),zIndex:0}]:[{transform:cs(-100),zIndex:0},{transform:cs(),zIndex:-1}],percent:(t,e,i)=>i<0?1-hs(e):hs(t),translate:(t,e)=>e<0?[{transform:cs(30*t),zIndex:-1},{transform:cs(-100*(1-t)),zIndex:0}]:[{transform:cs(100*-t),zIndex:0},{transform:cs(30*(1-t)),zIndex:-1}]},push:{show:t=>t<0?[{transform:cs(100),zIndex:0},{transform:cs(),zIndex:-1}]:[{transform:cs(-30),zIndex:-1},{transform:cs(),zIndex:0}],percent:(t,e,i)=>i>0?1-hs(e):hs(t),translate:(t,e)=>e<0?[{transform:cs(100*t),zIndex:0},{transform:cs(-30*(1-t)),zIndex:-1}]:[{transform:cs(-30*t),zIndex:-1},{transform:cs(100*(1-t)),zIndex:0}]}},Vo={mixins:[Qi,Qs,Mo,_o,Po],props:{ratio:String,minHeight:String,maxHeight:String},data:{ratio:"16:9",minHeight:void 0,maxHeight:void 0,selList:".bdt-slideshow-items",attrItem:"bdt-slideshow-item",selNav:".bdt-slideshow-nav",Animations:qo},watch:{list(t){re(t,{aspectRatio:this.ratio?this.ratio.replace(":","/"):void 0,minHeight:this.minHeight,maxHeight:this.maxHeight,minWidth:"100%",maxWidth:"100%"})}},methods:{getAdjacentSlides(){return[1,-1].map((t=>this.slides[this.getIndex(this.index+t)]))}}},Ro={mixins:[Qi,On],props:{group:String,threshold:Number,clsItem:String,clsPlaceholder:String,clsDrag:String,clsDragState:String,clsBase:String,clsNoDrag:String,clsEmpty:String,clsCustom:String,handle:String},data:{group:!1,threshold:5,clsItem:"bdt-sortable-item",clsPlaceholder:"bdt-sortable-placeholder",clsDrag:"bdt-sortable-drag",clsDragState:"bdt-drag",clsBase:"bdt-sortable",clsNoDrag:"bdt-sortable-nodrag",clsEmpty:"bdt-sortable-empty",clsCustom:"",handle:!1,pos:{}},events:{name:ut,passive:!1,handler:"init"},computed:{target:(t,e)=>(e.tBodies||[e])[0],items(){return Dt(this.target)},isEmpty(){return!this.items.length},handles({handle:t},e){return t?Oe(t,e):this.items}},watch:{isEmpty(t){it(this.target,this.clsEmpty,t)},handles(t,e){re(e,{touchAction:"",userSelect:""}),re(t,{touchAction:"none",userSelect:"none"})}},update:{write(t){if(!this.drag||!Ct(this.placeholder))return;const{pos:{x:e,y:i},origin:{offsetTop:n,offsetLeft:s},placeholder:o}=this;re(this.drag,{top:i-n,left:e-s});const r=this.getSortable(document.elementFromPoint(e,i));if(!r)return;const{items:a}=r;if(a.some(de.inProgress))return;const l=function(t,e){return t[d(t,(t=>U(e,ze(t))))]}(a,{x:e,y:i});if(a.length&&(!l||l===o))return;const h=this.getSortable(o),c=function(t,e,i,n,s,o){if(!Dt(t).length)return;const r=ze(e);if(!o)return function(t,e){const i=1===Dt(t).length;i&&xe(t,e);const n=Dt(t),s=n.some(((t,e)=>{const i=ze(t);return n.slice(e+1).some((t=>{const e=ze(t);return!Yo([i.left,i.right],[e.left,e.right])}))}));return i&&ke(e),s}(t,i)||s<r.top+r.height/2?e:e.nextElementSibling;const a=ze(i),l=Yo([r.top,r.bottom],[a.top,a.bottom]),[h,c,d,u]=l?[n,"width","left","right"]:[s,"height","top","bottom"],f=a[c]<r[c]?r[c]-a[c]:0;return a[d]<r[d]?!(f&&h<r[d]+f)&&e.nextElementSibling:!(f&&h>r[u]-f)&&e}(r.target,l,o,e,i,r===h&&t.moved!==l);!1!==c&&(c&&o===c||(r!==h?(h.remove(o),t.moved=l):delete t.moved,r.insert(o,c),this.touched.add(r)))},events:["move"]},methods:{init(t){const{target:e,button:i,defaultPrevented:n}=t,[s]=this.items.filter((t=>t.contains(e)));!s||n||i>0||St(e)||e.closest(`.${this.clsNoDrag}`)||this.handle&&!e.closest(this.handle)||(t.preventDefault(),this.pos=se(t),this.touched=new Set([this]),this.placeholder=s,this.origin={target:e,index:_t(s),...this.pos},Xt(document,ft,this.move),Xt(document,pt,this.end),this.threshold||this.start(t))},start(t){this.drag=function(t,e){let i;if(ve(e,"li","tr")){i=Pe("<div>"),xe(i,e.cloneNode(!0).children);for(const t of e.getAttributeNames())st(i,t,e.getAttribute(t))}else i=e.cloneNode(!0);return xe(t,i),re(i,"margin","0","important"),re(i,{boxSizing:"border-box",width:e.offsetWidth,height:e.offsetHeight,padding:re(e,"padding")}),Le(i.firstElementChild,Le(e.firstElementChild)),i}(this.$container,this.placeholder);const{left:e,top:i}=ze(this.placeholder);p(this.origin,{offsetLeft:this.pos.x-e,offsetTop:this.pos.y-i}),K(this.drag,this.clsDrag,this.clsCustom),K(this.placeholder,this.clsPlaceholder),K(this.items,this.clsItem),K(document.documentElement,this.clsDragState),Zt(this.$el,"start",[this,this.placeholder]),function(t){let e=Date.now();Uo=setInterval((()=>{let{x:i,y:n}=t;n+=document.scrollingElement.scrollTop;const s=.3*(Date.now()-e);e=Date.now(),Ei(document.elementFromPoint(i,t.y)).reverse().some((t=>{let{scrollTop:e,scrollHeight:i}=t;const{top:o,bottom:r,height:a}=_i(t);if(o<n&&o+35>n)e-=s;else{if(!(r>n&&r-35<n))return;e+=s}if(e>0&&e<i-a)return t.scrollTop=e,!0}))}),15)}(this.pos),this.move(t)},move:function(t){let e;return function(...i){e||(e=!0,t.call(this,...i),requestAnimationFrame((()=>e=!1)))}}((function(t){p(this.pos,se(t)),!this.drag&&(Math.abs(this.pos.x-this.origin.x)>this.threshold||Math.abs(this.pos.y-this.origin.y)>this.threshold)&&this.start(t),this.$emit("move")})),end(){if(Jt(document,ft,this.move),Jt(document,pt,this.end),!this.drag)return;clearInterval(Uo);const t=this.getSortable(this.placeholder);this===t?this.origin.index!==_t(this.placeholder)&&Zt(this.$el,"moved",[this,this.placeholder]):(Zt(t.$el,"added",[t,this.placeholder]),Zt(this.$el,"removed",[this,this.placeholder])),Zt(this.$el,"stop",[this,this.placeholder]),ke(this.drag),this.drag=null;for(const{clsPlaceholder:t,clsItem:e}of this.touched)for(const i of this.touched)Q(i.items,t,e);this.touched=null,Q(document.documentElement,this.clsDragState)},insert(t,e){K(this.items,this.clsItem),e&&e.previousElementSibling!==t?this.animate((()=>ye(e,t))):!e&&this.target.lastElementChild!==t&&this.animate((()=>xe(this.target,t)))},remove(t){this.target.contains(t)&&this.animate((()=>ke(t)))},getSortable(t){do{const e=this.$getComponent(t,"sortable");if(e&&(e===this||!1!==this.group&&e.group===this.group))return e}while(t=Ct(t))}}};let Uo;function Yo(t,e){return t[1]>e[0]&&e[1]>t[0]}var Xo={props:{pos:String,offset:Boolean,flip:Boolean,shift:Boolean,inset:Boolean},data:{pos:"bottom-"+(ht?"right":"left"),offset:!1,flip:!0,shift:!0,inset:!1},connected(){this.pos=this.$props.pos.split("-").concat("center").slice(0,2),[this.dir,this.align]=this.pos,this.axis=c(["top","bottom"],this.dir)?"y":"x"},methods:{positionAt(t,e,i){let n=[this.getPositionOffset(t),this.getShiftOffset(t)];const s=[this.flip&&"flip",this.shift&&"shift"],o={element:[this.inset?this.dir:Re(this.dir),this.align],target:[this.dir,this.align]};if("y"===this.axis){for(const t in o)o[t].reverse();n.reverse(),s.reverse()}const r=Jo(t),a=ze(t);re(t,{top:-a.height,left:-a.width}),zi(t,e,{attach:o,offset:n,boundary:i,placement:s,viewportOffset:this.getViewportOffset(t)}),r()},getPositionOffset(t=this.$el){return Ue(!1===this.offset?re(t,"--bdt-position-offset"):this.offset,"x"===this.axis?"width":"height",t)*(c(["left","top"],this.dir)?-1:1)*(this.inset?-1:1)},getShiftOffset(t=this.$el){return"center"===this.align?0:Ue(re(t,"--bdt-position-shift-offset"),"y"===this.axis?"width":"height",t)*(c(["left","top"],this.align)?1:-1)},getViewportOffset:t=>Ue(re(t,"--bdt-position-viewport-offset"))}};function Jo(t){const e=Ai(t),{scrollTop:i}=e;return()=>{i!==e.scrollTop&&(e.scrollTop=i)}}var Go={mixins:[Zn,Kn,Xo],data:{pos:"top",animation:["bdt-animation-scale-up"],duration:100,cls:"bdt-active"},connected(){var t;kt(t=this.$el)||st(t,"tabindex","0")},disconnected(){this.hide()},methods:{show(){if(this.isToggled(this.tooltip||null))return;const{delay:t=0,title:e}=function(t){const{el:e,id:i,data:n}=t;return["delay","title"].reduce(((t,i)=>({[i]:at(e,i),...t})),{...ln(at(e,i),["title"]),...n})}(this.$options);if(!e)return;const i=st(this.$el,"title"),n=Xt(this.$el,["blur",mt],(t=>!ne(t)&&this.hide()));this.reset=()=>{st(this.$el,{title:i,"aria-describedby":null}),n()};const s=Js(this);st(this.$el,{title:null,"aria-describedby":s}),clearTimeout(this.showTimer),this.showTimer=setTimeout((()=>this._show(e,s)),t)},async hide(){var t;Et(this.$el,"input:focus")||(clearTimeout(this.showTimer),this.isToggled(this.tooltip||null)&&await this.toggleElement(this.tooltip,!1,!1),null==(t=this.reset)||t.call(this),ke(this.tooltip),this.tooltip=null)},async _show(t,e){this.tooltip=xe(this.container,`<div id="${e}" class="bdt-${this.$options.name}" role="tooltip"> <div class="bdt-${this.$options.name}-inner">${t}</div> </div>`),Xt(this.tooltip,"toggled",((t,e)=>{if(!e)return;const i=()=>this.positionAt(this.tooltip,this.$el);i();const[n,s]=function(t,e,[i,n]){const s=He(t),o=He(e),r=[["left","right"],["top","bottom"]];for(const t of r){if(s[t[0]]>=o[t[1]]){i=t[1];break}if(s[t[1]]<=o[t[0]]){i=t[0];break}}return n=(c(r[0],i)?r[1]:r[0]).find((t=>s[t]===o[t]))||"center",[i,n]}(this.tooltip,this.$el,this.pos);this.origin="y"===this.axis?`${Re(n)}-${s}`:`${s}-${Re(n)}`;const o=[Gt(document,`keydown ${ut}`,this.hide,!1,(t=>t.type===ut&&!this.$el.contains(t.target)||"keydown"===t.type&&t.keyCode===Nn)),Xt([document,...Di(this.$el)],"scroll",i,{passive:!0})];Gt(this.tooltip,"hide",(()=>o.forEach((t=>t()))),{self:!0})})),await this.toggleElement(this.tooltip,!0)||this.hide()}},events:{[`focus ${gt} ${ut}`](t){(!ne(t)||t.type===ut)&&this.show()}}};var Zo={mixins:[ps],i18n:{invalidMime:"Invalid File Type: %s",invalidName:"Invalid File Name: %s",invalidSize:"Invalid File Size: %s Kilobytes Max"},props:{allow:String,clsDragover:String,concurrent:Number,maxSize:Number,method:String,mime:String,multiple:Boolean,name:String,params:Object,type:String,url:String},data:{allow:!1,clsDragover:"bdt-dragover",concurrent:1,maxSize:0,method:"POST",mime:!1,multiple:!1,name:"files[]",params:{},type:"",url:"",abort:V,beforeAll:V,beforeSend:V,complete:V,completeAll:V,error:V,fail:V,load:V,loadEnd:V,loadStart:V,progress:V},events:{change(t){Et(t.target,'input[type="file"]')&&(t.preventDefault(),t.target.files&&this.upload(t.target.files),t.target.value="")},drop(t){Qo(t);const e=t.dataTransfer;null!=e&&e.files&&(Q(this.$el,this.clsDragover),this.upload(e.files))},dragenter(t){Qo(t)},dragover(t){Qo(t),K(this.$el,this.clsDragover)},dragleave(t){Qo(t),Q(this.$el,this.clsDragover)}},methods:{async upload(t){if(!(t=f(t)).length)return;Zt(this.$el,"upload",[t]);for(const e of t){if(this.maxSize&&1e3*this.maxSize<e.size)return void this.fail(this.t("invalidSize",this.maxSize));if(this.allow&&!Ko(this.allow,e.name))return void this.fail(this.t("invalidName",this.allow));if(this.mime&&!Ko(this.mime,e.type))return void this.fail(this.t("invalidMime",this.mime))}this.multiple||(t=t.slice(0,1)),this.beforeAll(this,t);const e=function(t,e){const i=[];for(let n=0;n<t.length;n+=e)i.push(t.slice(n,n+e));return i}(t,this.concurrent),i=async t=>{const n=new FormData;t.forEach((t=>n.append(this.name,t)));for(const t in this.params)n.append(t,this.params[t]);try{const t=await async function(t,e){const i={data:null,method:"GET",headers:{},xhr:new XMLHttpRequest,beforeSend:V,responseType:"",...e};return await i.beforeSend(i),function(t,e){return new Promise(((i,n)=>{const{xhr:s}=e;for(const t in e)if(t in s)try{s[t]=e[t]}catch{}s.open(e.method.toUpperCase(),t);for(const t in e.headers)s.setRequestHeader(t,e.headers[t]);Xt(s,"load",(()=>{0===s.status||s.status>=200&&s.status<300||304===s.status?i(s):n(p(Error(s.statusText),{xhr:s,status:s.status}))})),Xt(s,"error",(()=>n(p(Error("Network Error"),{xhr:s})))),Xt(s,"timeout",(()=>n(p(Error("Network Timeout"),{xhr:s})))),s.send(e.data)}))}(t,i)}(this.url,{data:n,method:this.method,responseType:this.type,beforeSend:t=>{const{xhr:e}=t;Xt(e.upload,"progress",this.progress);for(const t of["loadStart","load","loadEnd","abort"])Xt(e,t.toLowerCase(),this[t]);return this.beforeSend(t)}});this.complete(t),e.length?await i(e.shift()):this.completeAll(t)}catch(t){this.error(t)}};await i(e.shift())}}};function Ko(t,e){return e.match(new RegExp(`^${t.replace(/\//g,"\\/").replace(/\*\*/g,"(\\/[^\\/]+)*").replace(/\*/g,"[^\\/]+").replace(/((?!\\))\?/g,"$1.")}$`,"i"))}function Qo(t){t.preventDefault(),t.stopPropagation()}var tr,er=Object.freeze({__proto__:null,Countdown:en,Filter:Vn,Lightbox:no,LightboxPanel:eo,Notification:oo,Parallax:Ao,Slider:Fo,SliderParallax:jo,Slideshow:Vo,SlideshowParallax:jo,Sortable:Ro,Tooltip:Go,Upload:Zo});function ir(t){Zt(document,"uikit:init",t),document.body&&Me(document.body,or),new MutationObserver((t=>t.forEach(nr))).observe(document,{subtree:!0,childList:!0}),new MutationObserver((t=>t.forEach(sr))).observe(document,{subtree:!0,attributes:!0}),t._initialized=!0}function nr({addedNodes:t,removedNodes:e}){for(const e of t)Me(e,or);for(const t of e)Me(t,rr)}function sr({target:t,attributeName:e}){var i;const n=ar(e);n&&(ot(t,e)?qs(n,t):null==(i=Rs(t,n))||i.$destroy())}function or(t){const e=Vs(t);for(const t in e)Bs(e[t]);for(const e of t.getAttributeNames()){const i=ar(e);i&&qs(i,t)}}function rr(t){const e=Vs(t);for(const t in e)Ns(e[t])}function ar(t){l(t,"data-")&&(t=t.slice(5));const e=Ls[t];return e&&(e.options||e).name}(function(t){let e;t.component=Ws,t.getComponents=Vs,t.getComponent=Rs,t.update=Us,t.use=function(t){if(!t.installed)return t.call(null,this),t.installed=!0,this},t.mixin=function(t,e){(e=(I(e)?this.component(e):e)||this).options=an(e.options,t)},t.extend=function(t){t||(t={});const e=this,i=function(t){Hs(this,t)};return(i.prototype=Object.create(e.prototype)).constructor=i,i.options=an(e.options,t),i.super=e,i.extend=e.extend,i},Object.defineProperty(t,"container",{get:()=>e||document.body,set(t){e=Pe(t)}})})(Fs),(tr=Fs).prototype.$mount=function(t){const e=this;(function(t,e){t[js]||(t[js]={}),t[js][e.$options.name]=e})(t,e),e.$options.el=t,document.contains(t)&&Bs(e)},tr.prototype.$destroy=function(t=!1){const e=this,{el:i}=e.$options;i&&Ns(e),Os(e,"destroy"),function(t,e){var i;null==(i=t[js])||delete i[e.$options.name],T(t[js])&&delete t[js]}(i,e),t&&ke(e.$el)},tr.prototype.$create=qs,tr.prototype.$emit=function(t){dn(this,t)},tr.prototype.$update=function(t=this.$el,e){Us(t,e)},tr.prototype.$reset=function(){Ns(this),Bs(this)},tr.prototype.$getComponent=Rs,Object.defineProperties(tr.prototype,{$el:{get(){return this.$options.el}},$container:Object.getOwnPropertyDescriptor(tr,"container")});var lr={mixins:[Qi,Kn],props:{animation:Boolean,targets:String,active:null,collapsible:Boolean,multiple:Boolean,toggle:String,content:String,offset:Number},data:{targets:"> *",active:!1,animation:!0,collapsible:!0,multiple:!1,clsOpen:"bdt-open",toggle:"> .bdt-accordion-title",content:"> .bdt-accordion-content",offset:0},computed:{items:({targets:t},e)=>Oe(t,e),toggles({toggle:t}){return this.items.map((e=>Pe(t,e)))},contents({content:t}){return this.items.map((e=>{var i;return(null==(i=e._wrapper)?void 0:i.firstElementChild)||Pe(t,e)}))}},watch:{items(t,e){if(e||et(t,this.clsOpen))return;const i=!1!==this.active&&t[Number(this.active)]||!this.collapsible&&t[0];i&&this.toggle(i,!1)},toggles(){this.$emit()},contents(t){for(const e of t){const t=et(this.items.find((t=>t.contains(e))),this.clsOpen);hr(e,!t)}this.$emit()}},observe:gn(),events:[{name:"click keydown",delegate:({targets:t,$props:e})=>`${t} ${e.toggle}`,async handler(t){var e;"keydown"===t.type&&t.keyCode!==zn||(t.preventDefault(),null==(e=this._off)||e.call(this),this._off=function(t){const e=Ai(t,!0);let i;return function n(){i=requestAnimationFrame((()=>{const{top:i}=ze(t);i<0&&(e.scrollTop+=i),n()}))}(),()=>requestAnimationFrame((()=>cancelAnimationFrame(i)))}(t.target),await this.toggle(_t(this.toggles,t.current)),this._off())}},{name:"shown hidden",self:!0,delegate:({targets:t})=>t,handler(){this.$emit()}}],update(){const t=Tt(this.items,`.${this.clsOpen}`);for(const e in this.items){const i=this.toggles[e],n=this.contents[e];if(!i||!n)continue;i.id=Js(this,i),n.id=Js(this,n);const s=c(t,this.items[e]);st(i,{role:ve(i,"a")?"button":null,"aria-controls":n.id,"aria-expanded":s,"aria-disabled":!this.collapsible&&t.length<2&&s}),st(n,{role:"region","aria-labelledby":i.id}),ve(n,"ul")&&st(Dt(n),"role","presentation")}},methods:{toggle(t,e){let i=[t=this.items[G(t,this.items)]];const n=Tt(this.items,`.${this.clsOpen}`);if(!this.multiple&&!c(n,i[0])&&(i=i.concat(n)),!(!this.collapsible&&n.length<2&&c(n,t)))return Promise.all(i.map((t=>this.toggleElement(t,!c(n,t),((t,i)=>{if(it(t,this.clsOpen,i),!1!==e&&this.animation)return async function(t,e,{content:i,duration:n,velocity:s,transition:o}){var r;i=(null==(r=t._wrapper)?void 0:r.firstElementChild)||Pe(i,t),t._wrapper||(t._wrapper=Ce(i,"<div>"));const a=t._wrapper;re(a,"overflow","hidden");const l=_(re(a,"height"));await de.cancel(a),hr(i,!1);const h=j(["marginTop","marginBottom"],(t=>re(i,t)))+ze(i).height,c=l/h;n=(s*h+n)*(e?1-c:c),re(a,"height",l),await de.start(a,{height:e?h:0},n,o),Ee(i),delete t._wrapper,e||hr(i,!0)}(t,i,this);hr(Pe(this.content,t),!i)})))))}}};function hr(t,e){t&&(t.hidden=e)}var cr={mixins:[Qi,Kn],args:"animation",props:{animation:Boolean,close:String},data:{animation:!0,selClose:".bdt-alert-close",duration:150},events:{name:"click",delegate:({selClose:t})=>t,handler(t){t.preventDefault(),this.close()}},methods:{async close(){await this.toggleElement(this.$el,!1,dr),this.$destroy(!0)}}};function dr(t,e,{duration:i,transition:n,velocity:s}){const o=_(re(t,"height"));return re(t,"height",o),de.start(t,{height:0,marginTop:0,marginBottom:0,paddingTop:0,paddingBottom:0,borderTop:0,borderBottom:0,opacity:0},s*o+i,n)}var ur={args:"autoplay",props:{automute:Boolean,autoplay:Boolean},data:{automute:!1,autoplay:!0},beforeConnect(){"inview"===this.autoplay&&!ot(this.$el,"preload")&&(this.$el.preload="none"),ve(this.$el,"iframe")&&!ot(this.$el,"allow")&&(this.$el.allow="autoplay"),"hover"===this.autoplay&&(ve(this.$el,"video")?this.$el.tabindex=0:this.autoplay=!0),this.automute&&mi(this.$el)},events:[{name:`${gt} focusin`,filter:({autoplay:t})=>c(t,"hover"),handler(t){ne(t)&&function(t){return!t.paused&&!t.ended}(this.$el)?gi(this.$el):pi(this.$el)}},{name:`${mt} focusout`,filter:({autoplay:t})=>c(t,"hover"),handler(t){ne(t)||gi(this.$el)}}],observe:[fn({filter:({$el:t,autoplay:e})=>e&&"hover"!==e&&vi(t),handler([{isIntersecting:t}]){document.fullscreenElement||(t?pi(this.$el):gi(this.$el))},args:{intersecting:!1},options:({$el:t,autoplay:e})=>({root:"inview"===e?null:Ct(t)})})]};var fr={mixins:[ur],props:{width:Number,height:Number},data:{automute:!0},created(){this.useObjectFit=ve(this.$el,"img","video")},observe:un({target:({$el:t})=>pr(t)||Ct(t),filter:({useObjectFit:t})=>!t}),update:{read(){if(this.useObjectFit)return!1;const{ratio:t,cover:e}=J,{$el:i,width:n,height:s}=this;let o={width:n,height:s};if(!n||!s){const e={width:i.naturalWidth||i.videoWidth||i.clientWidth,height:i.naturalHeight||i.videoHeight||i.clientHeight};o=n?t(e,"width",n):s?t(e,"height",s):e}const{offsetHeight:r,offsetWidth:a}=pr(i)||Ct(i),l=e(o,{width:a,height:r});return!(!l.width||!l.height)&&l},write({height:t,width:e}){re(this.$el,{height:t,width:e})},events:["resize"]}};function pr(t){for(;t=Ct(t);)if("static"!==re(t,"position"))return t}let gr;var mr={mixins:[Zn,Xo,Kn],args:"pos",props:{mode:"list",toggle:Boolean,boundary:Boolean,boundaryX:Boolean,boundaryY:Boolean,target:Boolean,targetX:Boolean,targetY:Boolean,stretch:Boolean,delayShow:Number,delayHide:Number,autoUpdate:Boolean,clsDrop:String,animateOut:Boolean,bgScroll:Boolean,closeOnScroll:Boolean},data:{mode:["click","hover"],toggle:"- *",boundary:!1,boundaryX:!1,boundaryY:!1,target:!1,targetX:!1,targetY:!1,stretch:!1,delayShow:0,delayHide:800,autoUpdate:!0,clsDrop:!1,animateOut:!1,bgScroll:!0,animation:["bdt-animation-fade"],cls:"bdt-open",container:!1,closeOnScroll:!1},computed:{boundary:({boundary:t,boundaryX:e,boundaryY:i},n)=>[Ot(e||t,n)||window,Ot(i||t,n)||window],target({target:t,targetX:e,targetY:i},n){return e||(e=t||this.targetEl),i||(i=t||this.targetEl),[!0===e?window:Ot(e,n),!0===i?window:Ot(i,n)]}},created(){this.tracker=new ai},beforeConnect(){this.clsDrop=this.$props.clsDrop||this.$options.id},connected(){K(this.$el,"bdt-drop",this.clsDrop),this.toggle&&!this.targetEl&&(this.targetEl=function(t){const{$el:e}=t.$create("toggle",Ot(t.toggle,t.$el),{target:t.$el,mode:t.mode});return st(e,"aria-haspopup",!0),e}(this)),this._style=W(this.$el.style,["width","height"])},disconnected(){this.isActive()&&(this.hide(!1),gr=null),re(this.$el,this._style)},events:[{name:"click",delegate:()=>".bdt-drop-close",handler(t){t.preventDefault(),this.hide(!1)}},{name:"click",delegate:()=>'a[href*="#"]',handler({defaultPrevented:t,current:e}){const{hash:i}=e;!t&&i&&Mt(e)&&!this.$el.contains(Pe(i))&&this.hide(!1)}},{name:"beforescroll",handler(){this.hide(!1)}},{name:"toggle",self:!0,handler(t,e){t.preventDefault(),this.isToggled()?this.hide(!1):this.show(null==e?void 0:e.$el,!1)}},{name:"toggleshow",self:!0,handler(t,e){t.preventDefault(),this.show(null==e?void 0:e.$el)}},{name:"togglehide",self:!0,handler(t){t.preventDefault(),Et(this.$el,":focus,:hover")||this.hide()}},{name:`${gt} focusin`,filter:({mode:t})=>c(t,"hover"),handler(t){ne(t)||this.clearTimers()}},{name:`${mt} focusout`,filter:({mode:t})=>c(t,"hover"),handler(t){!ne(t)&&t.relatedTarget&&this.hide()}},{name:"toggled",self:!0,handler(t,e){e&&(this.clearTimers(),this.position())}},{name:"show",self:!0,handler(){gr=this,this.tracker.init(),st(this.targetEl,"aria-expanded",!0);const t=[vr(this),wr(this),xr(this),this.autoUpdate&&br(this),this.closeOnScroll&&$r(this)];Gt(this.$el,"hide",(()=>t.forEach((t=>t&&t()))),{self:!0}),this.bgScroll||Gt(this.$el,"hidden",Gn(this.$el),{self:!0})}},{name:"beforehide",self:!0,handler:"clearTimers"},{name:"hide",handler({target:t}){this.$el===t?(gr=this.isActive()?null:gr,this.tracker.cancel(),st(this.targetEl,"aria-expanded",null)):gr=null===gr&&this.$el.contains(t)&&this.isToggled()?this:gr}}],update:{write(){this.isToggled()&&!et(this.$el,this.clsEnter)&&this.position()}},methods:{show(t=this.targetEl,e=!0){if(this.isToggled()&&t&&this.targetEl&&t!==this.targetEl&&this.hide(!1,!1),this.targetEl=t,this.clearTimers(),!this.isActive()){if(gr){if(e&&gr.isDelaying())return void(this.showTimer=setTimeout((()=>Et(t,":hover")&&this.show()),10));let i;for(;gr&&i!==gr&&!gr.$el.contains(this.$el);)i=gr,gr.hide(!1,!1)}this.container&&Ct(this.$el)!==this.container&&xe(this.container,this.$el),this.showTimer=setTimeout((()=>this.toggleElement(this.$el,!0)),e&&this.delayShow||0)}},hide(t=!0,e=!0){const i=()=>this.toggleElement(this.$el,!1,this.animateOut&&e);this.clearTimers(),this.isDelayedHide=t,t&&this.isDelaying()?this.hideTimer=setTimeout(this.hide,50):t&&this.delayHide?this.hideTimer=setTimeout(i,this.delayHide):i()},clearTimers(){clearTimeout(this.showTimer),clearTimeout(this.hideTimer),this.showTimer=null,this.hideTimer=null},isActive(){return gr===this},isDelaying(){return[this.$el,...Oe(".bdt-drop",this.$el)].some((t=>this.tracker.movesTo(t)))},position(){const t=Jo(this.$el);Q(this.$el,"bdt-drop-stack"),re(this.$el,this._style),this.$el.hidden=!0;const e=this.target.map((t=>function(t,e){return _i(Di(e).find((e=>e.contains(t))))}(this.$el,t))),i=this.getViewportOffset(this.$el),n=[[0,["x","width","left","right"]],[1,["y","height","top","bottom"]]];for(const[t,[s,o]]of n)this.axis!==s&&c([s,!0],this.stretch)&&re(this.$el,{[o]:Math.min(He(this.boundary[t])[o],e[t][o]-2*i),[`overflow-${s}`]:"auto"});const s=e[0].width-2*i;this.$el.hidden=!1,re(this.$el,"maxWidth",""),this.$el.offsetWidth>s&&K(this.$el,"bdt-drop-stack"),re(this.$el,"maxWidth",s),this.positionAt(this.$el,this.target,this.boundary);for(const[t,[s,o,r,a]]of n)if(this.axis===s&&c([s,!0],this.stretch)){const n=Math.abs(this.getPositionOffset()),l=He(this.target[t]),h=He(this.$el);re(this.$el,{[o]:(l[r]>h[r]?l[this.inset?a:r]-Math.max(He(this.boundary[t])[r],e[t][r]+i):Math.min(He(this.boundary[t])[a],e[t][a]-i)-l[this.inset?r:a])-n,[`overflow-${s}`]:"auto"}),this.positionAt(this.$el,this.target,this.boundary)}t()}}};function vr(t){const e=()=>t.$emit(),i=[di(e),ci(Di(t.$el).concat(t.target),e)];return()=>i.map((t=>t.disconnect()))}function br(t,e=()=>t.$emit()){return Xt([document,...Di(t.$el)],"scroll",e,{passive:!0})}function wr(t){return Xt(document,"keydown",(e=>{e.keyCode===Nn&&t.hide(!1)}))}function $r(t){return br(t,(()=>t.hide(!1)))}function xr(t){return Xt(document,ut,(({target:e})=>{t.$el.contains(e)||Gt(document,`${pt} ${vt} scroll`,(({defaultPrevented:i,type:n,target:s})=>{var o;!i&&n===pt&&e===s&&(null==(o=t.targetEl)||!o.contains(e))&&t.hide(!1)}),!0)}))}var yr={mixins:[Qi,Zn],props:{align:String,clsDrop:String,boundary:Boolean,dropbar:Boolean,dropbarAnchor:Boolean,duration:Number,mode:Boolean,offset:Boolean,stretch:Boolean,delayShow:Boolean,delayHide:Boolean,target:Boolean,targetX:Boolean,targetY:Boolean,animation:Boolean,animateOut:Boolean,closeOnScroll:Boolean},data:{align:ht?"right":"left",clsDrop:"bdt-dropdown",clsDropbar:"bdt-dropnav-dropbar",boundary:!0,dropbar:!1,dropbarAnchor:!1,duration:200,container:!1,selNavItem:"> li > a, > ul > li > a"},computed:{dropbarAnchor:({dropbarAnchor:t},e)=>Ot(t,e)||e,dropbar({dropbar:t}){return t?(t=this._dropbar||Ot(t,this.$el)||Pe(`+ .${this.clsDropbar}`,this.$el))||(this._dropbar=Pe("<div></div>")):null},dropContainer(t,e){return this.container||e},dropdowns({clsDrop:t},e){var i;const n=Oe(`.${t}`,e);if(this.dropContainer!==e)for(const e of Oe(`.${t}`,this.dropContainer)){const t=null==(i=this.getDropdown(e))?void 0:i.targetEl;!c(n,e)&&t&&this.$el.contains(t)&&n.push(e)}return n},items:({selNavItem:t},e)=>Oe(t,e)},watch:{dropbar(t){K(t,"bdt-dropbar","bdt-dropbar-top",this.clsDropbar,`bdt-${this.$options.name}-dropbar`)},dropdowns(){this.initializeDropdowns()}},connected(){this.initializeDropdowns()},disconnected(){ke(this._dropbar),delete this._dropbar},events:[{name:"mouseover focusin",delegate:({selNavItem:t})=>t,handler({current:t}){const e=this.getActive();e&&c(e.mode,"hover")&&e.targetEl&&!t.contains(e.targetEl)&&!e.isDelaying()&&e.hide(!1)}},{name:"keydown",self:!0,delegate:({selNavItem:t})=>t,handler(t){var e;const{current:i,keyCode:n}=t,s=this.getActive();n===qn&&(null==s?void 0:s.targetEl)===i&&(t.preventDefault(),null==(e=Pe(It,s.$el))||e.focus()),Sr(t,this.items,s)}},{name:"keydown",el:({dropContainer:t})=>t,delegate:({clsDrop:t})=>`.${t}`,handler(t){var e;const{current:i,keyCode:n,target:s}=t;if(St(s)||!c(this.dropdowns,i))return;const o=this.getActive();let r=-1;if(n===Fn?r=0:n===Hn?r="last":n===Ln?r="previous":n===qn?r="next":n===Nn&&(null==(e=o.targetEl)||e.focus()),~r){t.preventDefault();const e=Oe(It,i);e[G(r,e,d(e,(t=>Et(t,":focus"))))].focus()}Sr(t,this.items,o)}},{name:"mouseleave",el:({dropbar:t})=>t,filter:({dropbar:t})=>t,handler(){const t=this.getActive();t&&c(t.mode,"hover")&&!this.dropdowns.some((t=>Et(t,":hover")))&&t.hide()}},{name:"beforeshow",el:({dropContainer:t})=>t,filter:({dropbar:t})=>t,handler({target:t}){this.isDropbarDrop(t)&&(this.dropbar.previousElementSibling!==this.dropbarAnchor&&Se(this.dropbarAnchor,this.dropbar),K(t,`${this.clsDrop}-dropbar`))}},{name:"show",el:({dropContainer:t})=>t,filter:({dropbar:t})=>t,handler({target:t}){if(!this.isDropbarDrop(t))return;const e=this.getDropdown(t),i=()=>{const i=Math.max(...At(t,`.${this.clsDrop}`).concat(t).map((t=>He(t).bottom)));He(this.dropbar,{left:He(this.dropbar).left,top:this.getDropbarOffset(e.getPositionOffset())}),this.transitionTo(i-He(this.dropbar).top+_(re(t,"marginBottom")),t)};this._observer=ci([e.$el,...e.target],i),i()}},{name:"beforehide",el:({dropContainer:t})=>t,filter:({dropbar:t})=>t,handler(t){const e=this.getActive();Et(this.dropbar,":hover")&&e.$el===t.target&&this.isDropbarDrop(e.$el)&&c(e.mode,"hover")&&e.isDelayedHide&&!this.items.some((t=>e.targetEl!==t&&Et(t,":focus")))&&t.preventDefault()}},{name:"hide",el:({dropContainer:t})=>t,filter:({dropbar:t})=>t,handler({target:t}){var e;if(!this.isDropbarDrop(t))return;null==(e=this._observer)||e.disconnect();const i=this.getActive();(!i||i.$el===t)&&this.transitionTo(0)}}],methods:{getActive(){var t;return c(this.dropdowns,null==(t=gr)?void 0:t.$el)&&gr},async transitionTo(t,e){const{dropbar:i}=this,n=Le(i);if(e=n<t&&e,await de.cancel([e,i]),e){const s=He(e).top-He(i).top-n;s>0&&re(e,"transitionDelay",s/t*this.duration+"ms")}re(e,"clipPath",`polygon(0 0,100% 0,100% ${n}px,0 ${n}px)`),Le(i,n),await Promise.all([de.start(i,{height:t},this.duration),de.start(e,{clipPath:`polygon(0 0,100% 0,100% ${t}px,0 ${t}px)`},this.duration).finally((()=>re(e,{clipPath:"",transitionDelay:""})))]).catch(V)},getDropdown(t){return this.$getComponent(t,"drop")||this.$getComponent(t,"dropdown")},isDropbarDrop(t){return c(this.dropdowns,t)&&et(t,this.clsDrop)},getDropbarOffset(t){const{$el:e,target:i,targetY:n}=this,{top:s,height:o}=He(Ot(n||i||e,e));return s+o+t},initializeDropdowns(){this.$create("drop",this.dropdowns.filter((t=>!this.getDropdown(t))),{...this.$props,flip:!1,shift:!0,pos:`bottom-${this.align}`,boundary:!0===this.boundary?this.$el:this.boundary})}}};function Sr(t,e,i){var n,s,o;const{current:r,keyCode:a}=t;let l=-1;a===Fn?l=0:a===Hn?l="last":a===jn?l="previous":a===Wn?l="next":a===Bn&&(null==(n=i.targetEl)||n.focus(),null==(s=i.hide)||s.call(i,!1)),~l&&(t.preventDefault(),null==(o=i.hide)||o.call(i,!1),e[G(l,e,e.indexOf(i.targetEl||r))].focus())}var Ir={mixins:[Qi],args:"target",props:{target:Boolean},data:{target:!1},computed:{input:(t,e)=>Pe(yt,e),state(){return this.input.nextElementSibling},target({target:t},e){return t&&(!0===t&&Ct(this.input)===e&&this.input.nextElementSibling||Pe(t,e))}},update(){var t;const{target:e,input:i}=this;if(!e)return;let n;const s=St(e)?"value":"textContent",o=e[s],r=null!=(t=i.files)&&t[0]?i.files[0].name:Et(i,"select")&&(n=Oe("option",i).filter((t=>t.selected))[0])?n.textContent:i.value;o!==r&&(e[s]=r)},events:[{name:"change",handler(){this.$emit()}},{name:"reset",el:({$el:t})=>t.closest("form"),handler(){this.$emit()}}]},kr={extends:xn,mixins:[Qi],name:"grid",props:{masonry:Boolean,parallax:String,parallaxStart:String,parallaxEnd:String,parallaxJustify:Boolean},data:{margin:"bdt-grid-margin",clsStack:"bdt-grid-stack",masonry:!1,parallax:0,parallaxStart:0,parallaxEnd:0,parallaxJustify:!1},connected(){this.masonry&&K(this.$el,"bdt-flex-top","bdt-flex-wrap-top")},observe:vn({filter:({parallax:t,parallaxJustify:e})=>t||e}),update:[{write({rows:t}){it(this.$el,this.clsStack,!t.some((t=>t.length>1)))},events:["resize"]},{read(t){const{rows:e}=t;let{masonry:i,parallax:n,parallaxJustify:s,margin:o}=this;if(n=Math.max(0,Ue(n)),!(i||n||s)||Cr(e)||e[0].some(((t,i)=>e.some((e=>e[i]&&e[i].offsetWidth!==t.offsetWidth)))))return t.translates=t.scrollColumns=!1;let r,a,l=function(t,e){const i=t.flat().find((t=>et(t,e)));return _(i?re(i,"marginTop"):re(t[0][0],"paddingLeft"))}(e,o);i?[r,a]=function(t,e,i){const n=[],s=[],o=Array(t[0].length).fill(0);let r=0;for(let a of t){ht&&(a=a.reverse());let t=0;for(const l in a){const{offsetWidth:h,offsetHeight:c}=a[l],d=i?l:o.indexOf(Math.min(...o));Tr(n,d,a[l]),Tr(s,d,[(d-l)*h*(ht?-1:1),o[d]-r]),o[d]+=c+e,t=Math.max(t,c)}r+=t+e}return[n,s]}(e,l,"next"===i):r=function(t){const e=[];for(const i of t)for(const t in i)Tr(e,t,i[t]);return e}(e);const h=r.map((t=>j(t,"offsetHeight")+l*(t.length-1))),c=Math.max(0,...h);let d,u,f;return(n||s)&&(d=h.map(((t,e)=>s?c-t+n:n/(e%2||8))),s||(n=Math.max(...h.map(((t,e)=>t+d[e]-c)))),u=Ue(this.parallaxStart,"height",this.$el,!0),f=Ue(this.parallaxEnd,"height",this.$el,!0)),{columns:r,translates:a,scrollColumns:d,parallaxStart:u,parallaxEnd:f,padding:n,height:a?c:""}},write({height:t,padding:e}){re(this.$el,"paddingBottom",e||""),!1!==t&&re(this.$el,"height",t)},events:["resize"]},{read({rows:t,scrollColumns:e,parallaxStart:i,parallaxEnd:n}){return{scrolled:!(!e||Cr(t))&&Ti(this.$el,i,n)}},write({columns:t,scrolled:e,scrollColumns:i,translates:n}){!e&&!n||t.forEach(((t,s)=>t.forEach(((t,o)=>{let[r,a]=n&&n[s][o]||[0,0];e&&(a+=e*i[s]),re(t,"transform",`translate(${r}px, ${a}px)`)}))))},events:["scroll","resize"]}]};function Cr(t){return t.flat().some((t=>"absolute"===re(t,"position")))}function Tr(t,e,i){t[e]||(t[e]=[]),t[e].push(i)}var Er={args:"target",props:{target:String,row:Boolean},data:{target:"> *",row:!0},computed:{elements:({target:t},e)=>Oe(t,e)},observe:un({target:({$el:t,elements:e})=>e.reduce(((t,e)=>t.concat(e,...e.children)),[t])}),events:{name:"loadingdone",el:()=>document.fonts,handler(){this.$emit("resize")}},update:{read(){return{rows:(this.row?yn(this.elements):[this.elements]).map(Ar)}},write({rows:t}){for(const{heights:e,elements:i}of t)i.forEach(((t,i)=>re(t,"minHeight",e[i])))},events:["resize"]}};function Ar(t){if(t.length<2)return{heights:[""],elements:t};let e=t.map(Dr);const i=Math.max(...e);return{heights:t.map(((t,n)=>e[n].toFixed(2)===i.toFixed(2)?"":i)),elements:t}}function Dr(t){const e=W(t.style,["display","minHeight"]);xt(t)||re(t,"display","block","important"),re(t,"minHeight","");const i=ze(t).height-Ve(t,"height","content-box");return re(t,e),i}var _r={args:"target",props:{target:String},data:{target:""},computed:{target:{get:({target:t},e)=>Ot(t,e),observe:({target:t})=>t}},observe:un({target:({target:t})=>t}),update:{read(){return!!this.target&&{height:this.target.offsetHeight}},write({height:t}){re(this.$el,{minHeight:t})},events:["resize"]}},Mr={props:{expand:Boolean,offsetTop:Boolean,offsetBottom:Boolean,minHeight:Number},data:{expand:!1,offsetTop:!1,offsetBottom:!1,minHeight:0},observe:[mn({filter:({expand:t})=>t}),un({target:({$el:t})=>Ei(t)})],update:{read(){if(!xt(this.$el))return!1;let t="";const e=Ve(this.$el,"height","content-box"),{body:i,scrollingElement:n}=document,s=Ai(this.$el),{height:o}=_i(s===i?n:s),r=n===s||i===s;if(t="calc("+(r?"100vh":`${o}px`),this.expand){t+=` - ${ze(s).height-ze(this.$el).height}px`}else{if(this.offsetTop)if(r){const e=!0===this.offsetTop?this.$el:Ot(this.offsetTop,this.$el),{top:i}=He(e);t+=i>0&&i<o/2?` - ${i}px`:""}else t+=` - ${Ve(s,"height",re(s,"boxSizing"))}px`;!0===this.offsetBottom?t+=` - ${ze(this.$el.nextElementSibling).height}px`:C(this.offsetBottom)?t+=` - ${this.offsetBottom}vh`:this.offsetBottom&&h(this.offsetBottom,"px")?t+=` - ${_(this.offsetBottom)}px`:I(this.offsetBottom)&&(t+=` - ${ze(Ot(this.offsetBottom,this.$el)).height}px`)}return t+=(e?` - ${e}px`:"")+")",{minHeight:t}},write({minHeight:t}){re(this.$el,"minHeight",`max(${this.minHeight||0}px, ${t})`)},events:["resize"]}},Pr='<svg width="20" height="20" viewBox="0 0 20 20"><circle fill="none" stroke="#000" stroke-width="1.1" cx="9" cy="9" r="7"/><path fill="none" stroke="#000" stroke-width="1.1" d="M14,14 L18,18 L14,14 Z"/></svg>',Or={args:"src",props:{width:Number,height:Number,ratio:Number},data:{ratio:1},connected(){this.svg=this.getSvg().then((t=>{if(!this._connected)return;const e=function(t,e){if(wt(e)||ve(e,"canvas")){e.hidden=!0;const i=e.nextElementSibling;return Br(t,i)?i:Se(e,t)}const i=e.lastElementChild;return Br(t,i)?i:xe(e,t)}(t,this.$el);return this.svgEl&&e!==this.svgEl&&ke(this.svgEl),Nr.call(this,e,t),this.svgEl=e}),V)},disconnected(){this.svg.then((t=>{this._connected||(wt(this.$el)&&(this.$el.hidden=!1),ke(t),this.svgEl=null)})),this.svg=null},methods:{async getSvg(){}}};function Br(t,e){return ve(t,"svg")&&ve(e,"svg")&&t.innerHTML===e.innerHTML}function Nr(t,e){const i=["width","height"];let n=i.map((t=>this[t]));n.some((t=>t))||(n=i.map((t=>st(e,t))));const s=st(e,"viewBox");s&&!n.some((t=>t))&&(n=s.split(" ").slice(2)),n.forEach(((e,n)=>st(t,i[n],_(e)*this.ratio||null)))}var zr={mixins:[Or],args:"src",props:{src:String,icon:String,attributes:"list",strokeAnimation:Boolean},data:{strokeAnimation:!1},observe:[pn({async handler(){const t=await this.svg;t&&Hr.call(this,t)},options:{attributes:!0,attributeFilter:["id","class","style"]}})],async connected(){c(this.src,"#")&&([this.src,this.icon]=this.src.split("#"));const t=await this.svg;t&&(Hr.call(this,t),this.strokeAnimation&&function(t){const e=ao(t);e&&re(t,"--bdt-animation-stroke",e)}(t))},methods:{async getSvg(){return ve(this.$el,"img")&&!this.$el.complete&&"lazy"===this.$el.loading&&await new Promise((t=>Gt(this.$el,"load",t))),function(t,e){return e&&c(t,"<symbol")&&(t=Lr(t)[e]||t),Wr(t)}(await Fr(this.src),this.icon)||Promise.reject("SVG not found.")}}};function Hr(t){const{$el:e}=this;K(t,st(e,"class"),"bdt-svg");for(let i=0;i<e.style.length;i++){const n=e.style[i];re(t,n,re(e,n))}for(const e in this.attributes){const[i,n]=this.attributes[e].split(":",2);st(t,i,n)}this.$el.id||rt(t,"id")}const Fr=Z((async t=>t?l(t,"data:")?decodeURIComponent(t.split(",")[1]):(await fetch(t)).text():Promise.reject()));const jr=/<symbol([^]*?id=(['"])(.+?)\2[^]*?<\/)symbol>/g,Lr=Z((function(t){const e={};let i;for(jr.lastIndex=0;i=jr.exec(t);)e[i[3]]=`<svg ${i[1]}svg>`;return e}));function Wr(t){const e=document.createElement("template");return e.innerHTML=t,e.content.firstElementChild}const qr={spinner:'<svg width="30" height="30" viewBox="0 0 30 30"><circle fill="none" stroke="#000" cx="15" cy="15" r="14"/></svg>',totop:'<svg width="18" height="10" viewBox="0 0 18 10"><polyline fill="none" stroke="#000" stroke-width="1.2" points="1 9 9 1 17 9"/></svg>',marker:'<svg width="20" height="20" viewBox="0 0 20 20"><rect width="1" height="11" x="9" y="4"/><rect width="11" height="1" x="4" y="9"/></svg>',"close-icon":'<svg width="14" height="14" viewBox="0 0 14 14"><line fill="none" stroke="#000" stroke-width="1.1" x1="1" y1="1" x2="13" y2="13"/><line fill="none" stroke="#000" stroke-width="1.1" x1="13" y1="1" x2="1" y2="13"/></svg>',"close-large":'<svg width="20" height="20" viewBox="0 0 20 20"><line fill="none" stroke="#000" stroke-width="1.4" x1="1" y1="1" x2="19" y2="19"/><line fill="none" stroke="#000" stroke-width="1.4" x1="19" y1="1" x2="1" y2="19"/></svg>',"drop-parent-icon":'<svg width="12" height="12" viewBox="0 0 12 12"><polyline fill="none" stroke="#000" stroke-width="1.1" points="1 3.5 6 8.5 11 3.5"/></svg>',"nav-parent-icon":'<svg width="12" height="12" viewBox="0 0 12 12"><polyline fill="none" stroke="#000" stroke-width="1.1" points="1 3.5 6 8.5 11 3.5"/></svg>',"nav-parent-icon-large":'<svg width="14" height="14" viewBox="0 0 14 14"><polyline fill="none" stroke="#000" stroke-width="1.1" points="1 4 7 10 13 4"/></svg>',"navbar-parent-icon":'<svg width="12" height="12" viewBox="0 0 12 12"><polyline fill="none" stroke="#000" stroke-width="1.1" points="1 3.5 6 8.5 11 3.5"/></svg>',"navbar-toggle-icon":'<svg width="20" height="20" viewBox="0 0 20 20"><style>.bdt-navbar-toggle-icon svg&gt;[class*=&quot;line-&quot;]{transition:0.2s ease-in-out;transition-property:transform, opacity;transform-origin:center;opacity:1}.bdt-navbar-toggle-icon svg&gt;.line-3{opacity:0}.bdt-navbar-toggle-animate[aria-expanded=&quot;true&quot;] svg&gt;.line-3{opacity:1}.bdt-navbar-toggle-animate[aria-expanded=&quot;true&quot;] svg&gt;.line-2{transform:rotate(45deg)}.bdt-navbar-toggle-animate[aria-expanded=&quot;true&quot;] svg&gt;.line-3{transform:rotate(-45deg)}.bdt-navbar-toggle-animate[aria-expanded=&quot;true&quot;] svg&gt;.line-1,.bdt-navbar-toggle-animate[aria-expanded=&quot;true&quot;] svg&gt;.line-4{opacity:0}.bdt-navbar-toggle-animate[aria-expanded=&quot;true&quot;] svg&gt;.line-1{transform:translateY(6px) scaleX(0)}.bdt-navbar-toggle-animate[aria-expanded=&quot;true&quot;] svg&gt;.line-4{transform:translateY(-6px) scaleX(0)}</style><rect width="20" height="2" y="3" class="line-1"/><rect width="20" height="2" y="9" class="line-2"/><rect width="20" height="2" y="9" class="line-3"/><rect width="20" height="2" y="15" class="line-4"/></svg>',"overlay-icon":'<svg width="40" height="40" viewBox="0 0 40 40"><rect width="1" height="40" x="19" y="0"/><rect width="40" height="1" x="0" y="19"/></svg>',"pagination-next":'<svg width="7" height="12" viewBox="0 0 7 12"><polyline fill="none" stroke="#000" stroke-width="1.2" points="1 1 6 6 1 11"/></svg>',"pagination-previous":'<svg width="7" height="12" viewBox="0 0 7 12"><polyline fill="none" stroke="#000" stroke-width="1.2" points="6 1 1 6 6 11"/></svg>',"search-icon":Pr,"search-medium":'<svg width="24" height="24" viewBox="0 0 24 24"><circle fill="none" stroke="#000" stroke-width="1.1" cx="10.5" cy="10.5" r="9.5"/><line fill="none" stroke="#000" stroke-width="1.1" x1="23" y1="23" x2="17" y2="17"/></svg>',"search-large":'<svg width="40" height="40" viewBox="0 0 40 40"><circle fill="none" stroke="#000" stroke-width="1.8" cx="17.5" cy="17.5" r="16.5"/><line fill="none" stroke="#000" stroke-width="1.8" x1="38" y1="39" x2="29" y2="30"/></svg>',"search-toggle-icon":Pr,"slidenav-next":'<svg width="14" height="24" viewBox="0 0 14 24"><polyline fill="none" stroke="#000" stroke-width="1.4" points="1.225,23 12.775,12 1.225,1"/></svg>',"slidenav-next-large":'<svg width="25" height="40" viewBox="0 0 25 40"><polyline fill="none" stroke="#000" stroke-width="2" points="4.002,38.547 22.527,20.024 4,1.5"/></svg>',"slidenav-previous":'<svg width="14" height="24" viewBox="0 0 14 24"><polyline fill="none" stroke="#000" stroke-width="1.4" points="12.775,1 1.225,12 12.775,23"/></svg>',"slidenav-previous-large":'<svg width="25" height="40" viewBox="0 0 25 40"><polyline fill="none" stroke="#000" stroke-width="2" points="20.527,1.5 2,20.024 20.525,38.547"/></svg>'},Vr={install:function(t){t.icon.add=(e,i)=>{const n=I(e)?{[e]:i}:e;H(n,((t,e)=>{qr[e]=t,delete na[e]})),t._initialized&&Me(document.body,(e=>H(t.getComponents(e),(t=>{t.$options.isIcon&&t.icon in n&&t.$reset()}))))}},mixins:[Or],args:"icon",props:{icon:String},isIcon:!0,beforeConnect(){K(this.$el,"bdt-icon")},methods:{async getSvg(){const t=function(t){return t=sa[t]||t,qr[t]?(na[t]||(na[t]=Wr(qr[function(t){return ht?N(N(t,"left","right"),"previous","next"):t}(t)]||qr[t])),na[t].cloneNode(!0)):null}(this.icon);if(!t)throw"Icon not found.";return t}}},Rr={args:!1,extends:Vr,data:t=>({icon:s(t.constructor.options.name)}),beforeConnect(){K(this.$el,this.$options.id)}},Ur={extends:Rr,beforeConnect(){const t=this.$props.icon;this.icon=this.$el.closest(".bdt-nav-primary")?`${t}-large`:t}},Yr={extends:Rr,mixins:[ps],i18n:{toggle:"Open Search",submit:"Submit Search"},beforeConnect(){const t=et(this.$el,"bdt-search-toggle")||et(this.$el,"bdt-navbar-toggle");if(this.icon=t?"search-toggle-icon":et(this.$el,"bdt-search-icon")&&this.$el.closest(".bdt-search-large")?"search-large":this.$el.closest(".bdt-search-medium")?"search-medium":this.$props.icon,!ot(this.$el,"aria-label"))if(t){const t=this.t("toggle");st(this.$el,"aria-label",t)}else{const t=this.$el.closest("a,button");if(t){st(t,"aria-label",this.t("submit"))}}}},Xr={extends:Rr,beforeConnect(){st(this.$el,"role","status")},methods:{async getSvg(){const t=await Vr.methods.getSvg.call(this);return 1!==this.ratio&&re(Pe("circle",t),"strokeWidth",1/this.ratio),t}}},Jr={extends:Rr,mixins:[ps],beforeConnect(){const t=this.$el.closest("a,button");st(t,"role",null!==this.role&&ve(t,"a")?"button":this.role);const e=this.t("label");e&&!ot(t,"aria-label")&&st(t,"aria-label",e)}},Gr={extends:Jr,beforeConnect(){K(this.$el,"bdt-slidenav");const t=this.$props.icon;this.icon=et(this.$el,"bdt-slidenav-large")?`${t}-large`:t}},Zr={extends:Jr,i18n:{label:"Open menu"}},Kr={extends:Jr,i18n:{label:"Close"},beforeConnect(){this.icon="close-"+(et(this.$el,"bdt-close-large")?"large":"icon")}},Qr={extends:Jr,i18n:{label:"Open"}},ta={extends:Jr,i18n:{label:"Back to top"}},ea={extends:Jr,i18n:{label:"Next page"},data:{role:null}},ia={extends:Jr,i18n:{label:"Previous page"},data:{role:null}},na={};const sa={twitter:"x"};var oa={args:"dataSrc",props:{dataSrc:String,sources:String,margin:String,target:String,loading:String},data:{dataSrc:"",sources:!1,margin:"50%",target:!1,loading:"lazy"},connected(){"lazy"!==this.loading?this.load():ha(this.$el)&&(this.$el.loading="lazy",ra(this.$el))},disconnected(){this.img&&(this.img.onload=""),delete this.img},observe:fn({handler(t,e){this.load(),e.disconnect()},options:({margin:t})=>({rootMargin:t}),filter:({loading:t})=>"lazy"===t,target:({$el:t,$props:e})=>e.target?[t,...Bt(e.target,t)]:t}),methods:{load(){if(this.img)return this.img;const t=ha(this.$el)?this.$el:function(t,e,i){const n=new Image;return function(t,e){if(e=function(t){if(!t)return[];if(l(t,"["))try{t=JSON.parse(t)}catch{t=[]}else t=ln(t);return u(t)||(t=[t]),t.filter((t=>!T(t)))}(e),e.length){const i=De("<picture>");for(const t of e){const e=De("<source>");st(e,t),xe(i,e)}xe(i,t)}}(n,i),la(t,n),n.onload=()=>{ra(t,n.currentSrc)},st(n,"src",e),n}(this.$el,this.dataSrc,this.sources);return rt(t,"loading"),ra(this.$el,t.currentSrc),this.img=t}}};function ra(t,e){if(ha(t)){const e=Ct(t);(ve(e,"picture")?Dt(e):[t]).forEach((t=>la(t,t)))}else e&&!c(t.style.backgroundImage,e)&&(re(t,"backgroundImage",`url(${Yt(e)})`),Zt(t,Kt("load",!1)))}const aa=["data-src","data-srcset","sizes"];function la(t,e){for(const i of aa){const n=at(t,i);n&&st(e,i.replace(/^(data-)+/,""),n)}}function ha(t){return ve(t,"img")}var ca={props:{target:String,selActive:String},data:{target:!1,selActive:!1},computed:{target:({target:t},e)=>t?Oe(t,e):e},observe:[fn({handler(t){this.isIntersecting=t.some((({isIntersecting:t})=>t)),this.$emit()},target:({target:t})=>t,args:{intersecting:!1}}),pn({target:({target:t})=>t,options:{attributes:!0,attributeFilter:["class"],attributeOldValue:!0}}),{target:({target:t})=>t,observe:(t,e)=>{const i=ci([...P(t),document.documentElement],e),n=[Xt(document,"scroll itemshown itemhidden",e,{passive:!0,capture:!0}),Xt(document,"show hide transitionstart",(t=>(e(),i.observe(t.target)))),Xt(document,"shown hidden transitionend transitioncancel",(t=>(e(),i.unobserve(t.target))))];return{observe:i.observe.bind(i),unobserve:i.unobserve.bind(i),disconnect(){i.disconnect(),n.map((t=>t()))}}},handler(){this.$emit()}}],update:{read(){if(!this.isIntersecting)return!1;for(const t of P(this.target)){let e=!this.selActive||Et(t,this.selActive)?da(t):"";!1!==e&&tt(t,"bdt-light bdt-dark",e)}}}};function da(t){const e=ze(t),i=ze(window);if(!R(e,i))return!1;const{left:n,top:s,height:o,width:r}=e;let a;for(const e of[.25,.5,.75]){const l=t.ownerDocument.elementsFromPoint(Math.max(0,Math.min(n+r*e,i.width-1)),Math.max(0,Math.min(s+o/2,i.height-1)));for(const e of l){if(t.contains(e)||!ua(e)||e.closest('[class*="-leave"]')&&l.some((t=>e!==t&&Et(t,'[class*="-enter"]'))))continue;const i=re(e,"--bdt-inverse");if(i){if(i===a)return`bdt-${i}`;a=i;break}}}return a?`bdt-${a}`:""}function ua(t){if("visible"!==re(t,"visibility"))return!1;for(;t;){if("0"===re(t,"opacity"))return!1;t=Ct(t)}return!0}var fa={mixins:[Qi,ro],props:{fill:String},data:{fill:"",clsWrapper:"bdt-leader-fill",clsHide:"bdt-leader-hide",attrFill:"data-fill"},computed:{fill:({fill:t},e)=>t||re(e,"--bdt-leader-fill-content")},connected(){[this.wrapper]=Te(this.$el,`<span class="${this.clsWrapper}">`)},disconnected(){Ee(this.wrapper.childNodes)},observe:un(),update:{read(){return{width:Math.trunc(this.$el.offsetWidth/2),fill:this.fill,hide:!this.matchMedia}},write({width:t,fill:e,hide:i}){it(this.wrapper,this.clsHide,i),st(this.wrapper,this.attrFill,new Array(t).join(e))},events:["resize"]}},pa={install:function({modal:t}){function e(e,i,n=V,s=V){i={bgClose:!1,escClose:!0,...i,i18n:{...t.i18n,...null==i?void 0:i.i18n}};const o=t.dialog(e(i),i);return p(new Promise((t=>{const e=Xt(o.$el,"hide",(()=>t(n())));Xt(o.$el,"submit","form",(i=>{i.preventDefault(),t(s(o)),e(),o.hide()}))})),{dialog:o})}t.dialog=function(e,i){const n=t(Pe(`<div><div class="bdt-modal-dialog">${e}</div></div>`),{stack:!0,role:"alertdialog",...i});return n.show(),Xt(n.$el,"hidden",(async()=>{await Promise.resolve(),n.$destroy(!0)}),{self:!0}),n},t.alert=function(t,i){return e((({i18n:e})=>`<div class="bdt-modal-body">${I(t)?t:we(t)}</div> <div class="bdt-modal-footer bdt-text-right"> <button class="bdt-button bdt-button-primary bdt-modal-close" autofocus>${e.ok}</button> </div>`),i)},t.confirm=function(t,i){return e((({i18n:e})=>`<form> <div class="bdt-modal-body">${I(t)?t:we(t)}</div> <div class="bdt-modal-footer bdt-text-right"> <button class="bdt-button bdt-button-default bdt-modal-close" type="button">${e.cancel}</button> <button class="bdt-button bdt-button-primary" autofocus>${e.ok}</button> </div> </form>`),i,(()=>Promise.reject()))},t.prompt=function(t,i,n){const s=e((({i18n:e})=>`<form class="bdt-form-stacked"> <div class="bdt-modal-body"> <label>${I(t)?t:we(t)}</label> <input class="bdt-input" autofocus> </div> <div class="bdt-modal-footer bdt-text-right"> <button class="bdt-button bdt-button-default bdt-modal-close" type="button">${e.cancel}</button> <button class="bdt-button bdt-button-primary">${e.ok}</button> </div> </form>`),n,(()=>null),(()=>r.value)),{$el:o}=s.dialog,r=Pe("input",o);return r.value=i||"",Xt(o,"show",(()=>r.select())),s},t.i18n={ok:"Ok",cancel:"Cancel"}},mixins:[ns],data:{clsPage:"bdt-modal-page",selPanel:".bdt-modal-dialog",selClose:'[class*="bdt-modal-close"]'},events:[{name:"fullscreenchange webkitendfullscreen",capture:!0,handler(t){ve(t.target,"video")&&this.isToggled()&&!document.fullscreenElement&&this.hide()}},{name:"show",self:!0,handler(){et(this.panel,"bdt-margin-auto-vertical")?K(this.$el,"bdt-flex"):re(this.$el,"display","block"),Le(this.$el)}},{name:"hidden",self:!0,handler(){re(this.$el,"display",""),Q(this.$el,"bdt-flex")}}]};var ga={extends:lr,data:{targets:"> .bdt-parent",toggle:"> a",content:"> ul"}};const ma="bdt-navbar-transparent";var va={extends:yr,props:{dropbarTransparentMode:Boolean},data:{clsDrop:"bdt-navbar-dropdown",selNavItem:".bdt-navbar-nav > li > a,a.bdt-navbar-item,button.bdt-navbar-item,.bdt-navbar-item a,.bdt-navbar-item button,.bdt-navbar-toggle",dropbarTransparentMode:!1},computed:{navbarContainer:(t,e)=>e.closest(".bdt-navbar-container")},watch:{items(){const t=et(this.$el,"bdt-navbar-justify"),e=Oe(".bdt-navbar-nav, .bdt-navbar-left, .bdt-navbar-right",this.$el);for(const i of e){re(i,"flexGrow",t?Oe(".bdt-navbar-nav > li > a, .bdt-navbar-item, .bdt-navbar-toggle",i).length:"")}}},events:[{name:"show",el:({dropContainer:t})=>t,handler({target:t}){"remove"===this.getTransparentMode(t)&&et(this.navbarContainer,ma)&&(Q(this.navbarContainer,ma),this._transparent=!0)}},{name:"hide",el:({dropContainer:t})=>t,async handler(){await new Promise((t=>setTimeout(t))),!this.getActive()&&this._transparent&&(K(this.navbarContainer,ma),this._transparent=null)}}],methods:{getTransparentMode(t){if(!this.navbarContainer)return;if(this.dropbar&&this.isDropbarDrop(t))return this.dropbarTransparentMode;const e=this.getDropdown(t);return e&&et(t,"bdt-dropbar")?e.inset?"behind":"remove":void 0},getDropbarOffset(t){const{top:e,height:i}=He(this.navbarContainer);return e+("behind"===this.dropbarTransparentMode?0:i+t)}}};var ba={mixins:[ns],args:"mode",props:{mode:String,flip:Boolean,overlay:Boolean,swiping:Boolean},data:{mode:"slide",flip:!1,overlay:!1,clsPage:"bdt-offcanvas-page",clsContainer:"bdt-offcanvas-container",selPanel:".bdt-offcanvas-bar",clsFlip:"bdt-offcanvas-flip",clsContainerAnimation:"bdt-offcanvas-container-animation",clsSidebarAnimation:"bdt-offcanvas-bar-animation",clsMode:"bdt-offcanvas",clsOverlay:"bdt-offcanvas-overlay",selClose:".bdt-offcanvas-close",container:!1,swiping:!0},computed:{clsFlip:({flip:t,clsFlip:e})=>t?e:"",clsOverlay:({overlay:t,clsOverlay:e})=>t?e:"",clsMode:({mode:t,clsMode:e})=>`${e}-${t}`,clsSidebarAnimation:({mode:t,clsSidebarAnimation:e})=>"none"===t||"reveal"===t?"":e,clsContainerAnimation:({mode:t,clsContainerAnimation:e})=>"push"!==t&&"reveal"!==t?"":e,transitionElement({mode:t}){return"reveal"===t?Ct(this.panel):this.panel}},observe:bn({filter:({swiping:t})=>t}),update:{read(){this.isToggled()&&!xt(this.$el)&&this.hide()},events:["resize"]},events:[{name:"touchmove",self:!0,passive:!1,filter:({overlay:t})=>t,handler(t){t.cancelable&&t.preventDefault()}},{name:"show",self:!0,handler(){"reveal"===this.mode&&!et(Ct(this.panel),this.clsMode)&&(Ce(this.panel,"<div>"),K(Ct(this.panel),this.clsMode));const{body:t,scrollingElement:e}=document;K(t,this.clsContainer,this.clsFlip),re(t,"touch-action","pan-y pinch-zoom"),re(this.$el,"display","block"),re(this.panel,"maxWidth",e.clientWidth),K(this.$el,this.clsOverlay),K(this.panel,this.clsSidebarAnimation,"reveal"===this.mode?"":this.clsMode),Le(t),K(t,this.clsContainerAnimation),this.clsContainerAnimation&&(wa().content+=",user-scalable=0")}},{name:"hide",self:!0,handler(){Q(document.body,this.clsContainerAnimation),re(document.body,"touch-action","")}},{name:"hidden",self:!0,handler(){this.clsContainerAnimation&&function(){const t=wa();t.content=t.content.replace(/,user-scalable=0$/,"")}(),"reveal"===this.mode&&Ee(this.panel),Q(this.panel,this.clsSidebarAnimation,this.clsMode),Q(this.$el,this.clsOverlay),re(this.$el,"display",""),re(this.panel,"maxWidth",""),Q(document.body,this.clsContainer,this.clsFlip)}},{name:"swipeLeft swipeRight",handler(t){this.isToggled()&&h(t.type,"Left")^this.flip&&this.hide()}}]};function wa(){return Pe('meta[name="viewport"]',document.head)||xe(document.head,'<meta name="viewport">')}var $a={mixins:[Qi],props:{selContainer:String,selContent:String,minHeight:Number},data:{selContainer:".bdt-modal",selContent:".bdt-modal-dialog",minHeight:150},computed:{container:({selContainer:t},e)=>e.closest(t),content:({selContent:t},e)=>e.closest(t)},observe:un({target:({container:t,content:e})=>[t,e]}),update:{read(){return!!(this.content&&this.container&&xt(this.$el))&&{max:Math.max(this.minHeight,Le(this.container)-(ze(this.content).height-Le(this.$el)))}},write({max:t}){re(this.$el,{minHeight:this.minHeight,maxHeight:t})},events:["resize"]}},xa={props:["width","height"],connected(){K(this.$el,"bdt-responsive-width"),re(this.$el,"aspectRatio",`${this.width}/${this.height}`)}},ya={props:{offset:Number},data:{offset:0},connected(){!function(t){Sa.size||Xt(document,"click",Ia),Sa.add(t)}(this)},disconnected(){!function(t){Sa.delete(t),Sa.size||Jt(document,"click",Ia)}(this)},methods:{async scrollTo(t){t=t&&Pe(t)||document.body,Zt(this.$el,"beforescroll",[this,t])&&(await Ci(t,{offset:this.offset}),Zt(this.$el,"scrolled",[this,t]))}}};const Sa=new Set;function Ia(t){if(!t.defaultPrevented)for(const e of Sa)e.$el.contains(t.target)&&Mt(e.$el)&&(t.preventDefault(),window.location.href!==e.$el.href&&window.history.pushState({},"",e.$el.href),e.scrollTo(Pt(e.$el)))}const ka="bdt-scrollspy-inview";var Ca={args:"cls",props:{cls:String,target:String,hidden:Boolean,margin:String,repeat:Boolean,delay:Number},data:()=>({cls:"",target:!1,hidden:!0,margin:"-1px",repeat:!1,delay:0}),computed:{elements:({target:t},e)=>t?Oe(t,e):[e]},watch:{elements(t){this.hidden&&re(Tt(t,`:not(.${ka})`),"opacity",0)}},connected(){this.elementData=new Map},disconnected(){for(const[t,e]of this.elementData.entries())Q(t,ka,(null==e?void 0:e.cls)||"");delete this.elementData},observe:fn({target:({elements:t})=>t,handler(t){const e=this.elementData;for(const{target:i,isIntersecting:n}of t){e.has(i)||e.set(i,{cls:at(i,"bdt-scrollspy-class")||this.cls});const t=e.get(i);!this.repeat&&t.show||(t.show=n)}this.$emit()},options:({margin:t})=>({rootMargin:t}),args:{intersecting:!1}}),update:[{write(t){for(const[e,i]of this.elementData.entries())!i.show||i.inview||i.queued?!i.show&&i.inview&&!i.queued&&this.repeat&&this.toggle(e,!1):(i.queued=!0,t.promise=(t.promise||Promise.resolve()).then((()=>new Promise((t=>setTimeout(t,this.delay))))).then((()=>{this.toggle(e,!0),setTimeout((()=>{i.queued=!1,this.$emit()}),300)})))}}],methods:{toggle(t,e){var i,n;const s=null==(i=this.elementData)?void 0:i.get(t);if(!s)return;let o;if(null==(n=s.off)||n.call(s),re(t,"opacity",!e&&this.hidden?0:""),it(t,ka,e),it(t,s.cls),o=s.cls.match(/\bbdt-animation-[\w-]+/g)){const i=()=>Q(t,o);e?s.off=Gt(t,"animationcancel animationend",i,{self:!0}):i()}Zt(t,e?"inview":"outview"),s.inview=e}}},Ta={props:{cls:String,closest:Boolean,scroll:Boolean,target:String,offset:Number},data:{cls:"bdt-active",closest:!1,scroll:!1,target:'a[href]:not([role="button"])',offset:0},computed:{links:({target:t},e)=>Oe(t,e).filter((t=>Mt(t))),elements({closest:t}){return this.links.map((e=>e.closest(t||"*")))}},watch:{links(t){this.scroll&&this.$create("scroll",t,{offset:this.offset})}},observe:[fn(),vn()],update:[{read(){const t=this.links.map((t=>Pt(t)||t.ownerDocument)),{length:e}=t;if(!e||!xt(this.$el))return!1;const i=Ai(t,!0),{scrollTop:n,scrollHeight:s}=i,o=_i(i);let r=!1;if(n>=s-o.height)r=e-1;else{const e=this.offset+ze(Mi()).height+.1*o.height;for(let i=0;i<t.length&&!(He(t[i]).top-o.top-e>0);i++)r=+i}return{active:r}},write({active:t}){const e=!1!==t&&!et(this.elements[t],this.cls);this.links.forEach((t=>t.blur()));for(let e=0;e<this.elements.length;e++)it(this.elements[e],this.cls,+e===t);e&&Zt(this.$el,"active",[t,this.elements[t]])},events:["scroll","resize"]}]},Ea={mixins:[Qi,ro],props:{position:String,top:null,bottom:null,start:null,end:null,offset:String,overflowFlip:Boolean,animation:String,clsActive:String,clsInactive:String,clsFixed:String,clsBelow:String,selTarget:String,showOnUp:Boolean,targetOffset:Number},data:{position:"top",top:!1,bottom:!1,start:!1,end:!1,offset:0,overflowFlip:!1,animation:"",clsActive:"bdt-active",clsInactive:"",clsFixed:"bdt-sticky-fixed",clsBelow:"bdt-sticky-below",selTarget:"",showOnUp:!1,targetOffset:!1},computed:{target:({selTarget:t},e)=>t&&Pe(t,e)||e},connected(){this.start=Da(this.start||this.top),this.end=Da(this.end||this.bottom),this.placeholder=Pe("+ .bdt-sticky-placeholder",this.$el)||Pe('<div class="bdt-sticky-placeholder"></div>'),this.isFixed=!1,this.setActive(!1)},beforeDisconnect(){this.isFixed&&(this.hide(),Q(this.target,this.clsInactive)),_a(this.$el),ke(this.placeholder),this.placeholder=null},observe:[mn(),vn({target:()=>document.scrollingElement}),un({target:({$el:t})=>[t,Oa(t),document.scrollingElement],handler(t){this.$emit(this._data.resized&&t.some((({target:t})=>t===Oa(this.$el)))?"update":"resize"),this._data.resized=!0}})],events:[{name:"load hashchange popstate",el:()=>window,filter:({targetOffset:t})=>!1!==t,handler(){const{scrollingElement:t}=document;!location.hash||0===t.scrollTop||setTimeout((()=>{const e=He(Pe(location.hash)),i=He(this.$el);this.isFixed&&R(e,i)&&(t.scrollTop=Math.ceil(e.top-i.height-Ue(this.targetOffset,"height",this.placeholder)-Ue(this.offset,"height",this.placeholder)))}))}}],update:[{read({height:t,width:e,margin:i,sticky:n},s){if(this.inactive=!this.matchMedia||!xt(this.$el)||!this.$el.offsetHeight,this.inactive)return;const o=this.isFixed&&s.has("update");o&&(Pa(this.target),this.hide()),this.active||(({height:t,width:e}=ze(this.$el)),i=re(this.$el,"margin")),o&&this.show();const r=Ue("100vh","height"),a=Le(window),l=Math.max(0,document.scrollingElement.scrollHeight-r);let h=this.position;this.overflowFlip&&t>r&&(h="top"===h?"bottom":"top");const c=this.isFixed?this.placeholder:this.$el;let d=Ue(this.offset,"height",n?this.$el:c);"bottom"===h&&(t<a||this.overflowFlip)&&(d+=a-t);const u=this.overflowFlip?0:Math.max(0,t+d-r),f=He(c).top,p=ze(this.$el).height,g=(!1===this.start?f:Aa(this.start,this.$el,f))-d,m=!1===this.end?l:Math.min(l,Aa(this.end,this.$el,f+t,!0)-p-d+u);return n=l&&!this.showOnUp&&g+d===f&&m===Math.min(l,Aa(!0,this.$el,0,!0)-p-d+u)&&"visible"===re(Oa(this.$el),"overflowY"),{start:g,end:m,offset:d,overflow:u,height:t,elHeight:p,width:e,margin:i,top:je(c)[0],sticky:n,viewport:r,maxScrollHeight:l}},write({height:t,width:e,margin:i,offset:n,sticky:s}){if((this.inactive||s||!this.isFixed)&&_a(this.$el),this.inactive)return;s&&(t=e=i=0,re(this.$el,{position:"sticky",top:n}));const{placeholder:o}=this;re(o,{height:t,width:e,margin:i}),(Ct(o)!==Ct(this.$el)||s^_t(o)<_t(this.$el))&&((s?ye:Se)(this.$el,o),o.hidden=!0)},events:["resize"]},{read({scroll:t=0,dir:e="down",overflow:i,overflowScroll:n=0,start:s,end:o,elHeight:r,height:a,sticky:l,maxScrollHeight:h}){const c=Math.min(document.scrollingElement.scrollTop,h),d=t<=c?"down":"up",u=this.isFixed?this.placeholder:this.$el;return{dir:d,prevDir:e,scroll:c,prevScroll:t,below:c>He(u).top+(l?Math.min(a,r):a),offsetParentTop:He(u.offsetParent).top,overflowScroll:q(n+q(c,s,o)-q(t,s,o),0,i)}},write(t,e){const i=e.has("scroll"),{initTimestamp:n=0,dir:s,prevDir:o,scroll:r,prevScroll:a=0,top:l,start:h,below:c}=t;if(r<0||r===a&&i||this.showOnUp&&!i&&!this.isFixed)return;const d=Date.now();if((d-n>300||s!==o)&&(t.initScroll=r,t.initTimestamp=d),!(this.showOnUp&&!this.isFixed&&Math.abs(t.initScroll-r)<=30&&Math.abs(a-r)<=10))if(this.inactive||r<h||this.showOnUp&&(r<=h||"down"===s&&i||"up"===s&&!this.isFixed&&!c)){if(!this.isFixed)return void(me.inProgress(this.$el)&&l>r&&(me.cancel(this.$el),this.hide()));if(this.animation&&c){if(et(this.$el,"bdt-animation-leave"))return;me.out(this.$el,this.animation).then((()=>this.hide()),V)}else this.hide()}else this.isFixed?this.update():this.animation&&c?(this.show(),me.in(this.$el,this.animation).catch(V)):(Pa(this.target),this.show())},events:["resize","resizeViewport","scroll"]}],methods:{show(){this.isFixed=!0,this.update(),this.placeholder.hidden=!1},hide(){const{offset:t,sticky:e}=this._data;this.setActive(!1),Q(this.$el,this.clsFixed,this.clsBelow),e?re(this.$el,"top",t):re(this.$el,{position:"",top:"",width:"",marginTop:""}),this.placeholder.hidden=!0,this.isFixed=!1},update(){let{width:t,scroll:e=0,overflow:i,overflowScroll:n=0,start:s,end:o,offset:r,offsetParentTop:a,sticky:l,below:h}=this._data;const c=0!==s||e>s;if(!l){let s="fixed";e>o&&(r+=o-a+n-i,s="absolute"),re(this.$el,{position:s,width:t,marginTop:0},"important")}re(this.$el,"top",r-n),this.setActive(c),it(this.$el,this.clsBelow,h),K(this.$el,this.clsFixed)},setActive(t){const e=this.active;this.active=t,t?(tt(this.target,this.clsInactive,this.clsActive),e!==t&&Zt(this.$el,"active")):(tt(this.target,this.clsActive,this.clsInactive),e!==t&&(Pa(this.target),Zt(this.$el,"inactive")))}}};function Aa(t,e,i,n){if(!t)return 0;if(C(t)||I(t)&&t.match(/^-?\d/))return i+Ue(t,"height",e,!0);{const i=!0===t?Oa(e):Ot(t,e);return He(i).bottom-(n&&null!=i&&i.contains(e)?_(re(i,"paddingBottom")):0)}}function Da(t){return"true"===t||"false"!==t&&t}function _a(t){re(t,{position:"",top:"",marginTop:"",width:""})}const Ma="bdt-transition-disable";function Pa(t){et(t,Ma)||(K(t,Ma),requestAnimationFrame((()=>Q(t,Ma))))}function Oa(t){for(;t=Ct(t);)if(xt(t))return t}const Ba=".bdt-disabled *, .bdt-disabled, [disabled]";var Na={mixins:[Kn],args:"connect",props:{connect:String,toggle:String,itemNav:String,active:Number,followFocus:Boolean,swiping:Boolean},data:{connect:"~.bdt-switcher",toggle:"> * > :first-child",itemNav:!1,active:0,cls:"bdt-active",attrItem:"bdt-switcher-item",selVertical:".bdt-nav",followFocus:!1,swiping:!0},computed:{connects:{get:({connect:t},e)=>Bt(t,e),observe:({connect:t})=>t},connectChildren(){return this.connects.map((t=>Dt(t))).flat()},toggles:({toggle:t},e)=>Oe(t,e),children(t,e){return Dt(e).filter((t=>this.toggles.some((e=>t.contains(e)))))}},watch:{connects(t){this.swiping&&re(t,"touchAction","pan-y pinch-zoom"),this.$emit()},connectChildren(){let t=Math.max(0,this.index());for(const e of this.connects)Dt(e).forEach(((e,i)=>it(e,this.cls,i===t)));this.$emit()},toggles(t){this.$emit();const e=this.index();this.show(~e?e:t[this.active]||t[0])}},connected(){st(this.$el,"role","tablist")},observe:[gn({targets:({connectChildren:t})=>t}),bn({target:({connects:t})=>t,filter:({swiping:t})=>t})],events:[{name:"click keydown",delegate:({toggle:t})=>t,handler(t){!Et(t.current,Ba)&&("click"===t.type||t.keyCode===zn)&&(t.preventDefault(),this.show(t.current))}},{name:"keydown",delegate:({toggle:t})=>t,handler(t){const{current:e,keyCode:i}=t,n=Et(this.$el,this.selVertical);let s=i===Fn?0:i===Hn?"last":i===jn&&!n||i===Ln&&n?"previous":i===Wn&&!n||i===qn&&n?"next":-1;if(~s){t.preventDefault();const i=this.toggles.filter((t=>!Et(t,Ba))),n=i[G(s,i,i.indexOf(e))];n.focus(),this.followFocus&&this.show(n)}}},{name:"click",el:({$el:t,connects:e,itemNav:i})=>e.concat(i?Bt(i,t):[]),delegate:({attrItem:t})=>`[${t}],[data-${t}]`,handler(t){t.target.closest("a,button")&&(t.preventDefault(),this.show(at(t.current,this.attrItem)))}},{name:"swipeRight swipeLeft",filter:({swiping:t})=>t,el:({connects:t})=>t,handler({type:t}){this.show(h(t,"Left")?"next":"previous")}}],update(){var t;for(const t of this.connects)ve(t,"ul")&&st(t,"role","presentation");st(Dt(this.$el),"role","presentation");for(const e in this.toggles){const i=this.toggles[e],n=null==(t=this.connects[0])?void 0:t.children[e];st(i,"role","tab"),n&&(i.id=Js(this,i),n.id=Js(this,n),st(i,"aria-controls",n.id),st(n,{role:"tabpanel","aria-labelledby":i.id}))}st(this.$el,"aria-orientation",Et(this.$el,this.selVertical)?"vertical":null)},methods:{index(){return d(this.children,(t=>et(t,this.cls)))},show(t){const e=this.toggles.filter((t=>!Et(t,Ba))),i=this.index(),n=G(!$(t)||c(e,t)?t:0,e,G(this.toggles[i],e)),s=G(e[n],this.toggles);this.children.forEach(((t,e)=>{it(t,this.cls,s===e),st(this.toggles[e],{"aria-selected":s===e,tabindex:s===e?null:-1})}));const o=i>=0&&i!==n;this.connects.forEach((async({children:t})=>{const e=f(t).filter(((t,e)=>e!==s&&et(t,this.cls)));await this.toggleElement(e,!1,o)&&await this.toggleElement(t[s],!0,o)}))}}},za={mixins:[Qi],extends:Na,props:{media:Boolean},data:{media:960,attrItem:"bdt-tab-item",selVertical:".bdt-tab-left,.bdt-tab-right"},connected(){const t=et(this.$el,"bdt-tab-left")?"bdt-tab-left":!!et(this.$el,"bdt-tab-right")&&"bdt-tab-right";t&&this.$create("toggle",this.$el,{cls:t,mode:"media",media:this.media})}};var Ha={mixins:[ro,Kn],args:"target",props:{href:String,target:null,mode:"list",queued:Boolean},data:{href:!1,target:!1,mode:"click",queued:!0},computed:{target:{get:({target:t},e)=>(t=Bt(t||e.hash,e)).length?t:[e],observe:({target:t})=>t}},connected(){c(this.mode,"media")||(kt(this.$el)||st(this.$el,"tabindex","0"),!this.cls&&ve(this.$el,"a")&&st(this.$el,"role","button"))},observe:gn({targets:({target:t})=>t}),events:[{name:ut,filter:({mode:t})=>c(t,"hover"),handler(t){this._preventClick=null,ne(t)&&!S(this._showState)&&!this.$el.disabled&&(Zt(this.$el,"focus"),Gt(document,ut,(()=>Zt(this.$el,"blur")),!0,(t=>!this.$el.contains(t.target))),c(this.mode,"click")&&(this._preventClick=!0))}},{name:`mouseenter mouseleave ${gt} ${mt} focus blur`,filter:({mode:t})=>c(t,"hover"),handler(t){if(ne(t)||this.$el.disabled)return;const e=c(["mouseenter",gt,"focus"],t.type),i=this.isToggled(this.target);e||!(!S(this._showState)||"blur"!==t.type&&Et(this.$el,":focus")||"blur"===t.type&&Et(this.$el,":hover"))?e&&S(this._showState)&&i!==this._showState||(this._showState=e?i:null,this.toggle("toggle"+(e?"show":"hide"))):i===this._showState&&(this._showState=null)}},{name:"keydown",filter:({$el:t,mode:e})=>c(e,"click")&&!ve(t,"input"),handler(t){32===t.keyCode&&(t.preventDefault(),this.$el.click())}},{name:"click",filter:({mode:t})=>["click","hover"].some((e=>c(t,e))),handler(t){let e;(this._preventClick||t.target.closest('a[href="#"], a[href=""]')||(e=t.target.closest("a[href]"))&&(!this.isToggled(this.target)||e.hash&&Et(this.target,e.hash)))&&t.preventDefault(),!this._preventClick&&c(this.mode,"click")&&this.toggle()}},{name:"mediachange",filter:({mode:t})=>c(t,"media"),el:({target:t})=>t,handler(t,e){e.matches^this.isToggled(this.target)&&this.toggle()}}],methods:{async toggle(t){if(!Zt(this.target,t||"toggle",[this]))return;if(ot(this.$el,"aria-expanded")&&st(this.$el,"aria-expanded",!this.isToggled(this.target)),!this.queued)return this.toggleElement(this.target);const e=this.target.filter((t=>et(t,this.clsLeave)));if(e.length){for(const t of this.target){const i=c(e,t);this.toggleElement(t,i,i)}return}const i=this.target.filter(this.isToggled);await this.toggleElement(i,!1)&&await this.toggleElement(this.target.filter((t=>!c(i,t))),!0)}}};return H(Object.freeze({__proto__:null,Accordion:lr,Alert:cr,Close:Kr,Cover:fr,Drop:mr,DropParentIcon:Rr,Dropdown:mr,Dropnav:yr,FormCustom:Ir,Grid:kr,HeightMatch:Er,HeightPlaceholder:_r,HeightViewport:Mr,Icon:Vr,Img:oa,Inverse:ca,Leader:fa,Margin:xn,Marker:Qr,Modal:pa,Nav:ga,NavParentIcon:Ur,Navbar:va,NavbarParentIcon:Rr,NavbarToggleIcon:Zr,Offcanvas:ba,OverflowAuto:$a,OverlayIcon:Rr,PaginationNext:ea,PaginationPrevious:ia,Responsive:xa,Scroll:ya,Scrollspy:Ca,ScrollspyNav:Ta,SearchIcon:Yr,SlidenavNext:Gr,SlidenavPrevious:Gr,Spinner:Xr,Sticky:Ea,Svg:zr,Switcher:Na,Tab:za,Toggle:Ha,Totop:ta,Video:ur}),((t,e)=>Fs.component(e,t))),function(t){lt&&window.MutationObserver&&(document.body?requestAnimationFrame((()=>ir(t))):new MutationObserver(((e,i)=>{document.body&&(ir(t),i.disconnect())})).observe(document.documentElement,{childList:!0}))}(Fs),H(er,((t,e)=>Fs.component(e,t))),Fs}));