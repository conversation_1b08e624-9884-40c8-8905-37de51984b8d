<!doctype html>
<html lang="en-US"
    class="no-featured-area is-header-small is-body-full-width is-single-post-title-default is-post-title-align-center is-post-media-fixed is-blog-text-align-left is-meta-with-icons is-header-light is-header-full-width is-header-parallax-no is-menu-sticky is-menu-fixed-width is-menu-align-center is-menu-light is-submenu-light is-submenu-align-left is-menu-uppercase is-featured-area-full is-slider-buttons-center-margin is-slider-buttons-rounded is-slider-buttons-dark is-slider-title-label is-slider-parallax is-slider-title-none-uppercase is-slider-more-link-show-on-hover is-slider-more-link-border-bottom is-slider-text-align-center is-slider-v-align-center is-slider-h-align-center is-link-box-title-default is-link-box-title-transform-none is-link-box-text-align-center is-link-box-v-align-center is-link-box-parallax is-intro-align-center is-intro-text-dark is-intro-parallax-no is-more-link-border-bottom-light is-about-author-minimal is-related-posts-parallax is-related-posts-fixed is-share-links-boxed is-tagcloud-minimal is-nav-single-rounded is-nav-single-no-animated is-comments-minimal is-comments-image-rounded is-comment-form-boxed is-comment-form-border is-sidebar-right is-sidebar-sticky is-sidebar-align-left is-widget-title-align-left is-widget-bottomline is-trending-posts-default is-footer-subscribe-light is-footer-widgets-align-left is-footer-full-width is-meta-uppercase is-slider-dots-rounded-line-grow is-site-title-uppercase is-top-bar-mobile-left-visible is-top-bar-uppercase is-top-bar-full is-sub-menu-ani-fade-in-left is-menu-hover-badge is-menu-hover-badge-round is-copyright-uppercase is-logo-bg-stretch-left is-header-sticky-shadow-soft-shorter is-header-transparent-border-bottom is-header-bg-blur-slightly is-footer-border-top is-footer-border-light"
    data-title-ratio="0.7" data-link-box-title-ratio="0.5" data-generic-button-style="" data-header-bg-shape="">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="profile" href="http://gmpg.org/xfn/11">
    <title>Services &#8211; Motiff Square</title>
    <meta name='robots' content='max-image-preview:large' />
    <style>
        img:is([sizes="auto" i], [sizes^="auto," i]) {
            contain-intrinsic-size: 3000px 1500px
        }
    </style>
    <link rel='dns-prefetch' href='//fonts.googleapis.com' />
    <link rel="alternate" type="application/rss+xml" title="Motiff Square &raquo; Feed"
        href="https://themes.pixelwars.org/interique/demo-01/feed/" />
    <link rel="alternate" type="application/rss+xml" title="Motiff Square &raquo; Comments Feed"
        href="https://themes.pixelwars.org/interique/demo-01/comments/feed/" />
    <script type="text/javascript">
        /* <![CDATA[ */
        window._wpemojiSettings = { "baseUrl": "https:\/\/s.w.org\/images\/core\/emoji\/15.1.0\/72x72\/", "ext": ".png", "svgUrl": "https:\/\/s.w.org\/images\/core\/emoji\/15.1.0\/svg\/", "svgExt": ".svg", "source": { "concatemoji": "https:\/\/themes.pixelwars.org\/interique\/demo-01\/wp-includes\/js\/wp-emoji-release.min.js?ver=6.8.1" } };
        /*! This file is auto-generated */
        !function (i, n) { var o, s, e; function c(e) { try { var t = { supportTests: e, timestamp: (new Date).valueOf() }; sessionStorage.setItem(o, JSON.stringify(t)) } catch (e) { } } function p(e, t, n) { e.clearRect(0, 0, e.canvas.width, e.canvas.height), e.fillText(t, 0, 0); var t = new Uint32Array(e.getImageData(0, 0, e.canvas.width, e.canvas.height).data), r = (e.clearRect(0, 0, e.canvas.width, e.canvas.height), e.fillText(n, 0, 0), new Uint32Array(e.getImageData(0, 0, e.canvas.width, e.canvas.height).data)); return t.every(function (e, t) { return e === r[t] }) } function u(e, t, n) { switch (t) { case "flag": return n(e, "\ud83c\udff3\ufe0f\u200d\u26a7\ufe0f", "\ud83c\udff3\ufe0f\u200b\u26a7\ufe0f") ? !1 : !n(e, "\ud83c\uddfa\ud83c\uddf3", "\ud83c\uddfa\u200b\ud83c\uddf3") && !n(e, "\ud83c\udff4\udb40\udc67\udb40\udc62\udb40\udc65\udb40\udc6e\udb40\udc67\udb40\udc7f", "\ud83c\udff4\u200b\udb40\udc67\u200b\udb40\udc62\u200b\udb40\udc65\u200b\udb40\udc6e\u200b\udb40\udc67\u200b\udb40\udc7f"); case "emoji": return !n(e, "\ud83d\udc26\u200d\ud83d\udd25", "\ud83d\udc26\u200b\ud83d\udd25") }return !1 } function f(e, t, n) { var r = "undefined" != typeof WorkerGlobalScope && self instanceof WorkerGlobalScope ? new OffscreenCanvas(300, 150) : i.createElement("canvas"), a = r.getContext("2d", { willReadFrequently: !0 }), o = (a.textBaseline = "top", a.font = "600 32px Arial", {}); return e.forEach(function (e) { o[e] = t(a, e, n) }), o } function t(e) { var t = i.createElement("script"); t.src = e, t.defer = !0, i.head.appendChild(t) } "undefined" != typeof Promise && (o = "wpEmojiSettingsSupports", s = ["flag", "emoji"], n.supports = { everything: !0, everythingExceptFlag: !0 }, e = new Promise(function (e) { i.addEventListener("DOMContentLoaded", e, { once: !0 }) }), new Promise(function (t) { var n = function () { try { var e = JSON.parse(sessionStorage.getItem(o)); if ("object" == typeof e && "number" == typeof e.timestamp && (new Date).valueOf() < e.timestamp + 604800 && "object" == typeof e.supportTests) return e.supportTests } catch (e) { } return null }(); if (!n) { if ("undefined" != typeof Worker && "undefined" != typeof OffscreenCanvas && "undefined" != typeof URL && URL.createObjectURL && "undefined" != typeof Blob) try { var e = "postMessage(" + f.toString() + "(" + [JSON.stringify(s), u.toString(), p.toString()].join(",") + "));", r = new Blob([e], { type: "text/javascript" }), a = new Worker(URL.createObjectURL(r), { name: "wpTestEmojiSupports" }); return void (a.onmessage = function (e) { c(n = e.data), a.terminate(), t(n) }) } catch (e) { } c(n = f(s, u, p)) } t(n) }).then(function (e) { for (var t in e) n.supports[t] = e[t], n.supports.everything = n.supports.everything && n.supports[t], "flag" !== t && (n.supports.everythingExceptFlag = n.supports.everythingExceptFlag && n.supports[t]); n.supports.everythingExceptFlag = n.supports.everythingExceptFlag && !n.supports.flag, n.DOMReady = !1, n.readyCallback = function () { n.DOMReady = !0 } }).then(function () { return e }).then(function () { var e; n.supports.everything || (n.readyCallback(), (e = n.source || {}).concatemoji ? t(e.concatemoji) : e.wpemoji && e.twemoji && (t(e.twemoji), t(e.wpemoji))) })) }((window, document), window._wpemojiSettings);
        /* ]]> */
    </script>
    <style id='wp-emoji-styles-inline-css' type='text/css'>
        img.wp-smiley,
        img.emoji {
            display: inline !important;
            border: none !important;
            box-shadow: none !important;
            height: 1em !important;
            width: 1em !important;
            margin: 0 0.07em !important;
            vertical-align: -0.1em !important;
            background: none !important;
            padding: 0 !important;
        }
    </style>
    <style id='classic-theme-styles-inline-css' type='text/css'>
        /*! This file is auto-generated */
        .wp-block-button__link {
            color: #fff;
            background-color: #32373c;
            border-radius: 9999px;
            box-shadow: none;
            text-decoration: none;
            padding: calc(.667em + 2px) calc(1.333em + 2px);
            font-size: 1.125em
        }

        .wp-block-file__button {
            background: #32373c;
            color: #fff;
            text-decoration: none
        }
    </style>
    <style id='global-styles-inline-css' type='text/css'>
        :root {
            --wp--preset--aspect-ratio--square: 1;
            --wp--preset--aspect-ratio--4-3: 4/3;
            --wp--preset--aspect-ratio--3-4: 3/4;
            --wp--preset--aspect-ratio--3-2: 3/2;
            --wp--preset--aspect-ratio--2-3: 2/3;
            --wp--preset--aspect-ratio--16-9: 16/9;
            --wp--preset--aspect-ratio--9-16: 9/16;
            --wp--preset--color--black: #000000;
            --wp--preset--color--cyan-bluish-gray: #abb8c3;
            --wp--preset--color--white: #ffffff;
            --wp--preset--color--pale-pink: #f78da7;
            --wp--preset--color--vivid-red: #cf2e2e;
            --wp--preset--color--luminous-vivid-orange: #ff6900;
            --wp--preset--color--luminous-vivid-amber: #fcb900;
            --wp--preset--color--light-green-cyan: #7bdcb5;
            --wp--preset--color--vivid-green-cyan: #00d084;
            --wp--preset--color--pale-cyan-blue: #8ed1fc;
            --wp--preset--color--vivid-cyan-blue: #0693e3;
            --wp--preset--color--vivid-purple: #9b51e0;
            --wp--preset--gradient--vivid-cyan-blue-to-vivid-purple: linear-gradient(135deg, rgba(6, 147, 227, 1) 0%, rgb(155, 81, 224) 100%);
            --wp--preset--gradient--light-green-cyan-to-vivid-green-cyan: linear-gradient(135deg, rgb(122, 220, 180) 0%, rgb(0, 208, 130) 100%);
            --wp--preset--gradient--luminous-vivid-amber-to-luminous-vivid-orange: linear-gradient(135deg, rgba(252, 185, 0, 1) 0%, rgba(255, 105, 0, 1) 100%);
            --wp--preset--gradient--luminous-vivid-orange-to-vivid-red: linear-gradient(135deg, rgba(255, 105, 0, 1) 0%, rgb(207, 46, 46) 100%);
            --wp--preset--gradient--very-light-gray-to-cyan-bluish-gray: linear-gradient(135deg, rgb(238, 238, 238) 0%, rgb(169, 184, 195) 100%);
            --wp--preset--gradient--cool-to-warm-spectrum: linear-gradient(135deg, rgb(74, 234, 220) 0%, rgb(151, 120, 209) 20%, rgb(207, 42, 186) 40%, rgb(238, 44, 130) 60%, rgb(251, 105, 98) 80%, rgb(254, 248, 76) 100%);
            --wp--preset--gradient--blush-light-purple: linear-gradient(135deg, rgb(255, 206, 236) 0%, rgb(152, 150, 240) 100%);
            --wp--preset--gradient--blush-bordeaux: linear-gradient(135deg, rgb(254, 205, 165) 0%, rgb(254, 45, 45) 50%, rgb(107, 0, 62) 100%);
            --wp--preset--gradient--luminous-dusk: linear-gradient(135deg, rgb(255, 203, 112) 0%, rgb(199, 81, 192) 50%, rgb(65, 88, 208) 100%);
            --wp--preset--gradient--pale-ocean: linear-gradient(135deg, rgb(255, 245, 203) 0%, rgb(182, 227, 212) 50%, rgb(51, 167, 181) 100%);
            --wp--preset--gradient--electric-grass: linear-gradient(135deg, rgb(202, 248, 128) 0%, rgb(113, 206, 126) 100%);
            --wp--preset--gradient--midnight: linear-gradient(135deg, rgb(2, 3, 129) 0%, rgb(40, 116, 252) 100%);
            --wp--preset--font-size--small: 13px;
            --wp--preset--font-size--medium: 20px;
            --wp--preset--font-size--large: 36px;
            --wp--preset--font-size--x-large: 42px;
            --wp--preset--spacing--20: 0.44rem;
            --wp--preset--spacing--30: 0.67rem;
            --wp--preset--spacing--40: 1rem;
            --wp--preset--spacing--50: 1.5rem;
            --wp--preset--spacing--60: 2.25rem;
            --wp--preset--spacing--70: 3.38rem;
            --wp--preset--spacing--80: 5.06rem;
            --wp--preset--shadow--natural: 6px 6px 9px rgba(0, 0, 0, 0.2);
            --wp--preset--shadow--deep: 12px 12px 50px rgba(0, 0, 0, 0.4);
            --wp--preset--shadow--sharp: 6px 6px 0px rgba(0, 0, 0, 0.2);
            --wp--preset--shadow--outlined: 6px 6px 0px -3px rgba(255, 255, 255, 1), 6px 6px rgba(0, 0, 0, 1);
            --wp--preset--shadow--crisp: 6px 6px 0px rgba(0, 0, 0, 1);
        }

        :where(.is-layout-flex) {
            gap: 0.5em;
        }

        :where(.is-layout-grid) {
            gap: 0.5em;
        }

        body .is-layout-flex {
            display: flex;
        }

        .is-layout-flex {
            flex-wrap: wrap;
            align-items: center;
        }

        .is-layout-flex> :is(*, div) {
            margin: 0;
        }

        body .is-layout-grid {
            display: grid;
        }

        .is-layout-grid> :is(*, div) {
            margin: 0;
        }

        :where(.wp-block-columns.is-layout-flex) {
            gap: 2em;
        }

        :where(.wp-block-columns.is-layout-grid) {
            gap: 2em;
        }

        :where(.wp-block-post-template.is-layout-flex) {
            gap: 1.25em;
        }

        :where(.wp-block-post-template.is-layout-grid) {
            gap: 1.25em;
        }

        .has-black-color {
            color: var(--wp--preset--color--black) !important;
        }

        .has-cyan-bluish-gray-color {
            color: var(--wp--preset--color--cyan-bluish-gray) !important;
        }

        .has-white-color {
            color: var(--wp--preset--color--white) !important;
        }

        .has-pale-pink-color {
            color: var(--wp--preset--color--pale-pink) !important;
        }

        .has-vivid-red-color {
            color: var(--wp--preset--color--vivid-red) !important;
        }

        .has-luminous-vivid-orange-color {
            color: var(--wp--preset--color--luminous-vivid-orange) !important;
        }

        .has-luminous-vivid-amber-color {
            color: var(--wp--preset--color--luminous-vivid-amber) !important;
        }

        .has-light-green-cyan-color {
            color: var(--wp--preset--color--light-green-cyan) !important;
        }

        .has-vivid-green-cyan-color {
            color: var(--wp--preset--color--vivid-green-cyan) !important;
        }

        .has-pale-cyan-blue-color {
            color: var(--wp--preset--color--pale-cyan-blue) !important;
        }

        .has-vivid-cyan-blue-color {
            color: var(--wp--preset--color--vivid-cyan-blue) !important;
        }

        .has-vivid-purple-color {
            color: var(--wp--preset--color--vivid-purple) !important;
        }

        .has-black-background-color {
            background-color: var(--wp--preset--color--black) !important;
        }

        .has-cyan-bluish-gray-background-color {
            background-color: var(--wp--preset--color--cyan-bluish-gray) !important;
        }

        .has-white-background-color {
            background-color: var(--wp--preset--color--white) !important;
        }

        .has-pale-pink-background-color {
            background-color: var(--wp--preset--color--pale-pink) !important;
        }

        .has-vivid-red-background-color {
            background-color: var(--wp--preset--color--vivid-red) !important;
        }

        .has-luminous-vivid-orange-background-color {
            background-color: var(--wp--preset--color--luminous-vivid-orange) !important;
        }

        .has-luminous-vivid-amber-background-color {
            background-color: var(--wp--preset--color--luminous-vivid-amber) !important;
        }

        .has-light-green-cyan-background-color {
            background-color: var(--wp--preset--color--light-green-cyan) !important;
        }

        .has-vivid-green-cyan-background-color {
            background-color: var(--wp--preset--color--vivid-green-cyan) !important;
        }

        .has-pale-cyan-blue-background-color {
            background-color: var(--wp--preset--color--pale-cyan-blue) !important;
        }

        .has-vivid-cyan-blue-background-color {
            background-color: var(--wp--preset--color--vivid-cyan-blue) !important;
        }

        .has-vivid-purple-background-color {
            background-color: var(--wp--preset--color--vivid-purple) !important;
        }

        .has-black-border-color {
            border-color: var(--wp--preset--color--black) !important;
        }

        .has-cyan-bluish-gray-border-color {
            border-color: var(--wp--preset--color--cyan-bluish-gray) !important;
        }

        .has-white-border-color {
            border-color: var(--wp--preset--color--white) !important;
        }

        .has-pale-pink-border-color {
            border-color: var(--wp--preset--color--pale-pink) !important;
        }

        .has-vivid-red-border-color {
            border-color: var(--wp--preset--color--vivid-red) !important;
        }

        .has-luminous-vivid-orange-border-color {
            border-color: var(--wp--preset--color--luminous-vivid-orange) !important;
        }

        .has-luminous-vivid-amber-border-color {
            border-color: var(--wp--preset--color--luminous-vivid-amber) !important;
        }

        .has-light-green-cyan-border-color {
            border-color: var(--wp--preset--color--light-green-cyan) !important;
        }

        .has-vivid-green-cyan-border-color {
            border-color: var(--wp--preset--color--vivid-green-cyan) !important;
        }

        .has-pale-cyan-blue-border-color {
            border-color: var(--wp--preset--color--pale-cyan-blue) !important;
        }

        .has-vivid-cyan-blue-border-color {
            border-color: var(--wp--preset--color--vivid-cyan-blue) !important;
        }

        .has-vivid-purple-border-color {
            border-color: var(--wp--preset--color--vivid-purple) !important;
        }

        .has-vivid-cyan-blue-to-vivid-purple-gradient-background {
            background: var(--wp--preset--gradient--vivid-cyan-blue-to-vivid-purple) !important;
        }

        .has-light-green-cyan-to-vivid-green-cyan-gradient-background {
            background: var(--wp--preset--gradient--light-green-cyan-to-vivid-green-cyan) !important;
        }

        .has-luminous-vivid-amber-to-luminous-vivid-orange-gradient-background {
            background: var(--wp--preset--gradient--luminous-vivid-amber-to-luminous-vivid-orange) !important;
        }

        .has-luminous-vivid-orange-to-vivid-red-gradient-background {
            background: var(--wp--preset--gradient--luminous-vivid-orange-to-vivid-red) !important;
        }

        .has-very-light-gray-to-cyan-bluish-gray-gradient-background {
            background: var(--wp--preset--gradient--very-light-gray-to-cyan-bluish-gray) !important;
        }

        .has-cool-to-warm-spectrum-gradient-background {
            background: var(--wp--preset--gradient--cool-to-warm-spectrum) !important;
        }

        .has-blush-light-purple-gradient-background {
            background: var(--wp--preset--gradient--blush-light-purple) !important;
        }

        .has-blush-bordeaux-gradient-background {
            background: var(--wp--preset--gradient--blush-bordeaux) !important;
        }

        .has-luminous-dusk-gradient-background {
            background: var(--wp--preset--gradient--luminous-dusk) !important;
        }

        .has-pale-ocean-gradient-background {
            background: var(--wp--preset--gradient--pale-ocean) !important;
        }

        .has-electric-grass-gradient-background {
            background: var(--wp--preset--gradient--electric-grass) !important;
        }

        .has-midnight-gradient-background {
            background: var(--wp--preset--gradient--midnight) !important;
        }

        .has-small-font-size {
            font-size: var(--wp--preset--font-size--small) !important;
        }

        .has-medium-font-size {
            font-size: var(--wp--preset--font-size--medium) !important;
        }

        .has-large-font-size {
            font-size: var(--wp--preset--font-size--large) !important;
        }

        .has-x-large-font-size {
            font-size: var(--wp--preset--font-size--x-large) !important;
        }

        :where(.wp-block-post-template.is-layout-flex) {
            gap: 1.25em;
        }

        :where(.wp-block-post-template.is-layout-grid) {
            gap: 1.25em;
        }

        :where(.wp-block-columns.is-layout-flex) {
            gap: 2em;
        }

        :where(.wp-block-columns.is-layout-grid) {
            gap: 2em;
        }

        :root :where(.wp-block-pullquote) {
            font-size: 1.5em;
            line-height: 1.6;
        }
    </style>
    <link rel='stylesheet' id='fontello-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/pixelwars-core/themes/global/css/fonts/fontello/css/fontello.css'
        type='text/css' media='all' />
    <link rel='stylesheet' id='pixelwars-core-shortcodes-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/pixelwars-core/themes/global/css/shortcodes.css'
        type='text/css' media='all' />
    <link rel='stylesheet' id='qi-addons-for-elementor-grid-style-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/qi-addons-for-elementor/assets/css/grid.min.css?ver=1.8.9'
        type='text/css' media='all' />
    <link rel='stylesheet' id='qi-addons-for-elementor-helper-parts-style-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/qi-addons-for-elementor/assets/css/helper-parts.min.css?ver=1.8.9'
        type='text/css' media='all' />
    <link rel='stylesheet' id='qi-addons-for-elementor-style-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/qi-addons-for-elementor/assets/css/main.min.css?ver=1.8.9'
        type='text/css' media='all' />
    <link rel='stylesheet' id='interique-font-texgyreadventor-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/css/fonts/texgyreadventor/stylesheet.css?ver=6.8.1'
        type='text/css' media='all' />
    <link rel='stylesheet' id='interique-font-now-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/css/fonts/now/stylesheet.css?ver=6.8.1'
        type='text/css' media='all' />
    <link rel='stylesheet' id='interique-fonts-css'
        href='//fonts.googleapis.com/css?family=Jost%3A100%2C100i%2C200%2C200i%2C300%2C300i%2C400%2C400i%2C500%2C500i%2C600%2C600i%2C700%2C700i%2C800%2C800i%2C900%2C900i%7CBricolage+Grotesque%3A100%2C100i%2C200%2C200i%2C300%2C300i%2C400%2C400i%2C500%2C500i%2C600%2C600i%2C700%2C700i%2C800%2C800i%2C900%2C900i&#038;ver=6.8.1'
        type='text/css' media='all' />
    <link rel='stylesheet' id='normalize-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/css/normalize.css?ver=6.8.1'
        type='text/css' media='all' />
    <link rel='stylesheet' id='bootstrap-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/css/bootstrap.css?ver=6.8.1'
        type='text/css' media='all' />
    <link rel='stylesheet' id='fluidbox-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/js/fluidbox/fluidbox.css?ver=6.8.1'
        type='text/css' media='all' />
    <link rel='stylesheet' id='magnific-popup-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/js/jquery.magnific-popup/magnific-popup.css?ver=6.8.1'
        type='text/css' media='all' />
    <link rel='stylesheet' id='owl-carousel-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/js/owl-carousel/owl.carousel.css?ver=6.8.1'
        type='text/css' media='all' />
    <link rel='stylesheet' id='interique-main-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/css/main.css?ver=6.8.1'
        type='text/css' media='all' />
    <link rel='stylesheet' id='interique-768-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/css/768.css?ver=6.8.1'
        type='text/css' media='all' />
    <link rel='stylesheet' id='interique-992-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/css/992.css?ver=6.8.1'
        type='text/css' media='all' />
    <link rel='stylesheet' id='interique-style-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/style.css?ver=6.8.1'
        type='text/css' media='all' />
    <style id='interique-style-inline-css' type='text/css'>
        .site-title {
            font-family: 'TeXGyreAdventor', sans-serif;
        }

        .nav-menu,
        .entry-meta,
        .owl-nav,
        label,
        .page-links,
        .navigation,
        .entry-title i,
        .site-info,
        .filters {
            font-family: 'Jost';
        }

        .widget-title {
            font-family: 'TeXGyreAdventor', sans-serif;
        }

        h1,
        .entry-title,
        .footer-subscribe h3,
        .widget_categories ul li,
        .widget_recent_entries ul li a,
        .widget_pages ul li,
        .widget_nav_menu ul li,
        .widget_archive ul li,
        .widget_most_recommended_posts ul li a,
        .widget_calendar table caption,
        .tptn_title,
        .nav-single a,
        .widget_recent_comments ul li,
        .widget_product_categories ul li,
        .widget_meta ul li,
        .widget_rss ul a.rsswidget {
            font-family: 'Bricolage Grotesque';
        }

        h2,
        h3,
        h4,
        h5,
        h6,
        blockquote,
        .tab-titles {
            font-family: 'Jost';
        }

        .slider-box .entry-title {
            font-family: 'Now', sans-serif;
        }

        body {
            font-family: 'Jost';
        }

        .link-box .entry-title {
            font-family: 'Now', sans-serif;
        }

        .button,
        button,
        html .elementor-button,
        html .ekit-wid-con .elementskit-btn,
        html .ekit-wid-con .ekit_creative_button,
        .more-link {
            font-family: 'Jost';
        }

        .top-bar {
            font-family: 'Jost';
        }

        @media screen and (min-width: 992px) {
            .site-header .site-title {
                font-size: 24px;
            }
        }

        @media screen and (min-width: 992px) {
            .is-header-smaller .site-header.clone .site-title {
                font-size: 24px;
            }
        }

        @media screen and (max-width: 991px) {
            .site-header .site-title {
                font-size: 14px;
            }
        }

        @media screen and (min-width: 992px) {
            .blog-small .entry-title {
                font-size: 24px;
            }
        }

        @media screen and (min-width: 992px) {
            h1 {
                font-size: 102px;
            }
        }

        @media screen and (min-width: 992px) {
            html {
                font-size: 16px;
            }
        }

        @media screen and (max-width: 991px) {
            html {
                font-size: 15px;
            }
        }

        @media screen and (min-width: 992px) {
            .nav-menu>ul {
                font-size: 13px;
            }
        }

        @media screen and (min-width: 992px) {
            .blog-stream .entry-content {
                font-size: 16px;
            }
        }

        @media screen and (min-width: 992px) {
            .blog-stream.blog-small .entry-content {
                font-size: 15px;
            }
        }

        .widget-title {
            font-size: 13px;
        }

        @media screen and (min-width: 992px) {
            .nav-menu ul ul {
                font-size: 12px;
            }
        }

        .top-bar {
            font-size: 12px;
        }

        .site-footer .site-info {
            font-size: 12px;
        }

        .site-title {
            font-weight: 400;
        }

        h1,
        .entry-title,
        .footer-subscribe h3 {
            font-weight: 500;
        }

        h2,
        h3,
        h4,
        h5,
        h6,
        blockquote,
        .comment-meta .fn {
            font-weight: 500;
        }

        .slider-box .entry-title {
            font-weight: 700;
        }

        .widget-title {
            font-weight: 700;
        }

        @media screen and (min-width: 992px) {
            .nav-menu>ul {
                font-weight: 500;
            }
        }

        @media screen and (min-width: 992px) {
            .nav-menu ul ul {
                font-weight: 400;
            }
        }

        .link-box .entry-title {
            font-weight: 700;
        }

        .site-description {
            font-weight: 400;
        }

        .top-bar {
            font-weight: 500;
        }

        .site-footer .site-info {
            font-weight: 500;
        }

        .entry-meta {
            font-weight: 500;
        }

        @media screen and (min-width: 992px) {
            .nav-menu>ul {
                letter-spacing: 0px;
            }
        }

        @media screen and (min-width: 992px) {
            .nav-menu ul ul {
                letter-spacing: 0px;
            }
        }

        .widget-title {
            letter-spacing: 3px;
        }

        .site-footer .site-info {
            letter-spacing: 3px;
        }

        h1,
        .entry-title,
        .footer-subscribe h3,
        .widget_categories ul li,
        .widget_recent_entries ul li,
        .widget_pages ul li,
        .widget_archive ul li,
        .widget_calendar table caption,
        .tptn_title,
        .nav-single a {
            text-transform: none;
        }

        h2,
        h3,
        h4,
        h5,
        h6,
        blockquote,
        .comment-meta .fn {
            text-transform: none;
        }

        @media screen and (min-width: 992px) {
            html {
                line-height: 1.6;
            }
        }

        @media screen and (min-width: 992px) {
            .header-bg-shape {
                height: 50px;
            }
        }

        .header-wrap:after {
            bottom: -1px;
        }

        @media screen and (min-width: 992px) {
            .site-title img {
                max-height: 44px;
            }
        }

        @media screen and (max-width: 991px) {
            .site-title img {
                max-height: 24px;
            }
        }

        .top-bar {
            line-height: 36px;
        }

        html .site-header .site-title a {
            padding: 12px 24px;
        }

        @media screen and (min-width: 992px) {
            .site {
                margin-top: 0px;
                margin-bottom: 0px;
            }
        }

        .layout-medium,
        .is-header-row .header-wrap-inner,
        .is-header-small .header-wrap-inner,
        .is-menu-bar.is-menu-fixed-bg .menu-wrap,
        .is-header-fixed-width .header-wrap,
        .is-header-fixed-width.is-menu-bar .site-navigation,
        .is-header-float-box:not(.is-header-float-box-menu) .site-header:not(.clone) .header-wrap,
        .is-header-float-box.is-menu-bar .site-header:not(.clone) .site-navigation:not(.clone),
        .is-body-boxed .site,
        .is-body-boxed .header-wrap,
        .is-body-boxed.is-menu-bar .site-navigation,
        .is-body-boxed:not(.is-menu-bar) .site-header,
        .is-middle-boxed .site-main,
        .intro-content,
        .is-footer-boxed .site-footer,
        .is-content-boxed .site-main .layout-fixed,
        .top-bar .top-bar-wrap,
        .is-top-bar-fixed .top-bar,
        .is-top-bar-fixed-bg .top-bar,
        .is-menu-bottom.is-menu-bottom-overflow .site-header:not(.clone) .site-navigation:not(.clone) .menu-wrap,
        .site-branding-wrap,
        .is-header-border-fixed .header-wrap:after,
        .is-header-border-fixed .menu-wrap:after,
        html .tutor-container,
        html .lp-content-area,
        html .learn-press-breadcrumb {
            max-width: 1140px;
            margin-left: auto;
            margin-right: auto;
        }

        .layout-fixed,
        .blog-list,
        .blog-regular,
        .is-content-boxed .single .site-content,
        .is-content-boxed .page .site-content {
            max-width: 800px;
        }

        @media screen and (min-width: 992px) {

            .is-header-small .header-wrap,
            .is-menu-bar .nav-menu>ul>li,
            .is-header-vertical .nav-menu>ul>li {
                line-height: 80px;
            }

            .is-header-small .site-branding {
                max-height: 80px;
            }

        }

        @media screen and (min-width: 992px) {

            .is-header-small.is-header-smaller .site-header.clone .header-wrap,
            .is-header-row.is-header-smaller .site-header.clone .nav-menu>ul>li,
            .is-menu-bar.is-header-smaller .site-navigation.clone .nav-menu>ul>li,
            .is-menu-bar.is-header-smaller .site-header.clone .site-navigation .nav-menu>ul>li {
                line-height: 70px;
            }

            .is-header-small.is-header-smaller .site-header.clone .site-branding {
                max-height: 70px;
            }

        }

        .button.is-primary,
        .button.is-primary:after,
        html .elementor-button,
        .elementor-button.elementor-size-xs,
        .elementor-button.elementor-size-sm,
        .elementor-button.elementor-size-md,
        .elementor-button.elementor-size-lg,
        .elementor-button.elementor-size-xl,
        html .ekit-wid-con .elementskit-btn,
        html .ekit-wid-con .ekit_creative_button {
            border-radius: 0px;
        }

        .button.is-secondary,
        .button.is-secondary:after,
        .elementor-element.elementor-button-info .elementor-button {
            border-radius: 30px;
        }

        a {
            color: #d84156;
        }

        a:hover {
            color: #c60035;
        }

        .site-header .header-wrap {
            background-color: #fffefc;
        }

        html:not(.is-menu-bottom) .site-header .header-bg-shape {
            color: #fffefc;
        }

        .header-wrap:before {
            background: #fffefc;
        }

        .header-wrap:before {
            opacity: 0;
        }

        .is-header-half-transparent:not(.is-menu-toggled-on) .site-header:not(.clone) .header-wrap:before {
            opacity: 0.45;
        }

        .header-wrap:after {
            opacity: 0.08;
        }

        .site-header .menu-wrap {
            background-color: #ffffff;
        }

        html.is-menu-bottom .site-header .header-bg-shape {
            color: #ffffff;
        }

        @media screen and (min-width: 992px) {
            .nav-menu>ul>li.current-menu-item>a {
                color: #0a0a0a !important;
            }
        }

        @media screen and (min-width: 992px) {
            .nav-menu li.current-menu-item>a .link-text:before {
                background-color: #bbcfbd !important;
                border-color: #bbcfbd !important;
            }
        }

        @media screen and (min-width: 992px) {

            html .nav-menu>ul>li>a:hover,
            .nav-menu>ul>li.has-submenu:hover>a {
                color: #1c1a17;
            }
        }

        @media screen and (min-width: 992px) {

            html.loaded .nav-menu ul li a .link-text:before,
            .nav-menu li.has-submenu:hover>a .link-text:before {
                background-color: #d6cfc2;
                border-color: #d6cfc2;
            }
        }

        @media screen and (min-width: 992px) {
            .nav-menu ul ul li.current-menu-item>a .link-text {
                color: #0a0606 !important;
            }
        }

        @media screen and (min-width: 992px) {

            html .nav-menu ul ul li a:hover .link-text,
            .nav-menu ul ul li.has-submenu:hover>a .link-text {
                color: #111111;
            }
        }

        h1,
        h2,
        h3,
        h4,
        h5,
        h6,
        blockquote,
        .tab-titles {
            color: #020014;
        }

        body {
            color: #32455e;
        }

        body {
            background: #fffefc;
        }

        .site .footer-subscribe {
            background: #ebe6da;
        }

        .site-footer .site-info {
            background-color: #ffffff;
        }

        .site-footer .site-info {
            color: #0a0a0a;
        }

        .button.is-primary {
            color: #222222;
        }

        .button.is-primary.is-shadow,
        .button.is-primary.is-solid,
        .button.is-primary.is-solid-light,
        html .elementor-button,
        html .ekit-wid-con .elementskit-btn {
            background-color: #222222;
        }

        .button.is-primary.is-shadow {
            box-shadow: 0px 18px 23px -6px #222222;
        }

        .button.is-primary:hover {
            color: #215aed;
        }

        .button.is-primary.is-shadow:hover,
        .button.is-primary.is-solid:hover,
        .button.is-primary.is-solid-light:hover,
        .button.is-primary.is-shift:after,
        .button.is-primary.is-circle:before,
        html .elementor-button:hover,
        html .ekit-wid-con .elementskit-btn:hover {
            background-color: #215aed;
        }

        .button.is-primary.is-shadow:hover {
            box-shadow: 0px 2px 10px -5px #215aed;
        }

        .button.is-secondary {
            color: #f22000;
        }

        .button.is-secondary.is-shadow,
        .button.is-secondary.is-solid,
        .button.is-secondary.is-solid-light,
        .elementor-element.elementor-button-info .elementor-button {
            background-color: #f22000;
        }

        .button.is-secondary.is-shadow {
            box-shadow: 0px 18px 23px -6px #f22000;
        }

        .button.is-secondary:hover {
            color: #0026ff;
        }

        .button.is-secondary.is-shadow:hover,
        .button.is-secondary.is-solid:hover,
        .button.is-secondary.is-solid-light:hover,
        .button.is-secondary.is-shift:after,
        .button.is-secondary.is-circle:before,
        .elementor-element.elementor-button-info .elementor-button:hover {
            background-color: #0026ff;
        }

        .button.is-secondary.is-shadow:hover {
            box-shadow: 0px 2px 10px -5px #0026ff;
        }

        .top-bar,
        .top-bar select option {
            background-color: #262251;
        }

        .top-bar {
            background: linear-gradient(90deg, #262251 30%, #d10600 100%);
        }

        .header-wrap:after {
            color: #222222;
        }

        .is-menu-bar .menu-wrap:after {
            color: #222222;
        }
    </style>
    <link rel='stylesheet' id='swiper-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/qi-addons-for-elementor/assets/plugins/swiper/8.4.5/swiper.min.css?ver=8.4.5'
        type='text/css' media='all' />
    <link rel='stylesheet' id='elementor-frontend-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/elementor/assets/css/frontend.min.css?ver=3.29.0'
        type='text/css' media='all' />
    <link rel='stylesheet' id='elementor-post-6-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/elementor/css/post-6.css?ver=1748102204'
        type='text/css' media='all' />
    <link rel='stylesheet' id='e-animation-fadeIn-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/elementor/assets/lib/animations/styles/fadeIn.min.css?ver=3.29.0'
        type='text/css' media='all' />
    <link rel='stylesheet' id='ps-general-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/bdthemes-prime-slider-lite/assets/css/ps-general.css?ver=3.17.12'
        type='text/css' media='all' />
    <link rel='stylesheet' id='widget-heading-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/elementor/assets/css/widget-heading.min.css?ver=3.29.0'
        type='text/css' media='all' />
    <link rel='stylesheet' id='widget-spacer-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/elementor/assets/css/widget-spacer.min.css?ver=3.29.0'
        type='text/css' media='all' />
    <link rel='stylesheet' id='e-animation-fadeInUp-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/elementor/assets/lib/animations/styles/fadeInUp.min.css?ver=3.29.0'
        type='text/css' media='all' />
    <link rel='stylesheet' id='elementor-post-17804-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/elementor/css/post-17804.css?ver=1748200149'
        type='text/css' media='all' />
    <link rel='stylesheet' id='bdt-uikit-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/bdthemes-prime-slider-lite/assets/css/bdt-uikit.css?ver=3.21.7'
        type='text/css' media='all' />
    <link rel='stylesheet' id='prime-slider-site-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/bdthemes-prime-slider-lite/assets/css/prime-slider-site.css?ver=3.17.12'
        type='text/css' media='all' />
    <link rel='stylesheet' id='elementor-gf-local-roboto-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/elementor/google-fonts/css/roboto.css?ver=1745821354'
        type='text/css' media='all' />
    <link rel='stylesheet' id='elementor-gf-local-robotoslab-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/elementor/google-fonts/css/robotoslab.css?ver=1745821356'
        type='text/css' media='all' />
    <link rel='stylesheet' id='elementor-gf-local-jost-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/elementor/google-fonts/css/jost.css?ver=1745846366'
        type='text/css' media='all' />
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-includes/js/jquery/jquery.min.js?ver=3.7.1"
        id="jquery-core-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-includes/js/jquery/jquery-migrate.min.js?ver=3.4.1"
        id="jquery-migrate-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/bdthemes-prime-slider-lite/assets/js/bdt-uikit.min.js?ver=3.21.7"
        id="bdt-uikit-js"></script>
    <link rel="https://api.w.org/" href="https://themes.pixelwars.org/interique/demo-01/wp-json/" />
    <link rel="alternate" title="JSON" type="application/json"
        href="https://themes.pixelwars.org/interique/demo-01/wp-json/wp/v2/pages/17804" />
    <link rel="EditURI" type="application/rsd+xml" title="RSD"
        href="https://themes.pixelwars.org/interique/demo-01/xmlrpc.php?rsd" />
    <meta name="generator" content="WordPress 6.8.1" />
    <link rel="canonical" href="service.html" />
    <link rel='shortlink' href='https://themes.pixelwars.org/interique/demo-01/?p=17804' />
    <link rel="alternate" title="oEmbed (JSON)" type="application/json+oembed"
        href="https://themes.pixelwars.org/interique/demo-01/wp-json/oembed/1.0/embed?url=https%3A%2F%2Fthemes.pixelwars.org%2Finterique%2Fdemo-01%2Fservices%2F" />
    <link rel="alternate" title="oEmbed (XML)" type="text/xml+oembed"
        href="https://themes.pixelwars.org/interique/demo-01/wp-json/oembed/1.0/embed?url=https%3A%2F%2Fthemes.pixelwars.org%2Finterique%2Fdemo-01%2Fservices%2F&#038;format=xml" />
    <meta name="generator"
        content="Elementor 3.29.0; features: e_font_icon_svg, additional_custom_breakpoints, e_local_google_fonts, e_element_cache; settings: css_print_method-external, google_font-enabled, font_display-swap">
    <style>
        .e-con.e-parent:nth-of-type(n+4):not(.e-lazyloaded):not(.e-no-lazyload),
        .e-con.e-parent:nth-of-type(n+4):not(.e-lazyloaded):not(.e-no-lazyload) * {
            background-image: none !important;
        }

        @media screen and (max-height: 1024px) {

            .e-con.e-parent:nth-of-type(n+3):not(.e-lazyloaded):not(.e-no-lazyload),
            .e-con.e-parent:nth-of-type(n+3):not(.e-lazyloaded):not(.e-no-lazyload) * {
                background-image: none !important;
            }
        }

        @media screen and (max-height: 640px) {

            .e-con.e-parent:nth-of-type(n+2):not(.e-lazyloaded):not(.e-no-lazyload),
            .e-con.e-parent:nth-of-type(n+2):not(.e-lazyloaded):not(.e-no-lazyload) * {
                background-image: none !important;
            }
        }
    </style>
</head>

<body
    class="wp-singular page-template page-template-elementor_header_footer page page-id-17804 wp-theme-interique qodef-qi--no-touch qi-addons-for-elementor-1.8.9 elementor-default elementor-template-full-width elementor-kit-6 elementor-page elementor-page-17804">
    <div id="page" class="hfeed site">
        <header id="masthead" class="site-header" role="banner">
            <div class="header-wrap" data-parallax-video="">
                <div class="header-wrap-inner">
                    <div class="site-branding">
                        <div class="site-branding-wrap">
                            <div class="site-branding-left">
                            </div> <!-- .site-branding-left -->

                            <div class="site-branding-center">
                                <h1 class="site-title">
                                    <a href="index.html" rel="home">
                                        <span class="screen-reader-text">
                                            Motiff Square </span>
                                        <span class="site-title-text">
                                            Motiff Square </span>
                                    </a>
                                </h1> <!-- .site-title -->
                                <p class="site-description">
                                </p> <!-- .site-description -->
                            </div> <!-- .site-branding-center -->
                            <div class="site-branding-right">
                            </div> <!-- .site-branding-right -->
                        </div> <!-- .site-branding-wrap -->
                    </div> <!-- .site-branding -->

                    <nav id="site-navigation" class="main-navigation site-navigation" role="navigation">
                        <div class="menu-wrap">
                            <div class="layout-medium">
                                <a class="menu-toggle">
                                    <span class="lines"></span>
                                </a> <!-- .menu-toggle -->
                                <div class="nav-menu">
                                    <ul id="menu-mymenu" class="">
                                        <li id="menu-item-15712"
                                            class="menu-item menu-item-type-post_type menu-item-object-page menu-item-home menu-item-has-children menu-item-15712">
                                            <a href="index.html">Home</a>
                                            <!-- <ul class="sub-menu">
                                                <li id="menu-item-18584"
                                                    class="menu-item menu-item-type-post_type menu-item-object-page menu-item-home menu-item-18584">
                                                    <a href="index.html">Home
                                                        01</a></li>
                                                <li id="menu-item-18345"
                                                    class="menu-item menu-item-type-post_type menu-item-object-page menu-item-18345">
                                                    <a href="https://themes.pixelwars.org/interique/demo-01/home-02/">Home
                                                        02</a></li>
                                                <li id="menu-item-18342"
                                                    class="menu-item menu-item-type-post_type menu-item-object-page menu-item-18342">
                                                    <a href="https://themes.pixelwars.org/interique/demo-01/home-03/">Home
                                                        03</a></li>
                                                <li id="menu-item-18343"
                                                    class="menu-item menu-item-type-post_type menu-item-object-page menu-item-18343">
                                                    <a href="https://themes.pixelwars.org/interique/demo-01/home-04/">Home
                                                        04</a></li>
                                                <li id="menu-item-18344"
                                                    class="menu-item menu-item-type-post_type menu-item-object-page menu-item-18344">
                                                    <a href="https://themes.pixelwars.org/interique/demo-01/home-05/">Home
                                                        05</a></li>
                                            </ul> -->
                                        </li>
                                        <li id="menu-item-18246"
                                            class="menu-item menu-item-type-post_type menu-item-object-page menu-item-18246">
                                            <a href="about.html">About Us</a></li>
                                        <li id="menu-item-18245"
                                            class="menu-item menu-item-type-post_type menu-item-object-page current-menu-item page_item page-item-17804 current_page_item menu-item-18245">
                                            <a href="service.html"
                                                aria-current="page">Services</a></li>
                                        <li id="menu-item-15714"
                                            class="menu-item menu-item-type-post_type menu-item-object-page menu-item-15714">
                                            <a
                                                href="portfolio.html">Portfolio</a>
                                        </li>
                                        <li id="menu-item-18244"
                                            class="menu-item menu-item-type-post_type menu-item-object-page menu-item-18244">
                                            <a
                                                href="contact.html">Contact</a>
                                        </li>
                                    </ul>
                                </div> <a class="search-toggle toggle-link"></a>

                                <div class="search-container">
                                    <div class="search-box">
                                        <form class="search-form" method="get"
                                            action="index.html">
                                            <label>
                                                <span>
                                                    Search for </span>
                                                <input type="search" id="search-field" name="s"
                                                    placeholder="type and hit enter">
                                            </label>
                                            <input type="submit" class="search-submit" value="Search">
                                        </form> <!-- .search-form -->
                                    </div> <!-- .search-box -->
                                </div> <!-- .search-container -->
                                <div class="social-container widget-area">
                                    <a class="social-link instagram" target="_blank" href="#"></a>
                                    <a class="social-link twitter" target="_blank" href="#"></a>
                                </div> <!-- .social-container -->
                            </div> <!-- .layout-medium -->
                        </div> <!-- .menu-wrap -->
                    </nav> <!-- #site-navigation .main-navigation .site-navigation -->
                </div> <!-- .header-wrap-inner -->
            </div> <!-- .header-wrap -->
        </header> <!-- #masthead .site-header -->
        <div data-elementor-type="wp-page" data-elementor-id="17804" class="elementor elementor-17804">
            <section
                class="elementor-section elementor-top-section elementor-element elementor-element-3a989010 elementor-section-full_width elementor-section-stretched elementor-section-height-min-height elementor-section-items-top elementor-section-height-default"
                data-id="3a989010" data-element_type="section"
                data-settings="{&quot;stretch_section&quot;:&quot;section-stretched&quot;}">
                <div class="elementor-container elementor-column-gap-default">
                    <div class="elementor-column elementor-col-100 elementor-top-column elementor-element elementor-element-4473dc37"
                        data-id="4473dc37" data-element_type="column">
                        <div class="elementor-widget-wrap elementor-element-populated">
                            <div class="elementor-element elementor-element-1c8867c elementor-widget__width-initial elementor-invisible elementor-widget elementor-widget-prime-slider-general"
                                data-id="1c8867c" data-element_type="widget"
                                data-settings="{&quot;_animation&quot;:&quot;fadeIn&quot;}"
                                data-widget_type="prime-slider-general.default">
                                <div class="elementor-widget-container">
                                    <div class="bdt-prime-slider">
                                        <div class="bdt-prime-slider-skin-general">
                                            <div class="bdt-position-relative bdt-visible-toggle"
                                                bdt-slideshow="{&quot;animation&quot;:&quot;fade&quot;,&quot;ratio&quot;:&quot;16:5&quot;,&quot;min-height&quot;:&quot;345&quot;,&quot;autoplay&quot;:true,&quot;autoplay-interval&quot;:7000,&quot;pause-on-hover&quot;:false,&quot;draggable&quot;:true,&quot;velocity&quot;:1,&quot;finite&quot;:false}"
                                                data-settings="{&quot;id&quot;:&quot;#bdt-1c8867c&quot;,&quot;animation_status&quot;:&quot;no&quot;}"
                                                id="bdt-1c8867c">
                                                <ul class="bdt-slideshow-items">

                                                    <li
                                                        class="bdt-slideshow-item bdt-flex bdt-flex-middle bdt-flex-center elementor-repeater-item-2e35a47">
                                                        <div
                                                            class="bdt-position-cover bdt-animation-kenburns bdt-animation-reverse bdt-transform-origin-center-left">


                                                            <div class="bdt-ps-slide-img"
                                                                style="background-image: url('https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/05/freepik__a-cozy-living-room-bathed-in-warm-sunlight-featuri__82964.png')">
                                                            </div>


                                                        </div>


                                                        <div class="bdt-prime-slider-wrapper">
                                                            <div class="bdt-prime-slider-content">
                                                                <div class="bdt-prime-slider-desc">




                                                                    <div
                                                                        data-bdt-slideshow-parallax="x: 150,0,-30; opacity: 1,1,0">
                                                                        <div class="bdt-btn-wrapper">
                                                                        </div>
                                                                    </div>
                                                                </div>


                                                            </div>
                                                        </div>
                                                    </li>


                                                    <li
                                                        class="bdt-slideshow-item bdt-flex bdt-flex-middle bdt-flex-center elementor-repeater-item-36bf7a5">
                                                        <div
                                                            class="bdt-position-cover bdt-animation-kenburns bdt-animation-reverse bdt-transform-origin-center-left">


                                                            <div class="bdt-ps-slide-img"
                                                                style="background-image: url('https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/Bold-and-Graphic-Interior.jpeg')">
                                                            </div>


                                                        </div>


                                                        <div class="bdt-prime-slider-wrapper">
                                                            <div
                                                                class="bdt-prime-slider-content bdt-prime-slider-content">
                                                                <div class="bdt-prime-slider-desc">




                                                                    <div
                                                                        data-bdt-slideshow-parallax="x: 150,0,-30; opacity: 1,1,0">
                                                                        <div class="bdt-btn-wrapper">
                                                                        </div>
                                                                    </div>
                                                                </div>


                                                            </div>
                                                        </div>
                                                    </li>


                                                    <li
                                                        class="bdt-slideshow-item bdt-flex bdt-flex-middle bdt-flex-center elementor-repeater-item-ecf576a">
                                                        <div
                                                            class="bdt-position-cover bdt-animation-kenburns bdt-animation-reverse bdt-transform-origin-center-left">


                                                            <div class="bdt-ps-slide-img"
                                                                style="background-image: url('https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/Cozy-Reading-Nook.jpeg')">
                                                            </div>


                                                        </div>


                                                        <div class="bdt-prime-slider-wrapper">
                                                            <div
                                                                class="bdt-prime-slider-content bdt-prime-slider-content bdt-prime-slider-content">
                                                                <div class="bdt-prime-slider-desc">




                                                                    <div
                                                                        data-bdt-slideshow-parallax="x: 150,0,-30; opacity: 1,1,0">
                                                                        <div class="bdt-btn-wrapper">
                                                                        </div>
                                                                    </div>
                                                                </div>


                                                            </div>
                                                        </div>
                                                    </li>


                                                </ul>




                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <div class="elementor-element elementor-element-37ac375 e-flex e-con-boxed e-con e-parent" data-id="37ac375"
                data-element_type="container">
                <div class="e-con-inner">
                    <div class="elementor-element elementor-element-3fefeaf e-con-full e-flex e-con e-child"
                        data-id="3fefeaf" data-element_type="container">
                        <div class="elementor-element elementor-element-266f5c5 elementor-invisible elementor-widget elementor-widget-qi_addons_for_elementor_animated_text"
                            data-id="266f5c5" data-element_type="widget"
                            data-settings="{&quot;_animation&quot;:&quot;fadeIn&quot;}"
                            data-widget_type="qi_addons_for_elementor_animated_text.default">
                            <div class="elementor-widget-container">
                                <div
                                    class="qodef-shortcode qodef-m qodef-qi-animated-text qodef--animated-by-letter qodef-qi--has-appear qodef--appear-fade">
                                    <h1 class="qodef-m-title">
                                        <span class="qodef-e-word-holder">Tailored</span> <span
                                            class="qodef-e-word-holder">Interior</span> <span
                                            class="qodef-e-word-holder">Solutions</span>
                                    </h1>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="elementor-element elementor-element-d569fd9 e-con-full e-flex e-con e-child"
                        data-id="d569fd9" data-element_type="container">
                        <div class="elementor-element elementor-element-72eff90 elementor-widget elementor-widget-qi_addons_for_elementor_animated_text"
                            data-id="72eff90" data-element_type="widget"
                            data-widget_type="qi_addons_for_elementor_animated_text.default">
                            <div class="elementor-widget-container">
                                <div
                                    class="qodef-shortcode qodef-m qodef-qi-animated-text qodef--animated-by-letter qodef-qi--has-appear qodef--appear-fade">
                                    <h3 class="qodef-m-title">
                                        <span class="qodef-e-word-holder">Our</span> <span
                                            class="qodef-e-word-holder">Services</span>
                                    </h3>
                                </div>
                            </div>
                        </div>
                        <div class="elementor-element elementor-element-616b5f8 animated-slow elementor-invisible elementor-widget elementor-widget-heading"
                            data-id="616b5f8" data-element_type="widget"
                            data-settings="{&quot;_animation&quot;:&quot;fadeIn&quot;}"
                            data-widget_type="heading.default">
                            <div class="elementor-widget-container">
                                <p class="elementor-heading-title elementor-size-default">Whether you're dreaming of a
                                    cozy home retreat or a bold, modern workspace, our expert designers bring your
                                    vision to life.</p>
                            </div>
                        </div>
                    </div>
                    <div class="elementor-element elementor-element-6076ec3 e-con-full e-flex e-con e-child"
                        data-id="6076ec3" data-element_type="container">
                        <div class="elementor-element elementor-element-930ca9d e-con-full e-flex e-con e-child"
                            data-id="930ca9d" data-element_type="container">
                            <div class="elementor-element elementor-element-92da80c e-con-full e-flex e-con e-child"
                                data-id="92da80c" data-element_type="container"
                                data-settings="{&quot;background_background&quot;:&quot;video&quot;,&quot;background_video_link&quot;:&quot;https:\/\/themes.pixelwars.org\/interique\/demo-01\/wp-content\/uploads\/sites\/2\/2025\/05\/freepik__dolly-shot-a-stylish-living-room-featuring-a-cozy-__17637.mp4&quot;,&quot;background_play_once&quot;:&quot;yes&quot;}">
                                <div class="elementor-background-video-container elementor-hidden-mobile">
                                    <video class="elementor-background-video-hosted" autoplay muted playsinline></video>
                                </div>
                            </div>
                            <div class="elementor-element elementor-element-1561f22 elementor-widget elementor-widget-heading"
                                data-id="1561f22" data-element_type="widget" data-widget_type="heading.default">
                                <div class="elementor-widget-container">
                                    <h2 class="elementor-heading-title elementor-size-default">From vision to reality,
                                        Nikita Powle creates timeless interiors with thoughtful design, quality
                                        craftsmanship, and a passion for spaces that speak to your soul.</h2>
                                </div>
                            </div>
                            <div class="elementor-element elementor-element-c2e6a48 animated-slow elementor-invisible elementor-widget elementor-widget-heading"
                                data-id="c2e6a48" data-element_type="widget"
                                data-settings="{&quot;_animation&quot;:&quot;fadeIn&quot;}"
                                data-widget_type="heading.default">
                                <div class="elementor-widget-container">
                                    <p class="elementor-heading-title elementor-size-default">Welcome to Nikita Powle
                                        Interior Design—where creativity meets comfort. We craft inspiring, functional
                                        spaces tailored to your lifestyle, turning your vision into beautifully designed
                                        reality with care and passion.</p>
                                </div>
                            </div>
                        </div>
                        <div class="elementor-element elementor-element-095cb3b e-con-full e-flex e-con e-child"
                            data-id="095cb3b" data-element_type="container">
                            <div class="elementor-element elementor-element-229505d elementor-widget elementor-widget-heading"
                                data-id="229505d" data-element_type="widget" data-widget_type="heading.default">
                                <div class="elementor-widget-container">
                                    <h2 class="elementor-heading-title elementor-size-default">Residential Interior
                                        Design</h2>
                                </div>
                            </div>
                            <div class="elementor-element elementor-element-2f309a0 animated-slow elementor-invisible elementor-widget elementor-widget-heading"
                                data-id="2f309a0" data-element_type="widget"
                                data-settings="{&quot;_animation&quot;:&quot;fadeIn&quot;}"
                                data-widget_type="heading.default">
                                <div class="elementor-widget-container">
                                    <p class="elementor-heading-title elementor-size-default">Custom home interiors that
                                        reflect personality, lifestyle, and functionality with elegance.</p>
                                </div>
                            </div>
                            <div class="elementor-element elementor-element-21c2045 elementor-widget elementor-widget-spacer"
                                data-id="21c2045" data-element_type="widget" data-widget_type="spacer.default">
                                <div class="elementor-widget-container">
                                    <div class="elementor-spacer">
                                        <div class="elementor-spacer-inner"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="elementor-element elementor-element-b353f57 elementor-widget elementor-widget-heading"
                                data-id="b353f57" data-element_type="widget" data-widget_type="heading.default">
                                <div class="elementor-widget-container">
                                    <h2 class="elementor-heading-title elementor-size-default">Commercial Interior
                                        Design</h2>
                                </div>
                            </div>
                            <div class="elementor-element elementor-element-96d0e60 animated-slow elementor-invisible elementor-widget elementor-widget-heading"
                                data-id="96d0e60" data-element_type="widget"
                                data-settings="{&quot;_animation&quot;:&quot;fadeIn&quot;}"
                                data-widget_type="heading.default">
                                <div class="elementor-widget-container">
                                    <p class="elementor-heading-title elementor-size-default">Creative, branded spaces
                                        for offices, retail stores, cafes, and professional environments.</p>
                                </div>
                            </div>
                            <div class="elementor-element elementor-element-7a418ab elementor-widget elementor-widget-spacer"
                                data-id="7a418ab" data-element_type="widget" data-widget_type="spacer.default">
                                <div class="elementor-widget-container">
                                    <div class="elementor-spacer">
                                        <div class="elementor-spacer-inner"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="elementor-element elementor-element-a708069 elementor-widget elementor-widget-heading"
                                data-id="a708069" data-element_type="widget" data-widget_type="heading.default">
                                <div class="elementor-widget-container">
                                    <h2 class="elementor-heading-title elementor-size-default">3D Rendering &
                                        Visualization</h2>
                                </div>
                            </div>
                            <div class="elementor-element elementor-element-f95bdc4 animated-slow elementor-invisible elementor-widget elementor-widget-heading"
                                data-id="f95bdc4" data-element_type="widget"
                                data-settings="{&quot;_animation&quot;:&quot;fadeIn&quot;}"
                                data-widget_type="heading.default">
                                <div class="elementor-widget-container">
                                    <p class="elementor-heading-title elementor-size-default">Realistic digital previews
                                        to help clients envision final designs before implementation.</p>
                                </div>
                            </div>
                            <div class="elementor-element elementor-element-24021ef elementor-widget elementor-widget-spacer"
                                data-id="24021ef" data-element_type="widget" data-widget_type="spacer.default">
                                <div class="elementor-widget-container">
                                    <div class="elementor-spacer">
                                        <div class="elementor-spacer-inner"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="elementor-element elementor-element-fc43fc4 elementor-widget elementor-widget-heading"
                                data-id="fc43fc4" data-element_type="widget" data-widget_type="heading.default">
                                <div class="elementor-widget-container">
                                    <h2 class="elementor-heading-title elementor-size-default">Color & Material
                                        Consultation</h2>
                                </div>
                            </div>
                            <div class="elementor-element elementor-element-d367201 animated-slow elementor-invisible elementor-widget elementor-widget-heading"
                                data-id="d367201" data-element_type="widget"
                                data-settings="{&quot;_animation&quot;:&quot;fadeIn&quot;}"
                                data-widget_type="heading.default">
                                <div class="elementor-widget-container">
                                    <p class="elementor-heading-title elementor-size-default">Expert guidance on
                                        palettes, textures, and finishes for harmonious, stylish interiors.</p>
                                </div>
                            </div>
                            <div class="elementor-element elementor-element-fdaf6da elementor-widget elementor-widget-spacer"
                                data-id="fdaf6da" data-element_type="widget" data-widget_type="spacer.default">
                                <div class="elementor-widget-container">
                                    <div class="elementor-spacer">
                                        <div class="elementor-spacer-inner"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="elementor-element elementor-element-1d4d92c7 e-flex e-con-boxed e-con e-parent"
                data-id="1d4d92c7" data-element_type="container">
                <div class="e-con-inner">
                    <div class="elementor-element elementor-element-7e1e012e e-con-full e-flex e-con e-child"
                        data-id="7e1e012e" data-element_type="container">
                        <div class="elementor-element elementor-element-1de9defe animated-slow elementor-invisible elementor-widget elementor-widget-qi_addons_for_elementor_parallax_images"
                            data-id="1de9defe" data-element_type="widget"
                            data-settings="{&quot;_animation&quot;:&quot;fadeInUp&quot;}"
                            data-widget_type="qi_addons_for_elementor_parallax_images.default">
                            <div class="elementor-widget-container">
                                <div class="qodef-shortcode qodef-m qodef-qi-parallax-images qodef-layout--default">
                                    <div class="qodef-m-images">
                                        <div class="qodef-e-main-image-holder">
                                            <div class="qodef-e-main-image-zoom-holder">
                                                <div class="qodef-e-main-image" data-parallax-main="100">
                                                    <img fetchpriority="high" decoding="async" width="1300"
                                                        height="1200"
                                                        src="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/Minimalist-Interior-Scene-1300x1200.jpeg"
                                                        class="attachment-qi_addons_for_elementor_image_size_huge-square size-qi_addons_for_elementor_image_size_huge-square"
                                                        alt="" />
                                                </div>
                                            </div>
                                        </div>
                                        <div
                                            class="qodef-e-parallax-image qodef-position--bottom-left elementor-repeater-item-93b473e">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="elementor-element elementor-element-1c76006b e-con-full e-flex e-con e-child"
                        data-id="1c76006b" data-element_type="container">
                        <div class="elementor-element elementor-element-3a647b80 animated-slow elementor-invisible elementor-widget elementor-widget-qi_addons_for_elementor_accordion"
                            data-id="3a647b80" data-element_type="widget"
                            data-settings="{&quot;_animation&quot;:&quot;fadeInUp&quot;}"
                            data-widget_type="qi_addons_for_elementor_accordion.default">
                            <div class="elementor-widget-container">
                                <div
                                    class="qodef-shortcode qodef-m qodef-qi-accordion qodef-qi-clear qodef-behavior--accordion qodef-layout--simple qodef-style--border-between qodef-height--auto">
                                    <h3 class="qodef-e-title-holder">
                                        <span class="qodef-e-title">Residential Interior Design</span>
                                        <span class="qodef-e-mark">
                                            <span class="qodef-icon--plus">
                                                <span class="qodef-e-icon">
                                                    <svg xmlns="http://www.w3.org/2000/svg" id="Capa_1" height="512"
                                                        viewBox="0 0 207.2 207.2" width="512">
                                                        <g>
                                                            <g id="_3">
                                                                <path
                                                                    d="m103.6 207.2c-4.087 0-7.4-3.313-7.4-7.4v-192.4c0-4.087 3.313-7.4 7.4-7.4s7.4 3.313 7.4 7.4v192.4c0 4.087-3.313 7.4-7.4 7.4z">
                                                                </path>
                                                                <path
                                                                    d="m199.8 111h-192.4c-4.087 0-7.4-3.313-7.4-7.4s3.313-7.4 7.4-7.4h192.4c4.087 0 7.4 3.313 7.4 7.4s-3.313 7.4-7.4 7.4z">
                                                                </path>
                                                            </g>
                                                        </g>
                                                    </svg> </span>
                                            </span>
                                            <span class="qodef-icon--minus">
                                                <span class="qodef-e-icon">
                                                    <svg xmlns="http://www.w3.org/2000/svg" id="Capa_1" height="512"
                                                        viewBox="0 0 512 512" width="512">
                                                        <g>
                                                            <path
                                                                d="m480 288h-448c-17.673 0-32-14.327-32-32s14.327-32 32-32h448c17.673 0 32 14.327 32 32s-14.327 32-32 32z">
                                                            </path>
                                                        </g>
                                                    </svg> </span>
                                            </span>
                                        </span>
                                    </h3>
                                    <div class="qodef-e-content">
                                        <div class="qodef-e-content-inner">
                                            <p>We design elegant, functional homes tailored to your personality and
                                                lifestyle. From concept to finishing touches, we ensure every space
                                                reflects comfort, beauty, and purpose, enhancing daily living through
                                                thoughtful layouts, materials, and decor that blend seamlessly.</p>
                                        </div>
                                    </div>
                                    <h3 class="qodef-e-title-holder">
                                        <span class="qodef-e-title">Commercial Interior Design</span>
                                        <span class="qodef-e-mark">
                                            <span class="qodef-icon--plus">
                                                <span class="qodef-e-icon">
                                                    <svg xmlns="http://www.w3.org/2000/svg" id="Capa_1" height="512"
                                                        viewBox="0 0 207.2 207.2" width="512">
                                                        <g>
                                                            <g id="_3">
                                                                <path
                                                                    d="m103.6 207.2c-4.087 0-7.4-3.313-7.4-7.4v-192.4c0-4.087 3.313-7.4 7.4-7.4s7.4 3.313 7.4 7.4v192.4c0 4.087-3.313 7.4-7.4 7.4z">
                                                                </path>
                                                                <path
                                                                    d="m199.8 111h-192.4c-4.087 0-7.4-3.313-7.4-7.4s3.313-7.4 7.4-7.4h192.4c4.087 0 7.4 3.313 7.4 7.4s-3.313 7.4-7.4 7.4z">
                                                                </path>
                                                            </g>
                                                        </g>
                                                    </svg> </span>
                                            </span>
                                            <span class="qodef-icon--minus">
                                                <span class="qodef-e-icon">
                                                    <svg xmlns="http://www.w3.org/2000/svg" id="Capa_1" height="512"
                                                        viewBox="0 0 512 512" width="512">
                                                        <g>
                                                            <path
                                                                d="m480 288h-448c-17.673 0-32-14.327-32-32s14.327-32 32-32h448c17.673 0 32 14.327 32 32s-14.327 32-32 32z">
                                                            </path>
                                                        </g>
                                                    </svg> </span>
                                            </span>
                                        </span>
                                    </h3>
                                    <div class="qodef-e-content">
                                        <div class="qodef-e-content-inner">
                                            <p>We create impactful office, retail, and hospitality interiors that
                                                reflect your brand identity and improve user experience. Our designs
                                                prioritize functionality, aesthetics, and workflow, resulting in
                                                engaging environments that boost productivity, customer interaction, and
                                                visual appeal.</p>
                                        </div>
                                    </div>
                                    <h3 class="qodef-e-title-holder">
                                        <span class="qodef-e-title">Space Planning &amp; Layout</span>
                                        <span class="qodef-e-mark">
                                            <span class="qodef-icon--plus">
                                                <span class="qodef-e-icon">
                                                    <svg xmlns="http://www.w3.org/2000/svg" id="Capa_1" height="512"
                                                        viewBox="0 0 207.2 207.2" width="512">
                                                        <g>
                                                            <g id="_3">
                                                                <path
                                                                    d="m103.6 207.2c-4.087 0-7.4-3.313-7.4-7.4v-192.4c0-4.087 3.313-7.4 7.4-7.4s7.4 3.313 7.4 7.4v192.4c0 4.087-3.313 7.4-7.4 7.4z">
                                                                </path>
                                                                <path
                                                                    d="m199.8 111h-192.4c-4.087 0-7.4-3.313-7.4-7.4s3.313-7.4 7.4-7.4h192.4c4.087 0 7.4 3.313 7.4 7.4s-3.313 7.4-7.4 7.4z">
                                                                </path>
                                                            </g>
                                                        </g>
                                                    </svg> </span>
                                            </span>
                                            <span class="qodef-icon--minus">
                                                <span class="qodef-e-icon">
                                                    <svg xmlns="http://www.w3.org/2000/svg" id="Capa_1" height="512"
                                                        viewBox="0 0 512 512" width="512">
                                                        <g>
                                                            <path
                                                                d="m480 288h-448c-17.673 0-32-14.327-32-32s14.327-32 32-32h448c17.673 0 32 14.327 32 32s-14.327 32-32 32z">
                                                            </path>
                                                        </g>
                                                    </svg> </span>
                                            </span>
                                        </span>
                                    </h3>
                                    <div class="qodef-e-content">
                                        <div class="qodef-e-content-inner">
                                            <p>Our expert space planning optimizes flow, usability, and spatial harmony.
                                                We analyze dimensions, lighting, and usage needs to develop efficient,
                                                balanced layouts—making every square foot of your space work smarter and
                                                feel better.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="elementor-element elementor-element-32c20156 e-flex e-con-boxed e-con e-parent"
                data-id="32c20156" data-element_type="container">
                <div class="e-con-inner">
                    <div class="elementor-element elementor-element-31b519f8 e-con-full e-flex e-con e-child"
                        data-id="31b519f8" data-element_type="container">
                        <div class="elementor-element elementor-element-6801daa4 elementor-invisible elementor-widget elementor-widget-qi_addons_for_elementor_accordion"
                            data-id="6801daa4" data-element_type="widget"
                            data-settings="{&quot;_animation&quot;:&quot;fadeInUp&quot;}"
                            data-widget_type="qi_addons_for_elementor_accordion.default">
                            <div class="elementor-widget-container">
                                <div
                                    class="qodef-shortcode qodef-m qodef-qi-accordion qodef-qi-clear qodef-behavior--accordion qodef-layout--simple qodef-style--border-between qodef-height--auto">
                                    <h3 class="qodef-e-title-holder">
                                        <span class="qodef-e-title">Furniture &amp; Decor Selection</span>
                                        <span class="qodef-e-mark">
                                            <span class="qodef-icon--plus">
                                                <span class="qodef-e-icon">
                                                    <svg xmlns="http://www.w3.org/2000/svg" id="Capa_1" height="512"
                                                        viewBox="0 0 207.2 207.2" width="512">
                                                        <g>
                                                            <g id="_3">
                                                                <path
                                                                    d="m103.6 207.2c-4.087 0-7.4-3.313-7.4-7.4v-192.4c0-4.087 3.313-7.4 7.4-7.4s7.4 3.313 7.4 7.4v192.4c0 4.087-3.313 7.4-7.4 7.4z">
                                                                </path>
                                                                <path
                                                                    d="m199.8 111h-192.4c-4.087 0-7.4-3.313-7.4-7.4s3.313-7.4 7.4-7.4h192.4c4.087 0 7.4 3.313 7.4 7.4s-3.313 7.4-7.4 7.4z">
                                                                </path>
                                                            </g>
                                                        </g>
                                                    </svg> </span>
                                            </span>
                                            <span class="qodef-icon--minus">
                                                <span class="qodef-e-icon">
                                                    <svg xmlns="http://www.w3.org/2000/svg" id="Capa_1" height="512"
                                                        viewBox="0 0 512 512" width="512">
                                                        <g>
                                                            <path
                                                                d="m480 288h-448c-17.673 0-32-14.327-32-32s14.327-32 32-32h448c17.673 0 32 14.327 32 32s-14.327 32-32 32z">
                                                            </path>
                                                        </g>
                                                    </svg> </span>
                                            </span>
                                        </span>
                                    </h3>
                                    <div class="qodef-e-content">
                                        <div class="qodef-e-content-inner">
                                            <p>We curate high-quality furniture, artwork, and accessories that align
                                                with your design theme and lifestyle. Our selections bring cohesion,
                                                style, and character, enhancing each room’s personality while
                                                maintaining comfort and functionality.</p>
                                        </div>
                                    </div>
                                    <h3 class="qodef-e-title-holder">
                                        <span class="qodef-e-title">Custom Furniture Design</span>
                                        <span class="qodef-e-mark">
                                            <span class="qodef-icon--plus">
                                                <span class="qodef-e-icon">
                                                    <svg xmlns="http://www.w3.org/2000/svg" id="Capa_1" height="512"
                                                        viewBox="0 0 207.2 207.2" width="512">
                                                        <g>
                                                            <g id="_3">
                                                                <path
                                                                    d="m103.6 207.2c-4.087 0-7.4-3.313-7.4-7.4v-192.4c0-4.087 3.313-7.4 7.4-7.4s7.4 3.313 7.4 7.4v192.4c0 4.087-3.313 7.4-7.4 7.4z">
                                                                </path>
                                                                <path
                                                                    d="m199.8 111h-192.4c-4.087 0-7.4-3.313-7.4-7.4s3.313-7.4 7.4-7.4h192.4c4.087 0 7.4 3.313 7.4 7.4s-3.313 7.4-7.4 7.4z">
                                                                </path>
                                                            </g>
                                                        </g>
                                                    </svg> </span>
                                            </span>
                                            <span class="qodef-icon--minus">
                                                <span class="qodef-e-icon">
                                                    <svg xmlns="http://www.w3.org/2000/svg" id="Capa_1" height="512"
                                                        viewBox="0 0 512 512" width="512">
                                                        <g>
                                                            <path
                                                                d="m480 288h-448c-17.673 0-32-14.327-32-32s14.327-32 32-32h448c17.673 0 32 14.327 32 32s-14.327 32-32 32z">
                                                            </path>
                                                        </g>
                                                    </svg> </span>
                                            </span>
                                        </span>
                                    </h3>
                                    <div class="qodef-e-content">
                                        <div class="qodef-e-content-inner">
                                            <p>We design and build bespoke furniture pieces tailored to your space and
                                                taste. These one-of-a-kind creations ensure your interiors are both
                                                original and perfectly fitted, combining craftsmanship with artistic
                                                detail.</p>
                                        </div>
                                    </div>
                                    <h3 class="qodef-e-title-holder">
                                        <span class="qodef-e-title">Lighting Design</span>
                                        <span class="qodef-e-mark">
                                            <span class="qodef-icon--plus">
                                                <span class="qodef-e-icon">
                                                    <svg xmlns="http://www.w3.org/2000/svg" id="Capa_1" height="512"
                                                        viewBox="0 0 207.2 207.2" width="512">
                                                        <g>
                                                            <g id="_3">
                                                                <path
                                                                    d="m103.6 207.2c-4.087 0-7.4-3.313-7.4-7.4v-192.4c0-4.087 3.313-7.4 7.4-7.4s7.4 3.313 7.4 7.4v192.4c0 4.087-3.313 7.4-7.4 7.4z">
                                                                </path>
                                                                <path
                                                                    d="m199.8 111h-192.4c-4.087 0-7.4-3.313-7.4-7.4s3.313-7.4 7.4-7.4h192.4c4.087 0 7.4 3.313 7.4 7.4s-3.313 7.4-7.4 7.4z">
                                                                </path>
                                                            </g>
                                                        </g>
                                                    </svg> </span>
                                            </span>
                                            <span class="qodef-icon--minus">
                                                <span class="qodef-e-icon">
                                                    <svg xmlns="http://www.w3.org/2000/svg" id="Capa_1" height="512"
                                                        viewBox="0 0 512 512" width="512">
                                                        <g>
                                                            <path
                                                                d="m480 288h-448c-17.673 0-32-14.327-32-32s14.327-32 32-32h448c17.673 0 32 14.327 32 32s-14.327 32-32 32z">
                                                            </path>
                                                        </g>
                                                    </svg> </span>
                                            </span>
                                        </span>
                                    </h3>
                                    <div class="qodef-e-content">
                                        <div class="qodef-e-content-inner">
                                            <p>We craft layered lighting plans that balance natural and artificial
                                                light. Our designs highlight features, set moods, and improve
                                                functionality—using placement, style, and technology to bring warmth,
                                                drama, and efficiency.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="elementor-element elementor-element-643c91da e-con-full e-flex e-con e-child"
                        data-id="643c91da" data-element_type="container">
                        <div class="elementor-element elementor-element-2780c306 animated-slow elementor-invisible elementor-widget elementor-widget-qi_addons_for_elementor_parallax_images"
                            data-id="2780c306" data-element_type="widget"
                            data-settings="{&quot;_animation&quot;:&quot;fadeInUp&quot;}"
                            data-widget_type="qi_addons_for_elementor_parallax_images.default">
                            <div class="elementor-widget-container">
                                <div class="qodef-shortcode qodef-m qodef-qi-parallax-images qodef-layout--default">
                                    <div class="qodef-m-images">
                                        <div class="qodef-e-main-image-holder">
                                            <div class="qodef-e-main-image-zoom-holder">
                                                <div class="qodef-e-main-image" data-parallax-main="250">
                                                    <img decoding="async" width="1300" height="1200"
                                                        src="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/Minimalist-Interior-Scene-1-1300x1200.jpeg"
                                                        class="attachment-qi_addons_for_elementor_image_size_huge-square size-qi_addons_for_elementor_image_size_huge-square"
                                                        alt="" />
                                                </div>
                                            </div>
                                        </div>
                                        <div
                                            class="qodef-e-parallax-image qodef-position--bottom-left elementor-repeater-item-93b473e">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="elementor-element elementor-element-6bf0704 e-flex e-con-boxed e-con e-parent" data-id="6bf0704"
                data-element_type="container" data-settings="{&quot;background_background&quot;:&quot;classic&quot;}">
                <div class="e-con-inner">
                    <div class="elementor-element elementor-element-db59506 e-con-full e-flex e-con e-child"
                        data-id="db59506" data-element_type="container">
                        <div class="elementor-element elementor-element-a6c0642 elementor-invisible elementor-widget elementor-widget-qi_addons_for_elementor_animated_text"
                            data-id="a6c0642" data-element_type="widget"
                            data-settings="{&quot;_animation&quot;:&quot;fadeIn&quot;}"
                            data-widget_type="qi_addons_for_elementor_animated_text.default">
                            <div class="elementor-widget-container">
                                <div
                                    class="qodef-shortcode qodef-m qodef-qi-animated-text qodef--animated-by-letter qodef-qi--has-appear qodef--appear-fade">
                                    <h1 class="qodef-m-title">
                                        <span class="qodef-e-word-holder">Meet</span> <span
                                            class="qodef-e-word-holder">Our</span> <span
                                            class="qodef-e-word-holder">Team</span>
                                    </h1>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="elementor-element elementor-element-45921e7 e-con-full e-flex e-con e-child"
                        data-id="45921e7" data-element_type="container">
                        <div class="elementor-element elementor-element-38caf9b elementor-widget elementor-widget-qi_addons_for_elementor_animated_text"
                            data-id="38caf9b" data-element_type="widget"
                            data-widget_type="qi_addons_for_elementor_animated_text.default">
                            <div class="elementor-widget-container">
                                <div
                                    class="qodef-shortcode qodef-m qodef-qi-animated-text qodef--animated-by-letter qodef-qi--has-appear qodef--appear-fade">
                                    <h3 class="qodef-m-title">
                                        <span class="qodef-e-word-holder">Team</span>
                                    </h3>
                                </div>
                            </div>
                        </div>
                        <div class="elementor-element elementor-element-babb398 animated-slow elementor-invisible elementor-widget elementor-widget-heading"
                            data-id="babb398" data-element_type="widget"
                            data-settings="{&quot;_animation&quot;:&quot;fadeIn&quot;}"
                            data-widget_type="heading.default">
                            <div class="elementor-widget-container">
                                <p class="elementor-heading-title elementor-size-default">Meet the creative minds behind
                                    our designs—passionate experts dedicated to transforming your space beautifully.</p>
                            </div>
                        </div>
                    </div>
                    <div class="elementor-element elementor-element-025b3f0 e-con-full e-flex e-con e-child"
                        data-id="025b3f0" data-element_type="container">
                        <div class="elementor-element elementor-element-b0699c1 e-con-full e-flex e-con e-child"
                            data-id="b0699c1" data-element_type="container">
                            <div class="elementor-element elementor-element-8578786 animated-slow elementor-invisible elementor-widget elementor-widget-qi_addons_for_elementor_team_member"
                                data-id="8578786" data-element_type="widget"
                                data-settings="{&quot;_animation&quot;:&quot;fadeIn&quot;,&quot;_animation_delay&quot;:&quot;200&quot;}"
                                data-widget_type="qi_addons_for_elementor_team_member.default">
                                <div class="elementor-widget-container">
                                    <div
                                        class="qodef-shortcode qodef-m qodef-qi-team-member qodef-item-layout--info-from-bottom qodef-image--hover-zoom">
                                        <div class="qodef-m-inner">
                                            <div class="qodef-m-image">
                                                <div class="qodef-m-media-image">
                                                    <img decoding="async" width="800" height="900"
                                                        src="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-08.jpg"
                                                        class="attachment-full size-full" alt=""
                                                        srcset="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-08.jpg 800w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-08-267x300.jpg 267w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-08-768x864.jpg 768w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-08-489x550.jpg 489w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-08-550x619.jpg 550w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-08-444x500.jpg 444w"
                                                        sizes="(max-width: 800px) 100vw, 800px" />
                                                </div>
                                            </div>
                                            <div class="qodef-m-content">
                                                <h4 itemprop="name" class="qodef-m-title">
                                                    Sophia Bennett </h4>
                                                <p class="qodef-m-role">Creative Director</p>
                                                <div class="qodef-m-social-icons">
                                                    <a class="qodef-e-social-icon-link" itemprop="url" href="#"
                                                        target="_blank">
                                                        <span class="qodef-e-social-icon">
                                                            <svg aria-hidden="true"
                                                                class="e-font-icon-svg e-fab-twitter"
                                                                viewBox="0 0 512 512"
                                                                xmlns="http://www.w3.org/2000/svg">
                                                                <path
                                                                    d="M459.37 151.716c.325 4.548.325 9.097.325 13.645 0 138.72-105.583 298.558-298.558 298.558-59.452 0-114.68-17.219-161.137-47.106 8.447.974 16.568 1.299 25.34 1.299 49.055 0 94.213-16.568 130.274-44.832-46.132-.975-84.792-31.188-98.112-72.772 6.498.974 12.995 1.624 19.818 1.624 9.421 0 18.843-1.3 27.614-3.573-48.081-9.747-84.143-51.98-84.143-102.985v-1.299c13.969 7.797 30.214 12.67 47.431 13.319-28.264-18.843-46.781-51.005-46.781-87.391 0-19.492 5.197-37.36 14.294-52.954 51.655 63.675 129.3 105.258 216.365 109.807-1.624-7.797-2.599-15.918-2.599-24.04 0-57.828 46.782-104.934 104.934-104.934 30.213 0 57.502 12.67 76.67 33.137 23.715-4.548 46.456-13.32 66.599-25.34-7.798 24.366-24.366 44.833-46.132 57.827 21.117-2.273 41.584-8.122 60.426-16.243-14.292 20.791-32.161 39.308-52.628 54.253z">
                                                                </path>
                                                            </svg> </span>
                                                    </a>
                                                    <a class="qodef-e-social-icon-link" itemprop="url" href="#"
                                                        target="_blank">
                                                        <span class="qodef-e-social-icon">
                                                            <svg aria-hidden="true"
                                                                class="e-font-icon-svg e-fab-instagram"
                                                                viewBox="0 0 448 512"
                                                                xmlns="http://www.w3.org/2000/svg">
                                                                <path
                                                                    d="M224.1 141c-63.6 0-114.9 51.3-114.9 114.9s51.3 114.9 114.9 114.9S339 319.5 339 255.9 287.7 141 224.1 141zm0 189.6c-41.1 0-74.7-33.5-74.7-74.7s33.5-74.7 74.7-74.7 74.7 33.5 74.7 74.7-33.6 74.7-74.7 74.7zm146.4-194.3c0 14.9-12 26.8-26.8 26.8-14.9 0-26.8-12-26.8-26.8s12-26.8 26.8-26.8 26.8 12 26.8 26.8zm76.1 27.2c-1.7-35.9-9.9-67.7-36.2-93.9-26.2-26.2-58-34.4-93.9-36.2-37-2.1-147.9-2.1-184.9 0-35.8 1.7-67.6 9.9-93.9 36.1s-34.4 58-36.2 93.9c-2.1 37-2.1 147.9 0 184.9 1.7 35.9 9.9 67.7 36.2 93.9s58 34.4 93.9 36.2c37 2.1 147.9 2.1 184.9 0 35.9-1.7 67.7-9.9 93.9-36.2 26.2-26.2 34.4-58 36.2-93.9 2.1-37 2.1-147.8 0-184.8zM398.8 388c-7.8 19.6-22.9 34.7-42.6 42.6-29.5 11.7-99.5 9-132.1 9s-102.7 2.6-132.1-9c-19.6-7.8-34.7-22.9-42.6-42.6-11.7-29.5-9-99.5-9-132.1s-2.6-102.7 9-132.1c7.8-19.6 22.9-34.7 42.6-42.6 29.5-11.7 99.5-9 132.1-9s102.7-2.6 132.1 9c19.6 7.8 34.7 22.9 42.6 42.6 11.7 29.5 9 99.5 9 132.1s2.7 102.7-9 132.1z">
                                                                </path>
                                                            </svg> </span>
                                                    </a>
                                                    <a class="qodef-e-social-icon-link" itemprop="url" href="#"
                                                        target="_blank">
                                                        <span class="qodef-e-social-icon">
                                                            <svg aria-hidden="true"
                                                                class="e-font-icon-svg e-far-envelope"
                                                                viewBox="0 0 512 512"
                                                                xmlns="http://www.w3.org/2000/svg">
                                                                <path
                                                                    d="M464 64H48C21.49 64 0 85.49 0 112v288c0 26.51 21.49 48 48 48h416c26.51 0 48-21.49 48-48V112c0-26.51-21.49-48-48-48zm0 48v40.805c-22.422 18.259-58.168 46.651-134.587 106.49-16.841 13.247-50.201 45.072-73.413 44.701-23.208.375-56.579-31.459-73.413-44.701C106.18 199.465 70.425 171.067 48 152.805V112h416zM48 400V214.398c22.914 18.251 55.409 43.862 104.938 82.646 21.857 17.205 60.134 55.186 103.062 54.955 42.717.231 80.509-37.199 103.053-54.947 49.528-38.783 82.032-64.401 104.947-82.653V400H48z">
                                                                </path>
                                                            </svg> </span>
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="elementor-element elementor-element-4e53c0f e-con-full e-flex e-con e-child"
                            data-id="4e53c0f" data-element_type="container">
                            <div class="elementor-element elementor-element-b31d5ef animated-slow elementor-invisible elementor-widget elementor-widget-qi_addons_for_elementor_team_member"
                                data-id="b31d5ef" data-element_type="widget"
                                data-settings="{&quot;_animation&quot;:&quot;fadeIn&quot;,&quot;_animation_delay&quot;:&quot;200&quot;}"
                                data-widget_type="qi_addons_for_elementor_team_member.default">
                                <div class="elementor-widget-container">
                                    <div
                                        class="qodef-shortcode qodef-m qodef-qi-team-member qodef-item-layout--info-from-bottom qodef-image--hover-zoom">
                                        <div class="qodef-m-inner">
                                            <div class="qodef-m-image">
                                                <div class="qodef-m-media-image">
                                                    <img loading="lazy" decoding="async" width="800" height="900"
                                                        src="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-02.jpg"
                                                        class="attachment-full size-full" alt=""
                                                        srcset="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-02.jpg 800w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-02-267x300.jpg 267w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-02-768x864.jpg 768w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-02-489x550.jpg 489w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-02-550x619.jpg 550w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-02-444x500.jpg 444w"
                                                        sizes="(max-width: 800px) 100vw, 800px" />
                                                </div>
                                            </div>
                                            <div class="qodef-m-content">
                                                <h4 itemprop="name" class="qodef-m-title">
                                                    James Taylor </h4>
                                                <p class="qodef-m-role">Design Consultant</p>
                                                <div class="qodef-m-social-icons">
                                                    <a class="qodef-e-social-icon-link" itemprop="url" href="#"
                                                        target="_blank">
                                                        <span class="qodef-e-social-icon">
                                                            <svg aria-hidden="true"
                                                                class="e-font-icon-svg e-fab-facebook"
                                                                viewBox="0 0 512 512"
                                                                xmlns="http://www.w3.org/2000/svg">
                                                                <path
                                                                    d="M504 256C504 119 393 8 256 8S8 119 8 256c0 123.78 90.69 226.38 209.25 245V327.69h-63V256h63v-54.64c0-62.15 37-96.48 93.67-96.48 27.14 0 55.52 4.84 55.52 4.84v61h-31.28c-30.8 0-40.41 19.12-40.41 38.73V256h68.78l-11 71.69h-57.78V501C413.31 482.38 504 379.78 504 256z">
                                                                </path>
                                                            </svg> </span>
                                                    </a>
                                                    <a class="qodef-e-social-icon-link" itemprop="url" href="#"
                                                        target="_blank">
                                                        <span class="qodef-e-social-icon">
                                                            <svg aria-hidden="true"
                                                                class="e-font-icon-svg e-fab-twitter"
                                                                viewBox="0 0 512 512"
                                                                xmlns="http://www.w3.org/2000/svg">
                                                                <path
                                                                    d="M459.37 151.716c.325 4.548.325 9.097.325 13.645 0 138.72-105.583 298.558-298.558 298.558-59.452 0-114.68-17.219-161.137-47.106 8.447.974 16.568 1.299 25.34 1.299 49.055 0 94.213-16.568 130.274-44.832-46.132-.975-84.792-31.188-98.112-72.772 6.498.974 12.995 1.624 19.818 1.624 9.421 0 18.843-1.3 27.614-3.573-48.081-9.747-84.143-51.98-84.143-102.985v-1.299c13.969 7.797 30.214 12.67 47.431 13.319-28.264-18.843-46.781-51.005-46.781-87.391 0-19.492 5.197-37.36 14.294-52.954 51.655 63.675 129.3 105.258 216.365 109.807-1.624-7.797-2.599-15.918-2.599-24.04 0-57.828 46.782-104.934 104.934-104.934 30.213 0 57.502 12.67 76.67 33.137 23.715-4.548 46.456-13.32 66.599-25.34-7.798 24.366-24.366 44.833-46.132 57.827 21.117-2.273 41.584-8.122 60.426-16.243-14.292 20.791-32.161 39.308-52.628 54.253z">
                                                                </path>
                                                            </svg> </span>
                                                    </a>
                                                    <a class="qodef-e-social-icon-link" itemprop="url" href="#"
                                                        target="_blank">
                                                        <span class="qodef-e-social-icon">
                                                            <svg aria-hidden="true"
                                                                class="e-font-icon-svg e-fab-instagram"
                                                                viewBox="0 0 448 512"
                                                                xmlns="http://www.w3.org/2000/svg">
                                                                <path
                                                                    d="M224.1 141c-63.6 0-114.9 51.3-114.9 114.9s51.3 114.9 114.9 114.9S339 319.5 339 255.9 287.7 141 224.1 141zm0 189.6c-41.1 0-74.7-33.5-74.7-74.7s33.5-74.7 74.7-74.7 74.7 33.5 74.7 74.7-33.6 74.7-74.7 74.7zm146.4-194.3c0 14.9-12 26.8-26.8 26.8-14.9 0-26.8-12-26.8-26.8s12-26.8 26.8-26.8 26.8 12 26.8 26.8zm76.1 27.2c-1.7-35.9-9.9-67.7-36.2-93.9-26.2-26.2-58-34.4-93.9-36.2-37-2.1-147.9-2.1-184.9 0-35.8 1.7-67.6 9.9-93.9 36.1s-34.4 58-36.2 93.9c-2.1 37-2.1 147.9 0 184.9 1.7 35.9 9.9 67.7 36.2 93.9s58 34.4 93.9 36.2c37 2.1 147.9 2.1 184.9 0 35.9-1.7 67.7-9.9 93.9-36.2 26.2-26.2 34.4-58 36.2-93.9 2.1-37 2.1-147.8 0-184.8zM398.8 388c-7.8 19.6-22.9 34.7-42.6 42.6-29.5 11.7-99.5 9-132.1 9s-102.7 2.6-132.1-9c-19.6-7.8-34.7-22.9-42.6-42.6-11.7-29.5-9-99.5-9-132.1s-2.6-102.7 9-132.1c7.8-19.6 22.9-34.7 42.6-42.6 29.5-11.7 99.5-9 132.1-9s102.7-2.6 132.1 9c19.6 7.8 34.7 22.9 42.6 42.6 11.7 29.5 9 99.5 9 132.1s2.7 102.7-9 132.1z">
                                                                </path>
                                                            </svg> </span>
                                                    </a>
                                                    <a class="qodef-e-social-icon-link" itemprop="url" href="#"
                                                        target="_blank">
                                                        <span class="qodef-e-social-icon">
                                                            <svg aria-hidden="true"
                                                                class="e-font-icon-svg e-far-envelope"
                                                                viewBox="0 0 512 512"
                                                                xmlns="http://www.w3.org/2000/svg">
                                                                <path
                                                                    d="M464 64H48C21.49 64 0 85.49 0 112v288c0 26.51 21.49 48 48 48h416c26.51 0 48-21.49 48-48V112c0-26.51-21.49-48-48-48zm0 48v40.805c-22.422 18.259-58.168 46.651-134.587 106.49-16.841 13.247-50.201 45.072-73.413 44.701-23.208.375-56.579-31.459-73.413-44.701C106.18 199.465 70.425 171.067 48 152.805V112h416zM48 400V214.398c22.914 18.251 55.409 43.862 104.938 82.646 21.857 17.205 60.134 55.186 103.062 54.955 42.717.231 80.509-37.199 103.053-54.947 49.528-38.783 82.032-64.401 104.947-82.653V400H48z">
                                                                </path>
                                                            </svg> </span>
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="elementor-element elementor-element-187895c e-con-full e-flex e-con e-child"
                            data-id="187895c" data-element_type="container">
                            <div class="elementor-element elementor-element-3e20992 animated-slow elementor-invisible elementor-widget elementor-widget-qi_addons_for_elementor_team_member"
                                data-id="3e20992" data-element_type="widget"
                                data-settings="{&quot;_animation&quot;:&quot;fadeIn&quot;,&quot;_animation_delay&quot;:&quot;200&quot;}"
                                data-widget_type="qi_addons_for_elementor_team_member.default">
                                <div class="elementor-widget-container">
                                    <div
                                        class="qodef-shortcode qodef-m qodef-qi-team-member qodef-item-layout--info-from-bottom qodef-image--hover-zoom">
                                        <div class="qodef-m-inner">
                                            <div class="qodef-m-image">
                                                <div class="qodef-m-media-image">
                                                    <img loading="lazy" decoding="async" width="800" height="900"
                                                        src="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-21.jpg"
                                                        class="attachment-full size-full" alt=""
                                                        srcset="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-21.jpg 800w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-21-267x300.jpg 267w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-21-768x864.jpg 768w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-21-489x550.jpg 489w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-21-550x619.jpg 550w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-21-444x500.jpg 444w"
                                                        sizes="(max-width: 800px) 100vw, 800px" />
                                                </div>
                                            </div>
                                            <div class="qodef-m-content">
                                                <h4 itemprop="name" class="qodef-m-title">
                                                    Ava Morgan </h4>
                                                <p class="qodef-m-role">Project Manager</p>
                                                <div class="qodef-m-social-icons">
                                                    <a class="qodef-e-social-icon-link" itemprop="url" href="#"
                                                        target="_blank">
                                                        <span class="qodef-e-social-icon">
                                                            <svg aria-hidden="true"
                                                                class="e-font-icon-svg e-fab-facebook"
                                                                viewBox="0 0 512 512"
                                                                xmlns="http://www.w3.org/2000/svg">
                                                                <path
                                                                    d="M504 256C504 119 393 8 256 8S8 119 8 256c0 123.78 90.69 226.38 209.25 245V327.69h-63V256h63v-54.64c0-62.15 37-96.48 93.67-96.48 27.14 0 55.52 4.84 55.52 4.84v61h-31.28c-30.8 0-40.41 19.12-40.41 38.73V256h68.78l-11 71.69h-57.78V501C413.31 482.38 504 379.78 504 256z">
                                                                </path>
                                                            </svg> </span>
                                                    </a>
                                                    <a class="qodef-e-social-icon-link" itemprop="url" href="#"
                                                        target="_blank">
                                                        <span class="qodef-e-social-icon">
                                                            <svg aria-hidden="true"
                                                                class="e-font-icon-svg e-fab-twitter"
                                                                viewBox="0 0 512 512"
                                                                xmlns="http://www.w3.org/2000/svg">
                                                                <path
                                                                    d="M459.37 151.716c.325 4.548.325 9.097.325 13.645 0 138.72-105.583 298.558-298.558 298.558-59.452 0-114.68-17.219-161.137-47.106 8.447.974 16.568 1.299 25.34 1.299 49.055 0 94.213-16.568 130.274-44.832-46.132-.975-84.792-31.188-98.112-72.772 6.498.974 12.995 1.624 19.818 1.624 9.421 0 18.843-1.3 27.614-3.573-48.081-9.747-84.143-51.98-84.143-102.985v-1.299c13.969 7.797 30.214 12.67 47.431 13.319-28.264-18.843-46.781-51.005-46.781-87.391 0-19.492 5.197-37.36 14.294-52.954 51.655 63.675 129.3 105.258 216.365 109.807-1.624-7.797-2.599-15.918-2.599-24.04 0-57.828 46.782-104.934 104.934-104.934 30.213 0 57.502 12.67 76.67 33.137 23.715-4.548 46.456-13.32 66.599-25.34-7.798 24.366-24.366 44.833-46.132 57.827 21.117-2.273 41.584-8.122 60.426-16.243-14.292 20.791-32.161 39.308-52.628 54.253z">
                                                                </path>
                                                            </svg> </span>
                                                    </a>
                                                    <a class="qodef-e-social-icon-link" itemprop="url" href="#"
                                                        target="_blank">
                                                        <span class="qodef-e-social-icon">
                                                            <svg aria-hidden="true"
                                                                class="e-font-icon-svg e-fab-instagram"
                                                                viewBox="0 0 448 512"
                                                                xmlns="http://www.w3.org/2000/svg">
                                                                <path
                                                                    d="M224.1 141c-63.6 0-114.9 51.3-114.9 114.9s51.3 114.9 114.9 114.9S339 319.5 339 255.9 287.7 141 224.1 141zm0 189.6c-41.1 0-74.7-33.5-74.7-74.7s33.5-74.7 74.7-74.7 74.7 33.5 74.7 74.7-33.6 74.7-74.7 74.7zm146.4-194.3c0 14.9-12 26.8-26.8 26.8-14.9 0-26.8-12-26.8-26.8s12-26.8 26.8-26.8 26.8 12 26.8 26.8zm76.1 27.2c-1.7-35.9-9.9-67.7-36.2-93.9-26.2-26.2-58-34.4-93.9-36.2-37-2.1-147.9-2.1-184.9 0-35.8 1.7-67.6 9.9-93.9 36.1s-34.4 58-36.2 93.9c-2.1 37-2.1 147.9 0 184.9 1.7 35.9 9.9 67.7 36.2 93.9s58 34.4 93.9 36.2c37 2.1 147.9 2.1 184.9 0 35.9-1.7 67.7-9.9 93.9-36.2 26.2-26.2 34.4-58 36.2-93.9 2.1-37 2.1-147.8 0-184.8zM398.8 388c-7.8 19.6-22.9 34.7-42.6 42.6-29.5 11.7-99.5 9-132.1 9s-102.7 2.6-132.1-9c-19.6-7.8-34.7-22.9-42.6-42.6-11.7-29.5-9-99.5-9-132.1s-2.6-102.7 9-132.1c7.8-19.6 22.9-34.7 42.6-42.6 29.5-11.7 99.5-9 132.1-9s102.7-2.6 132.1 9c19.6 7.8 34.7 22.9 42.6 42.6 11.7 29.5 9 99.5 9 132.1s2.7 102.7-9 132.1z">
                                                                </path>
                                                            </svg> </span>
                                                    </a>
                                                    <a class="qodef-e-social-icon-link" itemprop="url" href="#"
                                                        target="_blank">
                                                        <span class="qodef-e-social-icon">
                                                            <svg aria-hidden="true"
                                                                class="e-font-icon-svg e-far-envelope"
                                                                viewBox="0 0 512 512"
                                                                xmlns="http://www.w3.org/2000/svg">
                                                                <path
                                                                    d="M464 64H48C21.49 64 0 85.49 0 112v288c0 26.51 21.49 48 48 48h416c26.51 0 48-21.49 48-48V112c0-26.51-21.49-48-48-48zm0 48v40.805c-22.422 18.259-58.168 46.651-134.587 106.49-16.841 13.247-50.201 45.072-73.413 44.701-23.208.375-56.579-31.459-73.413-44.701C106.18 199.465 70.425 171.067 48 152.805V112h416zM48 400V214.398c22.914 18.251 55.409 43.862 104.938 82.646 21.857 17.205 60.134 55.186 103.062 54.955 42.717.231 80.509-37.199 103.053-54.947 49.528-38.783 82.032-64.401 104.947-82.653V400H48z">
                                                                </path>
                                                            </svg> </span>
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="elementor-element elementor-element-48d04ed e-con-full e-flex e-con e-child"
                            data-id="48d04ed" data-element_type="container">
                            <div class="elementor-element elementor-element-33c6148 animated-slow elementor-invisible elementor-widget elementor-widget-qi_addons_for_elementor_team_member"
                                data-id="33c6148" data-element_type="widget"
                                data-settings="{&quot;_animation&quot;:&quot;fadeIn&quot;,&quot;_animation_delay&quot;:&quot;200&quot;}"
                                data-widget_type="qi_addons_for_elementor_team_member.default">
                                <div class="elementor-widget-container">
                                    <div
                                        class="qodef-shortcode qodef-m qodef-qi-team-member qodef-item-layout--info-from-bottom qodef-image--hover-zoom">
                                        <div class="qodef-m-inner">
                                            <div class="qodef-m-image">
                                                <div class="qodef-m-media-image">
                                                    <img loading="lazy" decoding="async" width="800" height="900"
                                                        src="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-14.jpg"
                                                        class="attachment-full size-full" alt=""
                                                        srcset="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-14.jpg 800w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-14-267x300.jpg 267w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-14-768x864.jpg 768w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-14-489x550.jpg 489w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-14-550x619.jpg 550w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-14-444x500.jpg 444w"
                                                        sizes="(max-width: 800px) 100vw, 800px" />
                                                </div>
                                            </div>
                                            <div class="qodef-m-content">
                                                <h4 itemprop="name" class="qodef-m-title">
                                                    Oliver James </h4>
                                                <p class="qodef-m-role">Furniture Designer</p>
                                                <div class="qodef-m-social-icons">
                                                    <a class="qodef-e-social-icon-link" itemprop="url" href="#"
                                                        target="_blank">
                                                        <span class="qodef-e-social-icon">
                                                            <svg aria-hidden="true"
                                                                class="e-font-icon-svg e-fab-facebook"
                                                                viewBox="0 0 512 512"
                                                                xmlns="http://www.w3.org/2000/svg">
                                                                <path
                                                                    d="M504 256C504 119 393 8 256 8S8 119 8 256c0 123.78 90.69 226.38 209.25 245V327.69h-63V256h63v-54.64c0-62.15 37-96.48 93.67-96.48 27.14 0 55.52 4.84 55.52 4.84v61h-31.28c-30.8 0-40.41 19.12-40.41 38.73V256h68.78l-11 71.69h-57.78V501C413.31 482.38 504 379.78 504 256z">
                                                                </path>
                                                            </svg> </span>
                                                    </a>
                                                    <a class="qodef-e-social-icon-link" itemprop="url" href="#"
                                                        target="_blank">
                                                        <span class="qodef-e-social-icon">
                                                            <svg aria-hidden="true"
                                                                class="e-font-icon-svg e-fab-twitter"
                                                                viewBox="0 0 512 512"
                                                                xmlns="http://www.w3.org/2000/svg">
                                                                <path
                                                                    d="M459.37 151.716c.325 4.548.325 9.097.325 13.645 0 138.72-105.583 298.558-298.558 298.558-59.452 0-114.68-17.219-161.137-47.106 8.447.974 16.568 1.299 25.34 1.299 49.055 0 94.213-16.568 130.274-44.832-46.132-.975-84.792-31.188-98.112-72.772 6.498.974 12.995 1.624 19.818 1.624 9.421 0 18.843-1.3 27.614-3.573-48.081-9.747-84.143-51.98-84.143-102.985v-1.299c13.969 7.797 30.214 12.67 47.431 13.319-28.264-18.843-46.781-51.005-46.781-87.391 0-19.492 5.197-37.36 14.294-52.954 51.655 63.675 129.3 105.258 216.365 109.807-1.624-7.797-2.599-15.918-2.599-24.04 0-57.828 46.782-104.934 104.934-104.934 30.213 0 57.502 12.67 76.67 33.137 23.715-4.548 46.456-13.32 66.599-25.34-7.798 24.366-24.366 44.833-46.132 57.827 21.117-2.273 41.584-8.122 60.426-16.243-14.292 20.791-32.161 39.308-52.628 54.253z">
                                                                </path>
                                                            </svg> </span>
                                                    </a>
                                                    <a class="qodef-e-social-icon-link" itemprop="url" href="#"
                                                        target="_blank">
                                                        <span class="qodef-e-social-icon">
                                                            <svg aria-hidden="true"
                                                                class="e-font-icon-svg e-fab-instagram"
                                                                viewBox="0 0 448 512"
                                                                xmlns="http://www.w3.org/2000/svg">
                                                                <path
                                                                    d="M224.1 141c-63.6 0-114.9 51.3-114.9 114.9s51.3 114.9 114.9 114.9S339 319.5 339 255.9 287.7 141 224.1 141zm0 189.6c-41.1 0-74.7-33.5-74.7-74.7s33.5-74.7 74.7-74.7 74.7 33.5 74.7 74.7-33.6 74.7-74.7 74.7zm146.4-194.3c0 14.9-12 26.8-26.8 26.8-14.9 0-26.8-12-26.8-26.8s12-26.8 26.8-26.8 26.8 12 26.8 26.8zm76.1 27.2c-1.7-35.9-9.9-67.7-36.2-93.9-26.2-26.2-58-34.4-93.9-36.2-37-2.1-147.9-2.1-184.9 0-35.8 1.7-67.6 9.9-93.9 36.1s-34.4 58-36.2 93.9c-2.1 37-2.1 147.9 0 184.9 1.7 35.9 9.9 67.7 36.2 93.9s58 34.4 93.9 36.2c37 2.1 147.9 2.1 184.9 0 35.9-1.7 67.7-9.9 93.9-36.2 26.2-26.2 34.4-58 36.2-93.9 2.1-37 2.1-147.8 0-184.8zM398.8 388c-7.8 19.6-22.9 34.7-42.6 42.6-29.5 11.7-99.5 9-132.1 9s-102.7 2.6-132.1-9c-19.6-7.8-34.7-22.9-42.6-42.6-11.7-29.5-9-99.5-9-132.1s-2.6-102.7 9-132.1c7.8-19.6 22.9-34.7 42.6-42.6 29.5-11.7 99.5-9 132.1-9s102.7-2.6 132.1 9c19.6 7.8 34.7 22.9 42.6 42.6 11.7 29.5 9 99.5 9 132.1s2.7 102.7-9 132.1z">
                                                                </path>
                                                            </svg> </span>
                                                    </a>
                                                    <a class="qodef-e-social-icon-link" itemprop="url" href="#"
                                                        target="_blank">
                                                        <span class="qodef-e-social-icon">
                                                            <svg aria-hidden="true"
                                                                class="e-font-icon-svg e-far-envelope"
                                                                viewBox="0 0 512 512"
                                                                xmlns="http://www.w3.org/2000/svg">
                                                                <path
                                                                    d="M464 64H48C21.49 64 0 85.49 0 112v288c0 26.51 21.49 48 48 48h416c26.51 0 48-21.49 48-48V112c0-26.51-21.49-48-48-48zm0 48v40.805c-22.422 18.259-58.168 46.651-134.587 106.49-16.841 13.247-50.201 45.072-73.413 44.701-23.208.375-56.579-31.459-73.413-44.701C106.18 199.465 70.425 171.067 48 152.805V112h416zM48 400V214.398c22.914 18.251 55.409 43.862 104.938 82.646 21.857 17.205 60.134 55.186 103.062 54.955 42.717.231 80.509-37.199 103.053-54.947 49.528-38.783 82.032-64.401 104.947-82.653V400H48z">
                                                                </path>
                                                            </svg> </span>
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <section
                class="elementor-section elementor-top-section elementor-element elementor-element-cade999 elementor-section-height-min-height -bg-dark elementor-section-boxed elementor-section-height-default elementor-section-items-middle"
                data-id="cade999" data-element_type="section"
                data-settings="{&quot;background_background&quot;:&quot;classic&quot;}">
                <div class="elementor-background-overlay"></div>
                <div class="elementor-container elementor-column-gap-default">
                    <div class="elementor-column elementor-col-50 elementor-top-column elementor-element elementor-element-923d7bf"
                        data-id="923d7bf" data-element_type="column">
                        <div class="elementor-widget-wrap elementor-element-populated">
                            <div class="elementor-element elementor-element-256783c elementor-widget__width-initial elementor-widget elementor-widget-qi_addons_for_elementor_animated_text"
                                data-id="256783c" data-element_type="widget"
                                data-widget_type="qi_addons_for_elementor_animated_text.default">
                                <div class="elementor-widget-container">
                                    <div
                                        class="qodef-shortcode qodef-m qodef-qi-animated-text qodef--animated-by-letter qodef-qi--has-appear qodef--appear-fade">
                                        <h3 class="qodef-m-title">
                                            <span class="qodef-e-word-holder">—</span> <span
                                                class="qodef-e-word-holder">Collaboration</span>
                                        </h3>
                                    </div>
                                </div>
                            </div>
                            <div class="elementor-element elementor-element-9aaa1e9 elementor-invisible elementor-widget elementor-widget-qi_addons_for_elementor_animated_text"
                                data-id="9aaa1e9" data-element_type="widget"
                                data-settings="{&quot;_animation&quot;:&quot;fadeIn&quot;}"
                                data-widget_type="qi_addons_for_elementor_animated_text.default">
                                <div class="elementor-widget-container">
                                    <div
                                        class="qodef-shortcode qodef-m qodef-qi-animated-text qodef--animated-by-letter qodef-qi--has-appear qodef--appear-fade">
                                        <h1 class="qodef-m-title">
                                            <span class="qodef-e-word-holder">Let’s</span> <span
                                                class="qodef-e-word-holder">Create</span> <span
                                                class="qodef-e-word-holder">Something</span> <span
                                                class="qodef-e-word-holder">Beautiful</span> <span
                                                class="qodef-e-word-holder">Together</span>
                                        </h1>
                                    </div>
                                </div>
                            </div>
                            <section
                                class="elementor-section elementor-inner-section elementor-element elementor-element-0d13f7a elementor-section-full_width elementor-section-height-default elementor-section-height-default"
                                data-id="0d13f7a" data-element_type="section">
                                <div class="elementor-container elementor-column-gap-default">
                                    <div class="elementor-column elementor-col-100 elementor-inner-column elementor-element elementor-element-b17cac4"
                                        data-id="b17cac4" data-element_type="column">
                                        <div class="elementor-widget-wrap elementor-element-populated">
                                            <div class="elementor-element elementor-element-c898fce elementor-widget elementor-widget-heading"
                                                data-id="c898fce" data-element_type="widget"
                                                data-widget_type="heading.default">
                                                <div class="elementor-widget-container">
                                                    <h3 class="elementor-heading-title elementor-size-default">Ready to
                                                        transform your space? We’d love to hear from you! <u>Get in
                                                            touch</u> today to schedule a consultation and start
                                                        bringing your vision to life.</h3>
                                                </div>
                                            </div>
                                            <div class="elementor-element elementor-element-2159ea9 elementor-invisible elementor-widget elementor-widget-qi_addons_for_elementor_button"
                                                data-id="2159ea9" data-element_type="widget"
                                                data-settings="{&quot;_animation&quot;:&quot;fadeInUp&quot;,&quot;_animation_delay&quot;:800}"
                                                data-widget_type="qi_addons_for_elementor_button.default">
                                                <div class="elementor-widget-container">
                                                    <a class="qodef-shortcode qodef-m qodef-qi-button qodef-html--link qodef-layout--outlined qodef-type--standard qodef-icon--right qodef-hover--icon-move-horizontal-short"
                                                        href="contact.html"
                                                        target="_self">
                                                        <span class="qodef-m-text">Get In Touch</span>
                                                        <span class="qodef-m-icon">
                                                            <span class="qodef-m-icon-inner">
                                                                <svg xmlns="http://www.w3.org/2000/svg" height="21"
                                                                    viewBox="0 0 21 21" width="21">
                                                                    <g fill="none" fill-rule="evenodd"
                                                                        stroke="currentColor" stroke-linecap="round"
                                                                        stroke-linejoin="round"
                                                                        transform="translate(6 6)">
                                                                        <path d="m8.5 7.5v-7h-7"></path>
                                                                        <path d="m8.5.5-8 8"></path>
                                                                    </g>
                                                                </svg> </span>
                                                        </span>
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </section>
                        </div>
                    </div>
                    <div class="elementor-column elementor-col-25 elementor-top-column elementor-element elementor-element-7b2830c"
                        data-id="7b2830c" data-element_type="column">
                        <div class="elementor-widget-wrap">
                        </div>
                    </div>
                    <div class="elementor-column elementor-col-25 elementor-top-column elementor-element elementor-element-66cbcb9"
                        data-id="66cbcb9" data-element_type="column">
                        <div class="elementor-widget-wrap elementor-element-populated">
                            <div class="elementor-element elementor-element-a1538f7 elementor-widget elementor-widget-heading"
                                data-id="a1538f7" data-element_type="widget" data-widget_type="heading.default">
                                <div class="elementor-widget-container">
                                    <h4 class="elementor-heading-title elementor-size-default">— Phone</h4>
                                </div>
                            </div>
                            <div class="elementor-element elementor-element-cb6b5e1 elementor-widget elementor-widget-heading"
                                data-id="cb6b5e1" data-element_type="widget" data-widget_type="heading.default">
                                <div class="elementor-widget-container">
                                    <h3 class="elementor-heading-title elementor-size-default">+34 567 721 12 35</h3>
                                </div>
                            </div>
                            <div class="elementor-element elementor-element-82dadfd elementor-widget elementor-widget-spacer"
                                data-id="82dadfd" data-element_type="widget" data-widget_type="spacer.default">
                                <div class="elementor-widget-container">
                                    <div class="elementor-spacer">
                                        <div class="elementor-spacer-inner"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="elementor-element elementor-element-66dc705 elementor-widget elementor-widget-heading"
                                data-id="66dc705" data-element_type="widget" data-widget_type="heading.default">
                                <div class="elementor-widget-container">
                                    <h4 class="elementor-heading-title elementor-size-default">— Email</h4>
                                </div>
                            </div>
                            <div class="elementor-element elementor-element-eb72d81 elementor-widget elementor-widget-heading"
                                data-id="eb72d81" data-element_type="widget" data-widget_type="heading.default">
                                <div class="elementor-widget-container">
                                    <h4 class="elementor-heading-title elementor-size-default"><a
                                            href="mailto:<EMAIL>"><EMAIL></a></h4>
                                </div>
                            </div>
                            <div class="elementor-element elementor-element-9b5dce2 elementor-widget elementor-widget-spacer"
                                data-id="9b5dce2" data-element_type="widget" data-widget_type="spacer.default">
                                <div class="elementor-widget-container">
                                    <div class="elementor-spacer">
                                        <div class="elementor-spacer-inner"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="elementor-element elementor-element-5fb9725 elementor-widget elementor-widget-heading"
                                data-id="5fb9725" data-element_type="widget" data-widget_type="heading.default">
                                <div class="elementor-widget-container">
                                    <h4 class="elementor-heading-title elementor-size-default">— Directions</h4>
                                </div>
                            </div>
                            <div class="elementor-element elementor-element-6d48495 elementor-widget elementor-widget-heading"
                                data-id="6d48495" data-element_type="widget" data-widget_type="heading.default">
                                <div class="elementor-widget-container">
                                    <p class="elementor-heading-title elementor-size-default">77 Kennedy Road <br> Soho
                                        Manhattan <br> New York - USA</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>


        <footer id="colophon" class="site-footer" role="contentinfo">
            <div class="site-info">

                <p>© 2025 Motiff Square </p>
            </div> <!-- .site-info -->
        </footer> <!-- #colophon .site-footer -->
    </div>
    <script type="speculationrules">
{"prefetch":[{"source":"document","where":{"and":[{"href_matches":"\/interique\/demo-01\/*"},{"not":{"href_matches":["\/interique\/demo-01\/wp-*.php","\/interique\/demo-01\/wp-admin\/*","\/interique\/demo-01\/wp-content\/uploads\/sites\/2\/*","\/interique\/demo-01\/wp-content\/*","\/interique\/demo-01\/wp-content\/plugins\/*","\/interique\/demo-01\/wp-content\/themes\/interique\/*","\/interique\/demo-01\/*\\?(.+)"]}},{"not":{"selector_matches":"a[rel~=\"nofollow\"]"}},{"not":{"selector_matches":".no-prefetch, .no-prefetch a"}}]},"eagerness":"conservative"}]}
</script>
    <script>
        const lazyloadRunObserver = () => {
            const lazyloadBackgrounds = document.querySelectorAll(`.e-con.e-parent:not(.e-lazyloaded)`);
            const lazyloadBackgroundObserver = new IntersectionObserver((entries) => {
                entries.forEach((entry) => {
                    if (entry.isIntersecting) {
                        let lazyloadBackground = entry.target;
                        if (lazyloadBackground) {
                            lazyloadBackground.classList.add('e-lazyloaded');
                        }
                        lazyloadBackgroundObserver.unobserve(entry.target);
                    }
                });
            }, { rootMargin: '200px 0px 200px 0px' });
            lazyloadBackgrounds.forEach((lazyloadBackground) => {
                lazyloadBackgroundObserver.observe(lazyloadBackground);
            });
        };
        const events = [
            'DOMContentLoaded',
            'elementor/lazyload/observe',
        ];
        events.forEach((event) => {
            document.addEventListener(event, lazyloadRunObserver);
        });
    </script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-includes/js/jquery/ui/core.min.js?ver=1.13.3"
        id="jquery-ui-core-js"></script>
    <script type="text/javascript" id="qi-addons-for-elementor-script-js-extra">
        /* <![CDATA[ */
        var qodefQiAddonsGlobal = { "vars": { "adminBarHeight": 0, "iconArrowLeft": "<svg  xmlns=\"http:\/\/www.w3.org\/2000\/svg\" x=\"0px\" y=\"0px\" viewBox=\"0 0 34.2 32.3\" xml:space=\"preserve\" style=\"stroke-width: 2;\"><line x1=\"0.5\" y1=\"16\" x2=\"33.5\" y2=\"16\"\/><line x1=\"0.3\" y1=\"16.5\" x2=\"16.2\" y2=\"0.7\"\/><line x1=\"0\" y1=\"15.4\" x2=\"16.2\" y2=\"31.6\"\/><\/svg>", "iconArrowRight": "<svg  xmlns=\"http:\/\/www.w3.org\/2000\/svg\" x=\"0px\" y=\"0px\" viewBox=\"0 0 34.2 32.3\" xml:space=\"preserve\" style=\"stroke-width: 2;\"><line x1=\"0\" y1=\"16\" x2=\"33\" y2=\"16\"\/><line x1=\"17.3\" y1=\"0.7\" x2=\"33.2\" y2=\"16.5\"\/><line x1=\"17.3\" y1=\"31.6\" x2=\"33.5\" y2=\"15.4\"\/><\/svg>", "iconClose": "<svg  xmlns=\"http:\/\/www.w3.org\/2000\/svg\" x=\"0px\" y=\"0px\" viewBox=\"0 0 9.1 9.1\" xml:space=\"preserve\"><g><path d=\"M8.5,0L9,0.6L5.1,4.5L9,8.5L8.5,9L4.5,5.1L0.6,9L0,8.5L4,4.5L0,0.6L0.6,0L4.5,4L8.5,0z\"\/><\/g><\/svg>" } };
        /* ]]> */
    </script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/qi-addons-for-elementor/assets/js/main.min.js?ver=1.8.9"
        id="qi-addons-for-elementor-script-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/js/jquery.fitvids.js"
        id="fitvids-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/js/jarallax.min.js"
        id="jarallax-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/js/jarallax-video.min.js"
        id="jarallax-video-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/js/fluidbox/jquery.fluidbox.min.js"
        id="fluidbox-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/js/jquery-validation/jquery.validate.js"
        id="jqueryvalidation-js"></script>
    <script type="text/javascript" id="jqueryvalidation-js-after">
        /* <![CDATA[ */
        (function ($) {
            "use strict";
            $.extend($.validator.messages, {
                required: "This field is required.",
                remote: "Please fix this field.",
                email: "Please enter a valid email address.",
                url: "Please enter a valid URL.",
                date: "Please enter a valid date.",
                dateISO: "Please enter a valid date ( ISO ).",
                number: "Please enter a valid number.",
                digits: "Please enter only digits.",
                equalTo: "Please enter the same value again.",
                maxlength: $.validator.format("Please enter no more than {0} characters."),
                minlength: $.validator.format("Please enter at least {0} characters."),
                rangelength: $.validator.format("Please enter a value between {0} and {1} characters long."),
                range: $.validator.format("Please enter a value between {0} and {1}."),
                max: $.validator.format("Please enter a value less than or equal to {0}."),
                min: $.validator.format("Please enter a value greater than or equal to {0}."),
                step: $.validator.format("Please enter a multiple of {0}.")
            });
        })(jQuery);
        /* ]]> */
    </script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/qi-addons-for-elementor/inc/masonry/assets/js/plugins/isotope.pkgd.min.js?ver=3.0.6"
        id="isotope-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/js/jquery.magnific-popup/jquery.magnific-popup.min.js"
        id="magnific-popup-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/js/owl-carousel/owl.carousel.min.js"
        id="owl-carousel-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-includes/js/imagesloaded.min.js?ver=5.0.0"
        id="imagesloaded-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/js/jquery.collagePlus.min.js"
        id="collagePlus-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/js/jquery.fittext.js"
        id="fittext-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/js/resize-sensor.js"
        id="resize-sensor-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/js/jquery.sticky-sidebar.min.js"
        id="sticky-sidebar-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/js/main.js"
        id="interique-main-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/pixelwars-core/themes/global/js/shortcodes.js"
        id="pixelwars-core-shortcodes-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/elementor/assets/js/webpack.runtime.min.js?ver=3.29.0"
        id="elementor-webpack-runtime-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/elementor/assets/js/frontend-modules.min.js?ver=3.29.0"
        id="elementor-frontend-modules-js"></script>
    <script type="text/javascript" id="elementor-frontend-js-before">
        /* <![CDATA[ */
        var elementorFrontendConfig = { "environmentMode": { "edit": false, "wpPreview": false, "isScriptDebug": false }, "i18n": { "shareOnFacebook": "Share on Facebook", "shareOnTwitter": "Share on Twitter", "pinIt": "Pin it", "download": "Download", "downloadImage": "Download image", "fullscreen": "Fullscreen", "zoom": "Zoom", "share": "Share", "playVideo": "Play Video", "previous": "Previous", "next": "Next", "close": "Close", "a11yCarouselPrevSlideMessage": "Previous slide", "a11yCarouselNextSlideMessage": "Next slide", "a11yCarouselFirstSlideMessage": "This is the first slide", "a11yCarouselLastSlideMessage": "This is the last slide", "a11yCarouselPaginationBulletMessage": "Go to slide" }, "is_rtl": false, "breakpoints": { "xs": 0, "sm": 480, "md": 768, "lg": 1025, "xl": 1440, "xxl": 1600 }, "responsive": { "breakpoints": { "mobile": { "label": "Mobile Portrait", "value": 767, "default_value": 767, "direction": "max", "is_enabled": true }, "mobile_extra": { "label": "Mobile Landscape", "value": 880, "default_value": 880, "direction": "max", "is_enabled": false }, "tablet": { "label": "Tablet Portrait", "value": 1024, "default_value": 1024, "direction": "max", "is_enabled": true }, "tablet_extra": { "label": "Tablet Landscape", "value": 1200, "default_value": 1200, "direction": "max", "is_enabled": false }, "laptop": { "label": "Laptop", "value": 1366, "default_value": 1366, "direction": "max", "is_enabled": false }, "widescreen": { "label": "Widescreen", "value": 2400, "default_value": 2400, "direction": "min", "is_enabled": false } }, "hasCustomBreakpoints": false }, "version": "3.29.0", "is_static": false, "experimentalFeatures": { "e_font_icon_svg": true, "additional_custom_breakpoints": true, "container": true, "e_local_google_fonts": true, "nested-elements": true, "e_element_cache": true, "home_screen": true, "launchpad-checklist": true, "cloud-library": true, "e_opt_in_v4_page": true }, "urls": { "assets": "https:\/\/themes.pixelwars.org\/interique\/demo-01\/wp-content\/plugins\/elementor\/assets\/", "ajaxurl": "https:\/\/themes.pixelwars.org\/interique\/demo-01\/wp-admin\/admin-ajax.php", "uploadUrl": "https:\/\/themes.pixelwars.org\/interique\/demo-01\/wp-content\/uploads\/sites\/2" }, "nonces": { "floatingButtonsClickTracking": "0b692f7042" }, "swiperClass": "swiper", "settings": { "page": [], "editorPreferences": [] }, "kit": { "active_breakpoints": ["viewport_mobile", "viewport_tablet"], "global_image_lightbox": "yes", "lightbox_enable_counter": "yes", "lightbox_enable_fullscreen": "yes", "lightbox_enable_zoom": "yes", "lightbox_enable_share": "yes", "lightbox_title_src": "title", "lightbox_description_src": "description" }, "post": { "id": 17804, "title": "Services%20%E2%80%93%20Interiqu%C3%A9", "excerpt": "", "featuredImage": false } };
        /* ]]> */
    </script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/elementor/assets/js/frontend.min.js?ver=3.29.0"
        id="elementor-frontend-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/qi-addons-for-elementor/inc/shortcodes/parallax-images/assets/js/plugins/jquery.parallax-scroll.js?ver=1"
        id="parallax-scroll-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-includes/js/jquery/ui/accordion.min.js?ver=1.13.3"
        id="jquery-ui-accordion-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/bdthemes-prime-slider-lite/assets/js/prime-slider-site.min.js?ver=3.17.12"
        id="prime-slider-site-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-includes/js/dist/hooks.min.js?ver=4d63a3d491d11ffd8ac6"
        id="wp-hooks-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-includes/js/dist/i18n.min.js?ver=5e580eb46a90c2b997e6"
        id="wp-i18n-js"></script>
    <script type="text/javascript" id="wp-i18n-js-after">
        /* <![CDATA[ */
        wp.i18n.setLocaleData({ 'text direction\u0004ltr': ['ltr'] });
        /* ]]> */
    </script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/qi-addons-for-elementor/inc/plugins/elementor/assets/js/elementor.js?ver=6.8.1"
        id="qi-addons-for-elementor-elementor-js"></script>
</body>

</html>