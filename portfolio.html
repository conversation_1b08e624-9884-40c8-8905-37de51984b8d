<!doctype html>
<html lang="en-US"
    class="no-featured-area is-header-small is-body-full-width is-single-post-title-default is-post-title-align-center is-post-media-fixed is-blog-text-align-left is-meta-with-icons is-header-light is-header-full-width is-header-parallax-no is-menu-sticky is-menu-fixed-width is-menu-align-center is-menu-light is-submenu-light is-submenu-align-left is-menu-uppercase is-featured-area-full is-slider-buttons-center-margin is-slider-buttons-rounded is-slider-buttons-dark is-slider-title-label is-slider-parallax is-slider-title-none-uppercase is-slider-more-link-show-on-hover is-slider-more-link-border-bottom is-slider-text-align-center is-slider-v-align-center is-slider-h-align-center is-link-box-title-default is-link-box-title-transform-none is-link-box-text-align-center is-link-box-v-align-center is-link-box-parallax is-intro-align-center is-intro-text-dark is-intro-parallax-no is-more-link-border-bottom-light is-about-author-minimal is-related-posts-parallax is-related-posts-fixed is-share-links-boxed is-tagcloud-minimal is-nav-single-rounded is-nav-single-no-animated is-comments-minimal is-comments-image-rounded is-comment-form-boxed is-comment-form-border is-sidebar-right is-sidebar-sticky is-sidebar-align-left is-widget-title-align-left is-widget-bottomline is-trending-posts-default is-footer-subscribe-light is-footer-widgets-align-left is-footer-full-width is-meta-uppercase is-slider-dots-rounded-line-grow is-site-title-uppercase is-top-bar-mobile-left-visible is-top-bar-uppercase is-top-bar-full is-sub-menu-ani-fade-in-left is-menu-hover-badge is-menu-hover-badge-round is-copyright-uppercase is-logo-bg-stretch-left is-header-sticky-shadow-soft-shorter is-header-transparent-border-bottom is-header-bg-blur-slightly is-footer-border-top is-footer-border-light"
    data-title-ratio="0.7" data-link-box-title-ratio="0.5" data-generic-button-style="" data-header-bg-shape="">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="profile" href="http://gmpg.org/xfn/11">
    <title>Portfolio - Motiff Square</title>
    <meta name='robots' content='max-image-preview:large' />
    <style>
        img:is([sizes="auto" i], [sizes^="auto," i]) {
            contain-intrinsic-size: 3000px 1500px
        }
    </style>
    <link rel='dns-prefetch' href='//fonts.googleapis.com' />
    <link rel="alternate" type="application/rss+xml" title="Motiff Square &raquo; Feed"
        href="https://themes.pixelwars.org/interique/demo-01/feed/" />
    <link rel="alternate" type="application/rss+xml" title="Motiff Square &raquo; Comments Feed"
        href="https://themes.pixelwars.org/interique/demo-01/comments/feed/" />
    <script type="text/javascript">
        /* <![CDATA[ */
        window._wpemojiSettings = { "baseUrl": "https:\/\/s.w.org\/images\/core\/emoji\/15.1.0\/72x72\/", "ext": ".png", "svgUrl": "https:\/\/s.w.org\/images\/core\/emoji\/15.1.0\/svg\/", "svgExt": ".svg", "source": { "concatemoji": "https:\/\/themes.pixelwars.org\/interique\/demo-01\/wp-includes\/js\/wp-emoji-release.min.js?ver=6.8.1" } };
        /*! This file is auto-generated */
        !function (i, n) { var o, s, e; function c(e) { try { var t = { supportTests: e, timestamp: (new Date).valueOf() }; sessionStorage.setItem(o, JSON.stringify(t)) } catch (e) { } } function p(e, t, n) { e.clearRect(0, 0, e.canvas.width, e.canvas.height), e.fillText(t, 0, 0); var t = new Uint32Array(e.getImageData(0, 0, e.canvas.width, e.canvas.height).data), r = (e.clearRect(0, 0, e.canvas.width, e.canvas.height), e.fillText(n, 0, 0), new Uint32Array(e.getImageData(0, 0, e.canvas.width, e.canvas.height).data)); return t.every(function (e, t) { return e === r[t] }) } function u(e, t, n) { switch (t) { case "flag": return n(e, "\ud83c\udff3\ufe0f\u200d\u26a7\ufe0f", "\ud83c\udff3\ufe0f\u200b\u26a7\ufe0f") ? !1 : !n(e, "\ud83c\uddfa\ud83c\uddf3", "\ud83c\uddfa\u200b\ud83c\uddf3") && !n(e, "\ud83c\udff4\udb40\udc67\udb40\udc62\udb40\udc65\udb40\udc6e\udb40\udc67\udb40\udc7f", "\ud83c\udff4\u200b\udb40\udc67\u200b\udb40\udc62\u200b\udb40\udc65\u200b\udb40\udc6e\u200b\udb40\udc67\u200b\udb40\udc7f"); case "emoji": return !n(e, "\ud83d\udc26\u200d\ud83d\udd25", "\ud83d\udc26\u200b\ud83d\udd25") }return !1 } function f(e, t, n) { var r = "undefined" != typeof WorkerGlobalScope && self instanceof WorkerGlobalScope ? new OffscreenCanvas(300, 150) : i.createElement("canvas"), a = r.getContext("2d", { willReadFrequently: !0 }), o = (a.textBaseline = "top", a.font = "600 32px Arial", {}); return e.forEach(function (e) { o[e] = t(a, e, n) }), o } function t(e) { var t = i.createElement("script"); t.src = e, t.defer = !0, i.head.appendChild(t) } "undefined" != typeof Promise && (o = "wpEmojiSettingsSupports", s = ["flag", "emoji"], n.supports = { everything: !0, everythingExceptFlag: !0 }, e = new Promise(function (e) { i.addEventListener("DOMContentLoaded", e, { once: !0 }) }), new Promise(function (t) { var n = function () { try { var e = JSON.parse(sessionStorage.getItem(o)); if ("object" == typeof e && "number" == typeof e.timestamp && (new Date).valueOf() < e.timestamp + 604800 && "object" == typeof e.supportTests) return e.supportTests } catch (e) { } return null }(); if (!n) { if ("undefined" != typeof Worker && "undefined" != typeof OffscreenCanvas && "undefined" != typeof URL && URL.createObjectURL && "undefined" != typeof Blob) try { var e = "postMessage(" + f.toString() + "(" + [JSON.stringify(s), u.toString(), p.toString()].join(",") + "));", r = new Blob([e], { type: "text/javascript" }), a = new Worker(URL.createObjectURL(r), { name: "wpTestEmojiSupports" }); return void (a.onmessage = function (e) { c(n = e.data), a.terminate(), t(n) }) } catch (e) { } c(n = f(s, u, p)) } t(n) }).then(function (e) { for (var t in e) n.supports[t] = e[t], n.supports.everything = n.supports.everything && n.supports[t], "flag" !== t && (n.supports.everythingExceptFlag = n.supports.everythingExceptFlag && n.supports[t]); n.supports.everythingExceptFlag = n.supports.everythingExceptFlag && !n.supports.flag, n.DOMReady = !1, n.readyCallback = function () { n.DOMReady = !0 } }).then(function () { return e }).then(function () { var e; n.supports.everything || (n.readyCallback(), (e = n.source || {}).concatemoji ? t(e.concatemoji) : e.wpemoji && e.twemoji && (t(e.twemoji), t(e.wpemoji))) })) }((window, document), window._wpemojiSettings);
        /* ]]> */
    </script>
    <style id='wp-emoji-styles-inline-css' type='text/css'>
        img.wp-smiley,
        img.emoji {
            display: inline !important;
            border: none !important;
            box-shadow: none !important;
            height: 1em !important;
            width: 1em !important;
            margin: 0 0.07em !important;
            vertical-align: -0.1em !important;
            background: none !important;
            padding: 0 !important;
        }
    </style>
    <link rel='stylesheet' id='wp-block-library-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-includes/css/dist/block-library/style.min.css?ver=6.8.1'
        type='text/css' media='all' />
    <style id='classic-theme-styles-inline-css' type='text/css'>
        /*! This file is auto-generated */
        .wp-block-button__link {
            color: #fff;
            background-color: #32373c;
            border-radius: 9999px;
            box-shadow: none;
            text-decoration: none;
            padding: calc(.667em + 2px) calc(1.333em + 2px);
            font-size: 1.125em
        }

        .wp-block-file__button {
            background: #32373c;
            color: #fff;
            text-decoration: none
        }
    </style>
    <style id='global-styles-inline-css' type='text/css'>
        :root {
            --wp--preset--aspect-ratio--square: 1;
            --wp--preset--aspect-ratio--4-3: 4/3;
            --wp--preset--aspect-ratio--3-4: 3/4;
            --wp--preset--aspect-ratio--3-2: 3/2;
            --wp--preset--aspect-ratio--2-3: 2/3;
            --wp--preset--aspect-ratio--16-9: 16/9;
            --wp--preset--aspect-ratio--9-16: 9/16;
            --wp--preset--color--black: #000000;
            --wp--preset--color--cyan-bluish-gray: #abb8c3;
            --wp--preset--color--white: #ffffff;
            --wp--preset--color--pale-pink: #f78da7;
            --wp--preset--color--vivid-red: #cf2e2e;
            --wp--preset--color--luminous-vivid-orange: #ff6900;
            --wp--preset--color--luminous-vivid-amber: #fcb900;
            --wp--preset--color--light-green-cyan: #7bdcb5;
            --wp--preset--color--vivid-green-cyan: #00d084;
            --wp--preset--color--pale-cyan-blue: #8ed1fc;
            --wp--preset--color--vivid-cyan-blue: #0693e3;
            --wp--preset--color--vivid-purple: #9b51e0;
            --wp--preset--gradient--vivid-cyan-blue-to-vivid-purple: linear-gradient(135deg, rgba(6, 147, 227, 1) 0%, rgb(155, 81, 224) 100%);
            --wp--preset--gradient--light-green-cyan-to-vivid-green-cyan: linear-gradient(135deg, rgb(122, 220, 180) 0%, rgb(0, 208, 130) 100%);
            --wp--preset--gradient--luminous-vivid-amber-to-luminous-vivid-orange: linear-gradient(135deg, rgba(252, 185, 0, 1) 0%, rgba(255, 105, 0, 1) 100%);
            --wp--preset--gradient--luminous-vivid-orange-to-vivid-red: linear-gradient(135deg, rgba(255, 105, 0, 1) 0%, rgb(207, 46, 46) 100%);
            --wp--preset--gradient--very-light-gray-to-cyan-bluish-gray: linear-gradient(135deg, rgb(238, 238, 238) 0%, rgb(169, 184, 195) 100%);
            --wp--preset--gradient--cool-to-warm-spectrum: linear-gradient(135deg, rgb(74, 234, 220) 0%, rgb(151, 120, 209) 20%, rgb(207, 42, 186) 40%, rgb(238, 44, 130) 60%, rgb(251, 105, 98) 80%, rgb(254, 248, 76) 100%);
            --wp--preset--gradient--blush-light-purple: linear-gradient(135deg, rgb(255, 206, 236) 0%, rgb(152, 150, 240) 100%);
            --wp--preset--gradient--blush-bordeaux: linear-gradient(135deg, rgb(254, 205, 165) 0%, rgb(254, 45, 45) 50%, rgb(107, 0, 62) 100%);
            --wp--preset--gradient--luminous-dusk: linear-gradient(135deg, rgb(255, 203, 112) 0%, rgb(199, 81, 192) 50%, rgb(65, 88, 208) 100%);
            --wp--preset--gradient--pale-ocean: linear-gradient(135deg, rgb(255, 245, 203) 0%, rgb(182, 227, 212) 50%, rgb(51, 167, 181) 100%);
            --wp--preset--gradient--electric-grass: linear-gradient(135deg, rgb(202, 248, 128) 0%, rgb(113, 206, 126) 100%);
            --wp--preset--gradient--midnight: linear-gradient(135deg, rgb(2, 3, 129) 0%, rgb(40, 116, 252) 100%);
            --wp--preset--font-size--small: 13px;
            --wp--preset--font-size--medium: 20px;
            --wp--preset--font-size--large: 36px;
            --wp--preset--font-size--x-large: 42px;
            --wp--preset--spacing--20: 0.44rem;
            --wp--preset--spacing--30: 0.67rem;
            --wp--preset--spacing--40: 1rem;
            --wp--preset--spacing--50: 1.5rem;
            --wp--preset--spacing--60: 2.25rem;
            --wp--preset--spacing--70: 3.38rem;
            --wp--preset--spacing--80: 5.06rem;
            --wp--preset--shadow--natural: 6px 6px 9px rgba(0, 0, 0, 0.2);
            --wp--preset--shadow--deep: 12px 12px 50px rgba(0, 0, 0, 0.4);
            --wp--preset--shadow--sharp: 6px 6px 0px rgba(0, 0, 0, 0.2);
            --wp--preset--shadow--outlined: 6px 6px 0px -3px rgba(255, 255, 255, 1), 6px 6px rgba(0, 0, 0, 1);
            --wp--preset--shadow--crisp: 6px 6px 0px rgba(0, 0, 0, 1);
        }

        :where(.is-layout-flex) {
            gap: 0.5em;
        }

        :where(.is-layout-grid) {
            gap: 0.5em;
        }

        body .is-layout-flex {
            display: flex;
        }

        .is-layout-flex {
            flex-wrap: wrap;
            align-items: center;
        }

        .is-layout-flex> :is(*, div) {
            margin: 0;
        }

        body .is-layout-grid {
            display: grid;
        }

        .is-layout-grid> :is(*, div) {
            margin: 0;
        }

        :where(.wp-block-columns.is-layout-flex) {
            gap: 2em;
        }

        :where(.wp-block-columns.is-layout-grid) {
            gap: 2em;
        }

        :where(.wp-block-post-template.is-layout-flex) {
            gap: 1.25em;
        }

        :where(.wp-block-post-template.is-layout-grid) {
            gap: 1.25em;
        }

        .has-black-color {
            color: var(--wp--preset--color--black) !important;
        }

        .has-cyan-bluish-gray-color {
            color: var(--wp--preset--color--cyan-bluish-gray) !important;
        }

        .has-white-color {
            color: var(--wp--preset--color--white) !important;
        }

        .has-pale-pink-color {
            color: var(--wp--preset--color--pale-pink) !important;
        }

        .has-vivid-red-color {
            color: var(--wp--preset--color--vivid-red) !important;
        }

        .has-luminous-vivid-orange-color {
            color: var(--wp--preset--color--luminous-vivid-orange) !important;
        }

        .has-luminous-vivid-amber-color {
            color: var(--wp--preset--color--luminous-vivid-amber) !important;
        }

        .has-light-green-cyan-color {
            color: var(--wp--preset--color--light-green-cyan) !important;
        }

        .has-vivid-green-cyan-color {
            color: var(--wp--preset--color--vivid-green-cyan) !important;
        }

        .has-pale-cyan-blue-color {
            color: var(--wp--preset--color--pale-cyan-blue) !important;
        }

        .has-vivid-cyan-blue-color {
            color: var(--wp--preset--color--vivid-cyan-blue) !important;
        }

        .has-vivid-purple-color {
            color: var(--wp--preset--color--vivid-purple) !important;
        }

        .has-black-background-color {
            background-color: var(--wp--preset--color--black) !important;
        }

        .has-cyan-bluish-gray-background-color {
            background-color: var(--wp--preset--color--cyan-bluish-gray) !important;
        }

        .has-white-background-color {
            background-color: var(--wp--preset--color--white) !important;
        }

        .has-pale-pink-background-color {
            background-color: var(--wp--preset--color--pale-pink) !important;
        }

        .has-vivid-red-background-color {
            background-color: var(--wp--preset--color--vivid-red) !important;
        }

        .has-luminous-vivid-orange-background-color {
            background-color: var(--wp--preset--color--luminous-vivid-orange) !important;
        }

        .has-luminous-vivid-amber-background-color {
            background-color: var(--wp--preset--color--luminous-vivid-amber) !important;
        }

        .has-light-green-cyan-background-color {
            background-color: var(--wp--preset--color--light-green-cyan) !important;
        }

        .has-vivid-green-cyan-background-color {
            background-color: var(--wp--preset--color--vivid-green-cyan) !important;
        }

        .has-pale-cyan-blue-background-color {
            background-color: var(--wp--preset--color--pale-cyan-blue) !important;
        }

        .has-vivid-cyan-blue-background-color {
            background-color: var(--wp--preset--color--vivid-cyan-blue) !important;
        }

        .has-vivid-purple-background-color {
            background-color: var(--wp--preset--color--vivid-purple) !important;
        }

        .has-black-border-color {
            border-color: var(--wp--preset--color--black) !important;
        }

        .has-cyan-bluish-gray-border-color {
            border-color: var(--wp--preset--color--cyan-bluish-gray) !important;
        }

        .has-white-border-color {
            border-color: var(--wp--preset--color--white) !important;
        }

        .has-pale-pink-border-color {
            border-color: var(--wp--preset--color--pale-pink) !important;
        }

        .has-vivid-red-border-color {
            border-color: var(--wp--preset--color--vivid-red) !important;
        }

        .has-luminous-vivid-orange-border-color {
            border-color: var(--wp--preset--color--luminous-vivid-orange) !important;
        }

        .has-luminous-vivid-amber-border-color {
            border-color: var(--wp--preset--color--luminous-vivid-amber) !important;
        }

        .has-light-green-cyan-border-color {
            border-color: var(--wp--preset--color--light-green-cyan) !important;
        }

        .has-vivid-green-cyan-border-color {
            border-color: var(--wp--preset--color--vivid-green-cyan) !important;
        }

        .has-pale-cyan-blue-border-color {
            border-color: var(--wp--preset--color--pale-cyan-blue) !important;
        }

        .has-vivid-cyan-blue-border-color {
            border-color: var(--wp--preset--color--vivid-cyan-blue) !important;
        }

        .has-vivid-purple-border-color {
            border-color: var(--wp--preset--color--vivid-purple) !important;
        }

        .has-vivid-cyan-blue-to-vivid-purple-gradient-background {
            background: var(--wp--preset--gradient--vivid-cyan-blue-to-vivid-purple) !important;
        }

        .has-light-green-cyan-to-vivid-green-cyan-gradient-background {
            background: var(--wp--preset--gradient--light-green-cyan-to-vivid-green-cyan) !important;
        }

        .has-luminous-vivid-amber-to-luminous-vivid-orange-gradient-background {
            background: var(--wp--preset--gradient--luminous-vivid-amber-to-luminous-vivid-orange) !important;
        }

        .has-luminous-vivid-orange-to-vivid-red-gradient-background {
            background: var(--wp--preset--gradient--luminous-vivid-orange-to-vivid-red) !important;
        }

        .has-very-light-gray-to-cyan-bluish-gray-gradient-background {
            background: var(--wp--preset--gradient--very-light-gray-to-cyan-bluish-gray) !important;
        }

        .has-cool-to-warm-spectrum-gradient-background {
            background: var(--wp--preset--gradient--cool-to-warm-spectrum) !important;
        }

        .has-blush-light-purple-gradient-background {
            background: var(--wp--preset--gradient--blush-light-purple) !important;
        }

        .has-blush-bordeaux-gradient-background {
            background: var(--wp--preset--gradient--blush-bordeaux) !important;
        }

        .has-luminous-dusk-gradient-background {
            background: var(--wp--preset--gradient--luminous-dusk) !important;
        }

        .has-pale-ocean-gradient-background {
            background: var(--wp--preset--gradient--pale-ocean) !important;
        }

        .has-electric-grass-gradient-background {
            background: var(--wp--preset--gradient--electric-grass) !important;
        }

        .has-midnight-gradient-background {
            background: var(--wp--preset--gradient--midnight) !important;
        }

        .has-small-font-size {
            font-size: var(--wp--preset--font-size--small) !important;
        }

        .has-medium-font-size {
            font-size: var(--wp--preset--font-size--medium) !important;
        }

        .has-large-font-size {
            font-size: var(--wp--preset--font-size--large) !important;
        }

        .has-x-large-font-size {
            font-size: var(--wp--preset--font-size--x-large) !important;
        }

        :where(.wp-block-post-template.is-layout-flex) {
            gap: 1.25em;
        }

        :where(.wp-block-post-template.is-layout-grid) {
            gap: 1.25em;
        }

        :where(.wp-block-columns.is-layout-flex) {
            gap: 2em;
        }

        :where(.wp-block-columns.is-layout-grid) {
            gap: 2em;
        }

        :root :where(.wp-block-pullquote) {
            font-size: 1.5em;
            line-height: 1.6;
        }
    </style>
    <link rel='stylesheet' id='fontello-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/pixelwars-core/themes/global/css/fonts/fontello/css/fontello.css'
        type='text/css' media='all' />
    <link rel='stylesheet' id='pixelwars-core-shortcodes-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/pixelwars-core/themes/global/css/shortcodes.css'
        type='text/css' media='all' />
    <link rel='stylesheet' id='qi-addons-for-elementor-grid-style-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/qi-addons-for-elementor/assets/css/grid.min.css?ver=1.8.9'
        type='text/css' media='all' />
    <link rel='stylesheet' id='qi-addons-for-elementor-helper-parts-style-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/qi-addons-for-elementor/assets/css/helper-parts.min.css?ver=1.8.9'
        type='text/css' media='all' />
    <link rel='stylesheet' id='qi-addons-for-elementor-style-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/qi-addons-for-elementor/assets/css/main.min.css?ver=1.8.9'
        type='text/css' media='all' />
    <link rel='stylesheet' id='interique-font-texgyreadventor-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/css/fonts/texgyreadventor/stylesheet.css?ver=6.8.1'
        type='text/css' media='all' />
    <link rel='stylesheet' id='interique-font-now-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/css/fonts/now/stylesheet.css?ver=6.8.1'
        type='text/css' media='all' />
    <link rel='stylesheet' id='interique-fonts-css'
        href='//fonts.googleapis.com/css?family=Jost%3A100%2C100i%2C200%2C200i%2C300%2C300i%2C400%2C400i%2C500%2C500i%2C600%2C600i%2C700%2C700i%2C800%2C800i%2C900%2C900i%7CBricolage+Grotesque%3A100%2C100i%2C200%2C200i%2C300%2C300i%2C400%2C400i%2C500%2C500i%2C600%2C600i%2C700%2C700i%2C800%2C800i%2C900%2C900i&#038;ver=6.8.1'
        type='text/css' media='all' />
    <link rel='stylesheet' id='normalize-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/css/normalize.css?ver=6.8.1'
        type='text/css' media='all' />
    <link rel='stylesheet' id='bootstrap-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/css/bootstrap.css?ver=6.8.1'
        type='text/css' media='all' />
    <link rel='stylesheet' id='fluidbox-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/js/fluidbox/fluidbox.css?ver=6.8.1'
        type='text/css' media='all' />
    <link rel='stylesheet' id='magnific-popup-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/js/jquery.magnific-popup/magnific-popup.css?ver=6.8.1'
        type='text/css' media='all' />
    <link rel='stylesheet' id='owl-carousel-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/js/owl-carousel/owl.carousel.css?ver=6.8.1'
        type='text/css' media='all' />
    <link rel='stylesheet' id='interique-main-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/css/main.css?ver=6.8.1'
        type='text/css' media='all' />
    <link rel='stylesheet' id='interique-768-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/css/768.css?ver=6.8.1'
        type='text/css' media='all' />
    <link rel='stylesheet' id='interique-992-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/css/992.css?ver=6.8.1'
        type='text/css' media='all' />
    <link rel='stylesheet' id='interique-style-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/style.css?ver=6.8.1'
        type='text/css' media='all' />
    <style id='interique-style-inline-css' type='text/css'>
        .site-title {
            font-family: 'TeXGyreAdventor', sans-serif;
        }

        .nav-menu,
        .entry-meta,
        .owl-nav,
        label,
        .page-links,
        .navigation,
        .entry-title i,
        .site-info,
        .filters {
            font-family: 'Jost';
        }

        .widget-title {
            font-family: 'TeXGyreAdventor', sans-serif;
        }

        h1,
        .entry-title,
        .footer-subscribe h3,
        .widget_categories ul li,
        .widget_recent_entries ul li a,
        .widget_pages ul li,
        .widget_nav_menu ul li,
        .widget_archive ul li,
        .widget_most_recommended_posts ul li a,
        .widget_calendar table caption,
        .tptn_title,
        .nav-single a,
        .widget_recent_comments ul li,
        .widget_product_categories ul li,
        .widget_meta ul li,
        .widget_rss ul a.rsswidget {
            font-family: 'Bricolage Grotesque';
        }

        h2,
        h3,
        h4,
        h5,
        h6,
        blockquote,
        .tab-titles {
            font-family: 'Jost';
        }

        .slider-box .entry-title {
            font-family: 'Now', sans-serif;
        }

        body {
            font-family: 'Jost';
        }

        .link-box .entry-title {
            font-family: 'Now', sans-serif;
        }

        .button,
        button,
        html .elementor-button,
        html .ekit-wid-con .elementskit-btn,
        html .ekit-wid-con .ekit_creative_button,
        .more-link {
            font-family: 'Jost';
        }

        .top-bar {
            font-family: 'Jost';
        }

        @media screen and (min-width: 992px) {
            .site-header .site-title {
                font-size: 24px;
            }
        }

        @media screen and (min-width: 992px) {
            .is-header-smaller .site-header.clone .site-title {
                font-size: 24px;
            }
        }

        @media screen and (max-width: 991px) {
            .site-header .site-title {
                font-size: 14px;
            }
        }

        @media screen and (min-width: 992px) {
            .blog-small .entry-title {
                font-size: 24px;
            }
        }

        @media screen and (min-width: 992px) {
            h1 {
                font-size: 102px;
            }
        }

        @media screen and (min-width: 992px) {
            html {
                font-size: 16px;
            }
        }

        @media screen and (max-width: 991px) {
            html {
                font-size: 15px;
            }
        }

        @media screen and (min-width: 992px) {
            .nav-menu>ul {
                font-size: 13px;
            }
        }

        @media screen and (min-width: 992px) {
            .blog-stream .entry-content {
                font-size: 16px;
            }
        }

        @media screen and (min-width: 992px) {
            .blog-stream.blog-small .entry-content {
                font-size: 15px;
            }
        }

        .widget-title {
            font-size: 13px;
        }

        @media screen and (min-width: 992px) {
            .nav-menu ul ul {
                font-size: 12px;
            }
        }

        .top-bar {
            font-size: 12px;
        }

        .site-footer .site-info {
            font-size: 12px;
        }

        .site-title {
            font-weight: 400;
        }

        h1,
        .entry-title,
        .footer-subscribe h3 {
            font-weight: 500;
        }

        h2,
        h3,
        h4,
        h5,
        h6,
        blockquote,
        .comment-meta .fn {
            font-weight: 500;
        }

        .slider-box .entry-title {
            font-weight: 700;
        }

        .widget-title {
            font-weight: 700;
        }

        @media screen and (min-width: 992px) {
            .nav-menu>ul {
                font-weight: 500;
            }
        }

        @media screen and (min-width: 992px) {
            .nav-menu ul ul {
                font-weight: 400;
            }
        }

        .link-box .entry-title {
            font-weight: 700;
        }

        .site-description {
            font-weight: 400;
        }

        .top-bar {
            font-weight: 500;
        }

        .site-footer .site-info {
            font-weight: 500;
        }

        .entry-meta {
            font-weight: 500;
        }

        @media screen and (min-width: 992px) {
            .nav-menu>ul {
                letter-spacing: 0px;
            }
        }

        @media screen and (min-width: 992px) {
            .nav-menu ul ul {
                letter-spacing: 0px;
            }
        }

        .widget-title {
            letter-spacing: 3px;
        }

        .site-footer .site-info {
            letter-spacing: 3px;
        }

        h1,
        .entry-title,
        .footer-subscribe h3,
        .widget_categories ul li,
        .widget_recent_entries ul li,
        .widget_pages ul li,
        .widget_archive ul li,
        .widget_calendar table caption,
        .tptn_title,
        .nav-single a {
            text-transform: none;
        }

        h2,
        h3,
        h4,
        h5,
        h6,
        blockquote,
        .comment-meta .fn {
            text-transform: none;
        }

        @media screen and (min-width: 992px) {
            html {
                line-height: 1.6;
            }
        }

        @media screen and (min-width: 992px) {
            .header-bg-shape {
                height: 50px;
            }
        }

        .header-wrap:after {
            bottom: -1px;
        }

        @media screen and (min-width: 992px) {
            .site-title img {
                max-height: 44px;
            }
        }

        @media screen and (max-width: 991px) {
            .site-title img {
                max-height: 24px;
            }
        }

        .top-bar {
            line-height: 36px;
        }

        html .site-header .site-title a {
            padding: 12px 24px;
        }

        @media screen and (min-width: 992px) {
            .site {
                margin-top: 0px;
                margin-bottom: 0px;
            }
        }

        .layout-medium,
        .is-header-row .header-wrap-inner,
        .is-header-small .header-wrap-inner,
        .is-menu-bar.is-menu-fixed-bg .menu-wrap,
        .is-header-fixed-width .header-wrap,
        .is-header-fixed-width.is-menu-bar .site-navigation,
        .is-header-float-box:not(.is-header-float-box-menu) .site-header:not(.clone) .header-wrap,
        .is-header-float-box.is-menu-bar .site-header:not(.clone) .site-navigation:not(.clone),
        .is-body-boxed .site,
        .is-body-boxed .header-wrap,
        .is-body-boxed.is-menu-bar .site-navigation,
        .is-body-boxed:not(.is-menu-bar) .site-header,
        .is-middle-boxed .site-main,
        .intro-content,
        .is-footer-boxed .site-footer,
        .is-content-boxed .site-main .layout-fixed,
        .top-bar .top-bar-wrap,
        .is-top-bar-fixed .top-bar,
        .is-top-bar-fixed-bg .top-bar,
        .is-menu-bottom.is-menu-bottom-overflow .site-header:not(.clone) .site-navigation:not(.clone) .menu-wrap,
        .site-branding-wrap,
        .is-header-border-fixed .header-wrap:after,
        .is-header-border-fixed .menu-wrap:after,
        html .tutor-container,
        html .lp-content-area,
        html .learn-press-breadcrumb {
            max-width: 1140px;
            margin-left: auto;
            margin-right: auto;
        }

        .layout-fixed,
        .blog-list,
        .blog-regular,
        .is-content-boxed .single .site-content,
        .is-content-boxed .page .site-content {
            max-width: 800px;
        }

        @media screen and (min-width: 992px) {

            .is-header-small .header-wrap,
            .is-menu-bar .nav-menu>ul>li,
            .is-header-vertical .nav-menu>ul>li {
                line-height: 80px;
            }

            .is-header-small .site-branding {
                max-height: 80px;
            }

        }

        @media screen and (min-width: 992px) {

            .is-header-small.is-header-smaller .site-header.clone .header-wrap,
            .is-header-row.is-header-smaller .site-header.clone .nav-menu>ul>li,
            .is-menu-bar.is-header-smaller .site-navigation.clone .nav-menu>ul>li,
            .is-menu-bar.is-header-smaller .site-header.clone .site-navigation .nav-menu>ul>li {
                line-height: 70px;
            }

            .is-header-small.is-header-smaller .site-header.clone .site-branding {
                max-height: 70px;
            }

        }

        .button.is-primary,
        .button.is-primary:after,
        html .elementor-button,
        .elementor-button.elementor-size-xs,
        .elementor-button.elementor-size-sm,
        .elementor-button.elementor-size-md,
        .elementor-button.elementor-size-lg,
        .elementor-button.elementor-size-xl,
        html .ekit-wid-con .elementskit-btn,
        html .ekit-wid-con .ekit_creative_button {
            border-radius: 0px;
        }

        .button.is-secondary,
        .button.is-secondary:after,
        .elementor-element.elementor-button-info .elementor-button {
            border-radius: 30px;
        }

        a {
            color: #d84156;
        }

        a:hover {
            color: #c60035;
        }

        .site-header .header-wrap {
            background-color: #fffefc;
        }

        html:not(.is-menu-bottom) .site-header .header-bg-shape {
            color: #fffefc;
        }

        .header-wrap:before {
            background: #fffefc;
        }

        .header-wrap:before {
            opacity: 0;
        }

        .is-header-half-transparent:not(.is-menu-toggled-on) .site-header:not(.clone) .header-wrap:before {
            opacity: 0.45;
        }

        .header-wrap:after {
            opacity: 0.08;
        }

        .site-header .menu-wrap {
            background-color: #ffffff;
        }

        html.is-menu-bottom .site-header .header-bg-shape {
            color: #ffffff;
        }

        @media screen and (min-width: 992px) {
            .nav-menu>ul>li.current-menu-item>a {
                color: #0a0a0a !important;
            }
        }

        @media screen and (min-width: 992px) {
            .nav-menu li.current-menu-item>a .link-text:before {
                background-color: #bbcfbd !important;
                border-color: #bbcfbd !important;
            }
        }

        @media screen and (min-width: 992px) {

            html .nav-menu>ul>li>a:hover,
            .nav-menu>ul>li.has-submenu:hover>a {
                color: #1c1a17;
            }
        }

        @media screen and (min-width: 992px) {

            html.loaded .nav-menu ul li a .link-text:before,
            .nav-menu li.has-submenu:hover>a .link-text:before {
                background-color: #d6cfc2;
                border-color: #d6cfc2;
            }
        }

        @media screen and (min-width: 992px) {
            .nav-menu ul ul li.current-menu-item>a .link-text {
                color: #0a0606 !important;
            }
        }

        @media screen and (min-width: 992px) {

            html .nav-menu ul ul li a:hover .link-text,
            .nav-menu ul ul li.has-submenu:hover>a .link-text {
                color: #111111;
            }
        }

        h1,
        h2,
        h3,
        h4,
        h5,
        h6,
        blockquote,
        .tab-titles {
            color: #020014;
        }

        body {
            color: #32455e;
        }

        body {
            background: #fffefc;
        }

        .site .footer-subscribe {
            background: #ebe6da;
        }

        .site-footer .site-info {
            background-color: #ffffff;
        }

        .site-footer .site-info {
            color: #0a0a0a;
        }

        .button.is-primary {
            color: #222222;
        }

        .button.is-primary.is-shadow,
        .button.is-primary.is-solid,
        .button.is-primary.is-solid-light,
        html .elementor-button,
        html .ekit-wid-con .elementskit-btn {
            background-color: #222222;
        }

        .button.is-primary.is-shadow {
            box-shadow: 0px 18px 23px -6px #222222;
        }

        .button.is-primary:hover {
            color: #215aed;
        }

        .button.is-primary.is-shadow:hover,
        .button.is-primary.is-solid:hover,
        .button.is-primary.is-solid-light:hover,
        .button.is-primary.is-shift:after,
        .button.is-primary.is-circle:before,
        html .elementor-button:hover,
        html .ekit-wid-con .elementskit-btn:hover {
            background-color: #215aed;
        }

        .button.is-primary.is-shadow:hover {
            box-shadow: 0px 2px 10px -5px #215aed;
        }

        .button.is-secondary {
            color: #f22000;
        }

        .button.is-secondary.is-shadow,
        .button.is-secondary.is-solid,
        .button.is-secondary.is-solid-light,
        .elementor-element.elementor-button-info .elementor-button {
            background-color: #f22000;
        }

        .button.is-secondary.is-shadow {
            box-shadow: 0px 18px 23px -6px #f22000;
        }

        .button.is-secondary:hover {
            color: #0026ff;
        }

        .button.is-secondary.is-shadow:hover,
        .button.is-secondary.is-solid:hover,
        .button.is-secondary.is-solid-light:hover,
        .button.is-secondary.is-shift:after,
        .button.is-secondary.is-circle:before,
        .elementor-element.elementor-button-info .elementor-button:hover {
            background-color: #0026ff;
        }

        .button.is-secondary.is-shadow:hover {
            box-shadow: 0px 2px 10px -5px #0026ff;
        }

        .top-bar,
        .top-bar select option {
            background-color: #262251;
        }

        .top-bar {
            background: linear-gradient(90deg, #262251 30%, #d10600 100%);
        }

        .header-wrap:after {
            color: #222222;
        }

        .is-menu-bar .menu-wrap:after {
            color: #222222;
        }
    </style>
    <link rel='stylesheet' id='swiper-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/qi-addons-for-elementor/assets/plugins/swiper/8.4.5/swiper.min.css?ver=8.4.5'
        type='text/css' media='all' />
    <link rel='stylesheet' id='bdt-uikit-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/bdthemes-prime-slider-lite/assets/css/bdt-uikit.css?ver=3.21.7'
        type='text/css' media='all' />
    <link rel='stylesheet' id='prime-slider-site-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/bdthemes-prime-slider-lite/assets/css/prime-slider-site.css?ver=3.17.12'
        type='text/css' media='all' />
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-includes/js/jquery/jquery.min.js?ver=3.7.1"
        id="jquery-core-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-includes/js/jquery/jquery-migrate.min.js?ver=3.4.1"
        id="jquery-migrate-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/bdthemes-prime-slider-lite/assets/js/bdt-uikit.min.js?ver=3.21.7"
        id="bdt-uikit-js"></script>
    <link rel="https://api.w.org/" href="https://themes.pixelwars.org/interique/demo-01/wp-json/" />
    <link rel="alternate" title="JSON" type="application/json"
        href="https://themes.pixelwars.org/interique/demo-01/wp-json/wp/v2/pages/15001" />
    <link rel="EditURI" type="application/rsd+xml" title="RSD"
        href="https://themes.pixelwars.org/interique/demo-01/xmlrpc.php?rsd" />
    <meta name="generator" content="WordPress 6.8.1" />
    <link rel="canonical" href="portfolio.html" />
    <link rel='shortlink' href='https://themes.pixelwars.org/interique/demo-01/?p=15001' />
    <link rel="alternate" title="oEmbed (JSON)" type="application/json+oembed"
        href="https://themes.pixelwars.org/interique/demo-01/wp-json/oembed/1.0/embed?url=https%3A%2F%2Fthemes.pixelwars.org%2Finterique%2Fdemo-01%2Fportfolio%2F" />
    <link rel="alternate" title="oEmbed (XML)" type="text/xml+oembed"
        href="https://themes.pixelwars.org/interique/demo-01/wp-json/oembed/1.0/embed?url=https%3A%2F%2Fthemes.pixelwars.org%2Finterique%2Fdemo-01%2Fportfolio%2F&#038;format=xml" />
    <meta name="generator"
        content="Elementor 3.29.0; features: e_font_icon_svg, additional_custom_breakpoints, e_local_google_fonts, e_element_cache; settings: css_print_method-external, google_font-enabled, font_display-swap">
    <style>
        .e-con.e-parent:nth-of-type(n+4):not(.e-lazyloaded):not(.e-no-lazyload),
        .e-con.e-parent:nth-of-type(n+4):not(.e-lazyloaded):not(.e-no-lazyload) * {
            background-image: none !important;
        }

        @media screen and (max-height: 1024px) {

            .e-con.e-parent:nth-of-type(n+3):not(.e-lazyloaded):not(.e-no-lazyload),
            .e-con.e-parent:nth-of-type(n+3):not(.e-lazyloaded):not(.e-no-lazyload) * {
                background-image: none !important;
            }
        }

        @media screen and (max-height: 640px) {

            .e-con.e-parent:nth-of-type(n+2):not(.e-lazyloaded):not(.e-no-lazyload),
            .e-con.e-parent:nth-of-type(n+2):not(.e-lazyloaded):not(.e-no-lazyload) * {
                background-image: none !important;
            }
        }
    </style>
</head>

<body
    class="wp-singular page-template page-template-page_template-portfolio page-template-page_template-portfolio-php page page-id-15001 wp-theme-interique qodef-qi--no-touch qi-addons-for-elementor-1.8.9 elementor-default elementor-kit-6">
    <div id="page" class="hfeed site">
        <header id="masthead" class="site-header" role="banner">
            <div class="header-wrap" data-parallax-video="">
                <div class="header-wrap-inner">
                    <div class="site-branding">
                        <div class="site-branding-wrap">
                            <div class="site-branding-left">
                            </div> <!-- .site-branding-left -->

                            <div class="site-branding-center">
                                <h1 class="site-title">
                                    <a href="index.html" rel="home">
                                        <span class="screen-reader-text">
                                            Motiff Square </span>
                                        <span class="site-title-text">
                                            Motiff Square </span>
                                    </a>
                                </h1> <!-- .site-title -->
                                <p class="site-description">
                                </p> <!-- .site-description -->
                            </div> <!-- .site-branding-center -->
                            <div class="site-branding-right">
                            </div> <!-- .site-branding-right -->
                        </div> <!-- .site-branding-wrap -->
                    </div> <!-- .site-branding -->

                    <nav id="site-navigation" class="main-navigation site-navigation" role="navigation">
                        <div class="menu-wrap">
                            <div class="layout-medium">
                                <a class="menu-toggle">
                                    <span class="lines"></span>
                                </a> <!-- .menu-toggle -->
                                <div class="nav-menu">
                                    <ul id="menu-mymenu" class="">
                                        <li id="menu-item-15712"
                                            class="menu-item menu-item-type-post_type menu-item-object-page menu-item-home menu-item-has-children menu-item-15712">
                                            <a href="index.html">Home</a>
                                            <!-- <ul class="sub-menu">
                                                <li id="menu-item-18584"
                                                    class="menu-item menu-item-type-post_type menu-item-object-page menu-item-home menu-item-18584">
                                                    <a href="index.html">Home
                                                        01</a></li>
                                                <li id="menu-item-18345"
                                                    class="menu-item menu-item-type-post_type menu-item-object-page menu-item-18345">
                                                    <a href="https://themes.pixelwars.org/interique/demo-01/home-02/">Home
                                                        02</a></li>
                                                <li id="menu-item-18342"
                                                    class="menu-item menu-item-type-post_type menu-item-object-page menu-item-18342">
                                                    <a href="https://themes.pixelwars.org/interique/demo-01/home-03/">Home
                                                        03</a></li>
                                                <li id="menu-item-18343"
                                                    class="menu-item menu-item-type-post_type menu-item-object-page menu-item-18343">
                                                    <a href="https://themes.pixelwars.org/interique/demo-01/home-04/">Home
                                                        04</a></li>
                                                <li id="menu-item-18344"
                                                    class="menu-item menu-item-type-post_type menu-item-object-page menu-item-18344">
                                                    <a href="https://themes.pixelwars.org/interique/demo-01/home-05/">Home
                                                        05</a></li>
                                            </ul> -->
                                        </li>
                                        <li id="menu-item-18246"
                                            class="menu-item menu-item-type-post_type menu-item-object-page menu-item-18246">
                                            <a href="about.html">About Us</a></li>
                                        <li id="menu-item-18245"
                                            class="menu-item menu-item-type-post_type menu-item-object-page menu-item-18245">
                                            <a href="service.html">Services</a></li>
                                        <li id="menu-item-15714"
                                            class="menu-item menu-item-type-post_type menu-item-object-page current-menu-item page_item page-item-15001 current_page_item menu-item-15714">
                                            <a href="portfolio.html"
                                                aria-current="page">Portfolio</a></li>
                                        <li id="menu-item-18244"
                                            class="menu-item menu-item-type-post_type menu-item-object-page menu-item-18244">
                                            <a
                                                href="contact.html">Contact</a>
                                        </li>
                                    </ul>
                                </div> <a class="search-toggle toggle-link"></a>

                                <div class="search-container">
                                    <div class="search-box">
                                        <form class="search-form" method="get"
                                            action="index.html">
                                            <label>
                                                <span>
                                                    Search for </span>
                                                <input type="search" id="search-field" name="s"
                                                    placeholder="type and hit enter">
                                            </label>
                                            <input type="submit" class="search-submit" value="Search">
                                        </form> <!-- .search-form -->
                                    </div> <!-- .search-box -->
                                </div> <!-- .search-container -->
                                <div class="social-container widget-area">
                                    <a class="social-link instagram" target="_blank" href="#"></a>
                                    <a class="social-link twitter" target="_blank" href="#"></a>
                                </div> <!-- .social-container -->
                            </div> <!-- .layout-medium -->
                        </div> <!-- .menu-wrap -->
                    </nav> <!-- #site-navigation .main-navigation .site-navigation -->
                </div> <!-- .header-wrap-inner -->
            </div> <!-- .header-wrap -->
        </header> <!-- #masthead .site-header -->

        <div id="main" class="site-main">
            <div class="layout-medium">
                <div id="primary" class="content-area ">
                    <div id="content" class="site-content" role="main">
                        <div class="post-header post-header-classic portfolio-header">
                            <header class="entry-header">
                                <h1 class="entry-title">
                                    Portfolio </h1> <!-- .entry-title -->
                            </header> <!-- .entry-header -->
                        </div> <!-- .post-header .post-header-classic .portfolio-header -->

                        <ul id="filters" class="filters">
                            <li class="current">
                                <a data-filter="*" href="#">
                                    all </a>
                            </li>
                            <li>
                                <a data-filter=".furniture-selection"
                                    href="https://themes.pixelwars.org/interique/demo-01/portfolio-category/furniture-selection/">
                                    Furniture Selection </a>
                            </li>
                            <li>
                                <a data-filter=".residential-design"
                                    href="https://themes.pixelwars.org/interique/demo-01/portfolio-category/residential-design/">
                                    Residential Design </a>
                            </li>
                            <li>
                                <a data-filter=".space-planning"
                                    href="https://themes.pixelwars.org/interique/demo-01/portfolio-category/space-planning/">
                                    Space Planning </a>
                            </li>
                        </ul> <!-- #filters .filters -->



                        <div class="blog-grid-wrap">
                            <div class="blog-stream blog-grid blog-small portfolio-grid masonry" data-layout="fitRows"
                                data-item-width="380">








                                <div id="post-17737"
                                    class="post-17737 portfolio type-portfolio status-publish format-standard has-post-thumbnail hentry ">
                                    <div class="hentry-wrap">
                                        <div class="featured-image">
                                            <a
                                                href="portfolio.htmldynamic-design/">
                                                <img fetchpriority="high" width="550" height="362"
                                                    src="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/Modern-Interior-with-Plush-White-Chair-550x362.jpeg"
                                                    class="attachment-interique_image_size_4 size-interique_image_size_4 wp-post-image"
                                                    alt="" decoding="async" /> </a>
                                        </div> <!-- .featured-image -->
                                        <div class="hentry-middle">
                                            <header class="entry-header">
                                                <h2 class="entry-title">
                                                    <a
                                                        href="portfolio.htmldynamic-design/">
                                                        Dynamic Design </a>
                                                </h2> <!-- .entry-title -->
                                            </header> <!-- .entry-header -->
                                        </div> <!-- .hentry-middle -->
                                    </div> <!-- .hentry-wrap -->
                                </div> <!-- .hentry -->
                                <div id="post-10458"
                                    class="post-10458 portfolio type-portfolio status-publish format-standard has-post-thumbnail hentry portfolio-category-residential-design residential-design ">
                                    <div class="hentry-wrap">
                                        <div class="featured-image">
                                            <a
                                                href="portfolio.htmlminimal-oasis/">
                                                <img width="550" height="362"
                                                    src="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/Modern-Translucent-Green-Chair-550x362.jpeg"
                                                    class="attachment-interique_image_size_4 size-interique_image_size_4 wp-post-image"
                                                    alt="" decoding="async" /> </a>
                                        </div> <!-- .featured-image -->
                                        <div class="hentry-middle">
                                            <header class="entry-header">
                                                <h2 class="entry-title">
                                                    <a
                                                        href="portfolio.htmlminimal-oasis/">
                                                        Minimal Oasis </a>
                                                </h2> <!-- .entry-title -->
                                            </header> <!-- .entry-header -->
                                        </div> <!-- .hentry-middle -->
                                    </div> <!-- .hentry-wrap -->
                                </div> <!-- .hentry -->
                                <div id="post-10456"
                                    class="post-10456 portfolio type-portfolio status-publish format-standard has-post-thumbnail hentry portfolio-category-space-planning space-planning ">
                                    <div class="hentry-wrap">
                                        <div class="featured-image">
                                            <a
                                                href="portfolio.htmltimeless-elegance/">
                                                <img width="550" height="362"
                                                    src="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/Modern-Sage-Armchair-2-550x362.jpeg"
                                                    class="attachment-interique_image_size_4 size-interique_image_size_4 wp-post-image"
                                                    alt="" decoding="async" /> </a>
                                        </div> <!-- .featured-image -->
                                        <div class="hentry-middle">
                                            <header class="entry-header">
                                                <h2 class="entry-title">
                                                    <a
                                                        href="portfolio.htmltimeless-elegance/">
                                                        Timeless Elegance </a>
                                                </h2> <!-- .entry-title -->
                                            </header> <!-- .entry-header -->
                                        </div> <!-- .hentry-middle -->
                                    </div> <!-- .hentry-wrap -->
                                </div> <!-- .hentry -->
                                <div id="post-10454"
                                    class="post-10454 portfolio type-portfolio status-publish format-standard has-post-thumbnail hentry portfolio-category-furniture-selection furniture-selection ">
                                    <div class="hentry-wrap">
                                        <div class="featured-image">
                                            <a
                                                href="portfolio.htmlbold-sanctuary/">
                                                <img loading="lazy" width="550" height="362"
                                                    src="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/living-room-mid-century-style-with-warm-colors-ai-generative-1-550x362.jpg"
                                                    class="attachment-interique_image_size_4 size-interique_image_size_4 wp-post-image"
                                                    alt="" decoding="async" /> </a>
                                        </div> <!-- .featured-image -->
                                        <div class="hentry-middle">
                                            <header class="entry-header">
                                                <h2 class="entry-title">
                                                    <a
                                                        href="portfolio.htmlbold-sanctuary/">
                                                        Bold Sanctuary </a>
                                                </h2> <!-- .entry-title -->
                                            </header> <!-- .entry-header -->
                                        </div> <!-- .hentry-middle -->
                                    </div> <!-- .hentry-wrap -->
                                </div> <!-- .hentry -->
                                <div id="post-10452"
                                    class="post-10452 portfolio type-portfolio status-publish format-standard has-post-thumbnail hentry portfolio-category-residential-design residential-design ">
                                    <div class="hentry-wrap">
                                        <div class="featured-image">
                                            <a
                                                href="portfolio.htmlelegant-escape/">
                                                <img loading="lazy" width="550" height="362"
                                                    src="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/Bold-and-Graphic-Interior-550x362.jpeg"
                                                    class="attachment-interique_image_size_4 size-interique_image_size_4 wp-post-image"
                                                    alt="" decoding="async" /> </a>
                                        </div> <!-- .featured-image -->
                                        <div class="hentry-middle">
                                            <header class="entry-header">
                                                <h2 class="entry-title">
                                                    <a
                                                        href="portfolio.htmlelegant-escape/">
                                                        Elegant Escape </a>
                                                </h2> <!-- .entry-title -->
                                            </header> <!-- .entry-header -->
                                        </div> <!-- .hentry-middle -->
                                    </div> <!-- .hentry-wrap -->
                                </div> <!-- .hentry -->
                                <div id="post-10449"
                                    class="post-10449 portfolio type-portfolio status-publish format-standard has-post-thumbnail hentry portfolio-category-furniture-selection furniture-selection ">
                                    <div class="hentry-wrap">
                                        <div class="featured-image">
                                            <a
                                                href="portfolio.htmlsleek-harmony/">
                                                <img loading="lazy" width="550" height="362"
                                                    src="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/industrial-style-loft-apartment-with-indoor-balcony-3d-render-550x362.jpg"
                                                    class="attachment-interique_image_size_4 size-interique_image_size_4 wp-post-image"
                                                    alt="" decoding="async" /> </a>
                                        </div> <!-- .featured-image -->
                                        <div class="hentry-middle">
                                            <header class="entry-header">
                                                <h2 class="entry-title">
                                                    <a
                                                        href="portfolio.htmlsleek-harmony/">
                                                        Sleek Harmony </a>
                                                </h2> <!-- .entry-title -->
                                            </header> <!-- .entry-header -->
                                        </div> <!-- .hentry-middle -->
                                    </div> <!-- .hentry-wrap -->
                                </div> <!-- .hentry -->
                            </div> <!-- .blog-stream .blog-grid .blog-small .portfolio-grid .masonry -->
                        </div> <!-- .blog-grid-wrap -->

                    </div> <!-- #content .site-content -->
                </div> <!-- #primary .content-area -->

            </div> <!-- layout -->
        </div> <!-- #main .site-main -->



        <footer id="colophon" class="site-footer" role="contentinfo">
            <div class="site-info">

                <p>© 2025 Motiff Square </p>
            </div> <!-- .site-info -->
        </footer> <!-- #colophon .site-footer -->
    </div>
    <script type="speculationrules">
{"prefetch":[{"source":"document","where":{"and":[{"href_matches":"\/interique\/demo-01\/*"},{"not":{"href_matches":["\/interique\/demo-01\/wp-*.php","\/interique\/demo-01\/wp-admin\/*","\/interique\/demo-01\/wp-content\/uploads\/sites\/2\/*","\/interique\/demo-01\/wp-content\/*","\/interique\/demo-01\/wp-content\/plugins\/*","\/interique\/demo-01\/wp-content\/themes\/interique\/*","\/interique\/demo-01\/*\\?(.+)"]}},{"not":{"selector_matches":"a[rel~=\"nofollow\"]"}},{"not":{"selector_matches":".no-prefetch, .no-prefetch a"}}]},"eagerness":"conservative"}]}
</script>
    <script>
        const lazyloadRunObserver = () => {
            const lazyloadBackgrounds = document.querySelectorAll(`.e-con.e-parent:not(.e-lazyloaded)`);
            const lazyloadBackgroundObserver = new IntersectionObserver((entries) => {
                entries.forEach((entry) => {
                    if (entry.isIntersecting) {
                        let lazyloadBackground = entry.target;
                        if (lazyloadBackground) {
                            lazyloadBackground.classList.add('e-lazyloaded');
                        }
                        lazyloadBackgroundObserver.unobserve(entry.target);
                    }
                });
            }, { rootMargin: '200px 0px 200px 0px' });
            lazyloadBackgrounds.forEach((lazyloadBackground) => {
                lazyloadBackgroundObserver.observe(lazyloadBackground);
            });
        };
        const events = [
            'DOMContentLoaded',
            'elementor/lazyload/observe',
        ];
        events.forEach((event) => {
            document.addEventListener(event, lazyloadRunObserver);
        });
    </script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-includes/js/jquery/ui/core.min.js?ver=1.13.3"
        id="jquery-ui-core-js"></script>
    <script type="text/javascript" id="qi-addons-for-elementor-script-js-extra">
        /* <![CDATA[ */
        var qodefQiAddonsGlobal = { "vars": { "adminBarHeight": 0, "iconArrowLeft": "<svg  xmlns=\"http:\/\/www.w3.org\/2000\/svg\" x=\"0px\" y=\"0px\" viewBox=\"0 0 34.2 32.3\" xml:space=\"preserve\" style=\"stroke-width: 2;\"><line x1=\"0.5\" y1=\"16\" x2=\"33.5\" y2=\"16\"\/><line x1=\"0.3\" y1=\"16.5\" x2=\"16.2\" y2=\"0.7\"\/><line x1=\"0\" y1=\"15.4\" x2=\"16.2\" y2=\"31.6\"\/><\/svg>", "iconArrowRight": "<svg  xmlns=\"http:\/\/www.w3.org\/2000\/svg\" x=\"0px\" y=\"0px\" viewBox=\"0 0 34.2 32.3\" xml:space=\"preserve\" style=\"stroke-width: 2;\"><line x1=\"0\" y1=\"16\" x2=\"33\" y2=\"16\"\/><line x1=\"17.3\" y1=\"0.7\" x2=\"33.2\" y2=\"16.5\"\/><line x1=\"17.3\" y1=\"31.6\" x2=\"33.5\" y2=\"15.4\"\/><\/svg>", "iconClose": "<svg  xmlns=\"http:\/\/www.w3.org\/2000\/svg\" x=\"0px\" y=\"0px\" viewBox=\"0 0 9.1 9.1\" xml:space=\"preserve\"><g><path d=\"M8.5,0L9,0.6L5.1,4.5L9,8.5L8.5,9L4.5,5.1L0.6,9L0,8.5L4,4.5L0,0.6L0.6,0L4.5,4L8.5,0z\"\/><\/g><\/svg>" } };
        /* ]]> */
    </script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/qi-addons-for-elementor/assets/js/main.min.js?ver=1.8.9"
        id="qi-addons-for-elementor-script-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/js/jquery.fitvids.js"
        id="fitvids-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/js/jarallax.min.js"
        id="jarallax-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/js/jarallax-video.min.js"
        id="jarallax-video-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/js/fluidbox/jquery.fluidbox.min.js"
        id="fluidbox-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/js/jquery-validation/jquery.validate.js"
        id="jqueryvalidation-js"></script>
    <script type="text/javascript" id="jqueryvalidation-js-after">
        /* <![CDATA[ */
        (function ($) {
            "use strict";
            $.extend($.validator.messages, {
                required: "This field is required.",
                remote: "Please fix this field.",
                email: "Please enter a valid email address.",
                url: "Please enter a valid URL.",
                date: "Please enter a valid date.",
                dateISO: "Please enter a valid date ( ISO ).",
                number: "Please enter a valid number.",
                digits: "Please enter only digits.",
                equalTo: "Please enter the same value again.",
                maxlength: $.validator.format("Please enter no more than {0} characters."),
                minlength: $.validator.format("Please enter at least {0} characters."),
                rangelength: $.validator.format("Please enter a value between {0} and {1} characters long."),
                range: $.validator.format("Please enter a value between {0} and {1}."),
                max: $.validator.format("Please enter a value less than or equal to {0}."),
                min: $.validator.format("Please enter a value greater than or equal to {0}."),
                step: $.validator.format("Please enter a multiple of {0}.")
            });
        })(jQuery);
        /* ]]> */
    </script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/qi-addons-for-elementor/inc/masonry/assets/js/plugins/isotope.pkgd.min.js?ver=3.0.6"
        id="isotope-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/js/jquery.magnific-popup/jquery.magnific-popup.min.js"
        id="magnific-popup-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/js/owl-carousel/owl.carousel.min.js"
        id="owl-carousel-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-includes/js/imagesloaded.min.js?ver=5.0.0"
        id="imagesloaded-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/js/jquery.collagePlus.min.js"
        id="collagePlus-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/js/jquery.fittext.js"
        id="fittext-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/js/resize-sensor.js"
        id="resize-sensor-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/js/jquery.sticky-sidebar.min.js"
        id="sticky-sidebar-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/js/main.js"
        id="interique-main-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/pixelwars-core/themes/global/js/shortcodes.js"
        id="pixelwars-core-shortcodes-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/bdthemes-prime-slider-lite/assets/js/prime-slider-site.min.js?ver=3.17.12"
        id="prime-slider-site-js"></script>
</body>

</html>